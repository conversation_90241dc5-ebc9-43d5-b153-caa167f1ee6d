{% extends 'base.html' %}
{% load static %}

{% block title %}
  Admin Dashboard
{% endblock %}

{% block extra_head %}
  <!-- Resource hints for faster loading -->
  <link rel="preconnect" href="https://cdn.jsdelivr.net">
  <link rel="preload" href="https://cdn.jsdelivr.net/npm/chart.js" as="script">
  <link rel="preload" href="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2.0.0" as="script">

  <!-- Meta description for SEO -->
  <meta name="description" content="Admin dashboard for e-logbook system with performance metrics and analytics">

  <!-- Optimized styles for animations and better UX -->
  <style>
    /* Animations */
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(-10px); }
      to { opacity: 1; transform: translateY(0); }
    }

    @keyframes fadeOut {
      from { opacity: 1; transform: translateY(0); }
      to { opacity: 0; transform: translateY(-10px); }
    }

    @keyframes pulse {
      0% { transform: scale(1); }
      50% { transform: scale(1.05); }
      100% { transform: scale(1); }
    }

    @keyframes shimmer {
      0% { background-position: -1000px 0; }
      100% { background-position: 1000px 0; }
    }

    @keyframes loadingDots {
      0% { content: ""; }
      25% { content: "."; }
      50% { content: ".."; }
      75% { content: "..."; }
      100% { content: ""; }
    }

    .animate-fadeIn {
      animation: fadeIn 0.3s ease-out forwards;
    }

    .animate-fadeOut {
      animation: fadeOut 0.3s ease-in forwards;
    }

    .animate-pulse {
      animation: pulse 2s infinite;
    }

    .loading-shimmer {
      background: linear-gradient(to right, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.2) 20%, rgba(255,255,255,0.1) 40%);
      background-size: 1000px 100%;
      animation: shimmer 2s infinite linear;
    }

    .loading-dots::after {
      content: "";
      animation: loadingDots 1.5s infinite;
      display: inline-block;
      width: 24px; /* Adjust width as needed */
      text-align: left;
    }

    /* Accessibility improvements */
    @media (prefers-reduced-motion: reduce) {
      .animate-fadeIn, .animate-fadeOut, .animate-pulse, .loading-shimmer, .animate-spin {
        animation: none !important;
        transition: none !important;
      }
      .loading-dots::after {
        animation: none !important;
        content: "..."; /* Static content for reduced motion */
      }
    }

    /* Print styles */
    @media print {
      .no-print, .print\:hidden {
        display: none !important;
      }

      body {
        background: white !important;
        color: black !important;
        -webkit-print-color-adjust: exact !important; /* Chrome, Safari */
        print-color-adjust: exact !important; /* Firefox, Edge */
      }

      .print-break-inside-avoid {
        break-inside: avoid;
      }

      .print-full-width {
        width: 100% !important;
        max-width: 100% !important;
      }
      .shadow-lg, .shadow-md, .shadow-sm {
        box-shadow: none !important;
      }
      .border {
        border-width: 1px !important;
        border-color: #ccc !important;
      }
      .bg-gradient-to-r {
        background: none !important;
        background-color: #f0f0f0 !important; /* Light gray fallback */
      }
      canvas {
        max-width: 100% !important;
        height: auto !important;
      }
      .chart-loading {
        display: none !important; /* Hide loading indicators when printing */
      }
      .opacity-0 {
          opacity: 1 !important; /* Ensure charts are visible */
      }
    }
  </style>
{% endblock %}

{% block navbar %}
  {% include 'components/admin_auth_navbar.html' %}
{% endblock %}

{% block content %}
  <div class="px-4 sm:px-6 lg:px-8 py-8 transition-all duration-300 w-full mx-auto" x-data="{ darkMode: localStorage.getItem('darkMode') === 'true' }" :class="{ 'dark': darkMode }">
    <!-- Responsive Layout Indicator for Development -->
    <div class="fixed bottom-4 right-4 z-50 hidden">
      <div class="bg-blue-600 text-white text-xs px-2 py-1 rounded shadow-lg">
        <span class="sm:hidden">XS</span>
        <span class="hidden sm:inline md:hidden">SM</span>
        <span class="hidden md:inline lg:hidden">MD</span>
        <span class="hidden lg:inline xl:hidden">LG</span>
        <span class="hidden xl:inline 2xl:hidden">XL</span>
        <span class="hidden 2xl:inline">2XL</span>
      </div>
    </div>

    <!-- Dashboard Controls -->
    <div class="print:hidden mb-4 flex justify-end space-x-3">

      <button id="printDashboard" class="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
        <i class="fas fa-print mr-2"></i> Print Dashboard
      </button>
    </div>
    <!-- Welcome Section -->
    <div class="bg-gradient-to-r from-blue-600 to-indigo-700 dark:from-blue-800 dark:to-indigo-900 rounded-xl shadow-lg p-8 mb-8 mt-6 relative overflow-hidden print-break-inside-avoid">
      <div class="absolute right-0 top-0 opacity-10 print:hidden">
        <i class="fas fa-chart-line text-white text-9xl transform rotate-12 translate-x-8 -translate-y-8"></i>
      </div>
      <div class="relative z-10">
        <h2 class="text-3xl font-bold text-white mb-2 flex items-center">
          <i class="fas fa-tachometer-alt mr-3"></i> Admin Dashboard
        </h2>
        <p class="text-white text-lg max-w-3xl font-medium">Manage your e-logbook system efficiently from this central hub. Monitor student activities, review logs, and track performance metrics.</p>
        <div class="mt-4 flex flex-wrap gap-3 print:hidden">
          <a href="{% url 'admin_section:admin_reviews' %}" class="inline-flex items-center px-4 py-2 bg-white hover:bg-opacity-90 rounded-lg text-blue-700 font-medium transition-all duration-200 shadow-sm">
            <i class="fas fa-clipboard-check mr-2"></i> Review Logs
          </a>
          <a href="{% url 'admin_section:add_department' %}" class="inline-flex items-center px-4 py-2 bg-white hover:bg-opacity-90 rounded-lg text-blue-700 font-medium transition-all duration-200 shadow-sm">
            <i class="fas fa-building mr-2"></i> Manage Departments
          </a>
          <a href="{% url 'admin_section:add_doctor' %}" class="inline-flex items-center px-4 py-2 bg-white hover:bg-opacity-90 rounded-lg text-blue-700 font-medium transition-all duration-200 shadow-sm">
            <i class="fas fa-user-md mr-2"></i> Manage Doctors
          </a>
          <a href="{% url 'admin_section:add_staff' %}" class="inline-flex items-center px-4 py-2 bg-white hover:bg-opacity-90 rounded-lg text-blue-700 font-medium transition-all duration-200 shadow-sm">
            <i class="fas fa-user-tie mr-2"></i> Manage Staff
          </a>
          <a href="{% url 'admin_section:bulk_import_users' %}" class="inline-flex items-center px-4 py-2 bg-white hover:bg-opacity-90 rounded-lg text-blue-700 font-medium transition-all duration-200 shadow-sm">
            <i class="fas fa-user-plus mr-2"></i> Add Users
          </a>
        </div>
      </div>
    </div>

    <!-- Quick Stats Grid Layout -->
    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4 mb-8 print:grid-cols-5 print-break-inside-avoid">
      <!-- Students Card -->
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300 border-b-4 border-blue-500 h-full">
        <div class="p-4 bg-blue-100 dark:bg-blue-900/50">
          <div class="flex items-center justify-between">
            <p class="text-sm font-semibold text-blue-800 dark:text-blue-100">Total Students</p>
            <div class="p-2 rounded-full bg-blue-600 text-white shadow-sm">
              <i class="fas fa-user-graduate"></i>
            </div>
          </div>
        </div>
        <div class="px-4 py-4">
          <div class="flex items-center">
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{total_student|default:"0"}}</p>
            <div class="ml-auto text-blue-600 dark:text-blue-400">
              <i class="fas fa-chart-line"></i>
            </div>
          </div>
          <p class="text-xs font-medium text-gray-700 dark:text-gray-300 mt-1">Registered in the system</p>
        </div>
      </div>

      <!-- Doctors Card -->
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300 border-b-4 border-green-500 h-full">
        <div class="p-4 bg-green-100 dark:bg-green-900/50">
          <div class="flex items-center justify-between">
            <p class="text-sm font-semibold text-green-800 dark:text-green-100">Total Doctors</p>
            <div class="p-2 rounded-full bg-green-600 text-white shadow-sm">
              <i class="fas fa-user-md"></i>
            </div>
          </div>
        </div>
        <div class="px-4 py-4">
          <div class="flex items-center">
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{total_doctors|default:"0"}}</p>
            <div class="ml-auto text-green-600 dark:text-green-400">
              <i class="fas fa-stethoscope"></i>
            </div>
          </div>
          <p class="text-xs font-medium text-gray-700 dark:text-gray-300 mt-1">Active supervisors</p>
        </div>
      </div>

      <!-- Activities Card -->
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300 border-b-4 border-purple-500 h-full">
        <div class="p-4 bg-purple-100 dark:bg-purple-900/50">
          <div class="flex items-center justify-between">
            <p class="text-sm font-semibold text-purple-800 dark:text-purple-100">Total Activities</p>
            <div class="p-2 rounded-full bg-purple-600 text-white shadow-sm">
              <i class="fas fa-clipboard-list"></i>
            </div>
          </div>
        </div>
        <div class="px-4 py-4">
          <div class="flex items-center">
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{total_activities|default:"0"}}</p>
            <div class="ml-auto text-purple-600 dark:text-purple-400">
              <i class="fas fa-tasks"></i>
            </div>
          </div>
          <p class="text-xs font-medium text-gray-700 dark:text-gray-300 mt-1">Available activity types</p>
        </div>
      </div>

      <!-- Records Card -->
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300 border-b-4 border-amber-500 h-full">
        <div class="p-4 bg-amber-100 dark:bg-amber-900/50">
          <div class="flex items-center justify-between">
            <p class="text-sm font-semibold text-amber-800 dark:text-amber-100">Total Records</p>
            <div class="p-2 rounded-full bg-amber-600 text-white shadow-sm">
              <i class="fas fa-file-alt"></i>
            </div>
          </div>
        </div>
        <div class="px-4 py-4">
          <div class="flex items-center">
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ total_logs|default:"0" }}</p>
            <div class="ml-auto text-amber-600 dark:text-amber-400">
              <i class="fas fa-clipboard-check"></i>
            </div>
          </div>
          <p class="text-xs font-medium text-gray-700 dark:text-gray-300 mt-1">Total log entries</p>
        </div>
      </div>

      <!-- Departments Card -->
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300 border-b-4 border-cyan-500 h-full">
        <div class="p-4 bg-cyan-100 dark:bg-cyan-900/50">
          <div class="flex items-center justify-between">
            <p class="text-sm font-semibold text-cyan-800 dark:text-cyan-100">Total Departments</p>
            <div class="p-2 rounded-full bg-cyan-600 text-white shadow-sm">
              <i class="fas fa-building"></i>
            </div>
          </div>
        </div>
        <div class="px-4 py-4">
          <div class="flex items-center">
            <p class="text-2xl font-bold text-gray-900 dark:text-white">{{total_departments|default:"0"}}</p>
            <div class="ml-auto text-cyan-600 dark:text-cyan-400">
              <i class="fas fa-sitemap"></i>
            </div>
          </div>
          <p class="text-xs font-medium text-gray-700 dark:text-gray-300 mt-1">Active departments</p>
        </div>
      </div>
    </div>


    <!-- Filter Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-8 print:grid-cols-2 print-break-inside-avoid">
      <!-- Department Filter -->
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden border-t-4 border-indigo-500 h-full">
        <div class="px-6 py-4 bg-indigo-100 dark:bg-indigo-900/50 border-b border-gray-200 dark:border-gray-700">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
            <i class="fas fa-filter text-indigo-600 dark:text-indigo-400 mr-2"></i> Department Filter
          </h3>
        </div>
        <div class="p-6">
          <form method="get" action="{% url 'admin_section:admin_dash' %}" class="grid grid-cols-1 md:grid-cols-3 gap-6 items-end">
            <!-- Department Filter -->
            <div class="col-span-1 md:col-span-2">
              <label for="department" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Filter by Department</label>
              <div class="relative rounded-md shadow-sm">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <i class="fas fa-building text-indigo-500 dark:text-indigo-400"></i>
                </div>
                <select name="department" id="department" class="pl-10 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm focus:border-indigo-500 focus:ring focus:ring-indigo-500 focus:ring-opacity-50 py-2">
                  <option value="">All Departments</option>
                  {% for dept in departments %}
                  <option value="{{ dept.id }}" {% if selected_department == dept.id %}selected{% endif %}>{{ dept.name }}</option>
                  {% endfor %}
                </select>
                <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700 dark:text-gray-300">

                </div>
              </div>
              {% if selected_department %}
              <div class="mt-2 text-sm text-indigo-600 dark:text-indigo-400 flex items-center">
                <i class="fas fa-info-circle mr-1"></i> Showing data for selected department
                <a href="{% url 'admin_section:admin_dash' %}" class="ml-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300" title="Clear filter">
                  <i class="fas fa-times-circle"></i>
                </a>
              </div>
              {% endif %}
            </div>

            <!-- Action Buttons -->
            <div class="flex space-x-3">
              <button type="submit" class="flex-1 px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-md shadow-sm transition-colors duration-200 flex items-center justify-center">
                <i class="fas fa-search mr-2"></i> Apply Filters
              </button>
              {% if selected_department %}
              <a href="{% url 'admin_section:admin_dash' %}" class="px-4 py-2 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-gray-200 rounded-md shadow-sm transition-colors duration-200 flex items-center justify-center">
                <i class="fas fa-times mr-2"></i> Clear
              </a>
              {% endif %}
            </div>
          </form>

          {% if selected_department and doctor_performance %}
          <!-- Doctor Performance Chart for Selected Department -->
          <div class="mt-6">
            <div class="flex justify-between items-center mb-4">
              <h4 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                <i class="fas fa-user-md text-indigo-600 dark:text-indigo-400 mr-2"></i> Doctor Performance
              </h4>
              <span class="px-3 py-1 bg-indigo-100 text-indigo-800 dark:bg-indigo-900/50 dark:text-indigo-100 rounded-full text-xs font-medium border border-indigo-200 dark:border-indigo-800">
                <i class="fas fa-users mr-1"></i> {{ doctor_performance|length }} Doctors
              </span>
            </div>
            <div class="h-64 bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm relative chart-container">
              <!-- Loading placeholder -->
              <div class="absolute inset-0 flex items-center justify-center bg-gray-50 dark:bg-gray-700 rounded-lg z-10 chart-loading">
                <div class="flex flex-col items-center">
                  <div class="animate-spin rounded-full h-8 w-8 border-2 border-t-2 border-indigo-500 mb-2"></div>
                  <span class="text-sm text-gray-600 dark:text-gray-300">Loading chart<span class="loading-dots"></span></span>
                </div>
              </div>
              <canvas id="doctorPerformanceChart" class="opacity-0 transition-opacity duration-300"></canvas>
            </div>

            <!-- Doctor List Table -->
            <div class="mt-4 overflow-x-auto bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm">
              <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead>
                  <tr class="bg-gray-50 dark:bg-gray-700">
                    <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-700 dark:text-gray-200 uppercase tracking-wider">Doctor</th>
                    <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-700 dark:text-gray-200 uppercase tracking-wider">Reviewed</th>
                    <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-700 dark:text-gray-200 uppercase tracking-wider">Approved</th>
                    <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-700 dark:text-gray-200 uppercase tracking-wider">Rejected</th>
                  </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                  {% for doctor in doctor_performance %}
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150">
                      <td class="px-3 py-2 whitespace-nowrap">
                        <div class="flex items-center">
                          <div class="flex-shrink-0 h-6 w-6 rounded-full bg-indigo-100 dark:bg-indigo-900/50 flex items-center justify-center mr-2">
                            <i class="fas fa-user-md text-indigo-500 dark:text-indigo-400 text-xs"></i>
                          </div>
                          <div class="text-sm font-medium text-gray-900 dark:text-white">{{ doctor.name }}</div>
                        </div>
                      </td>
                      <td class="px-3 py-2 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">{{ doctor.reviewed }}</td>
                      <td class="px-3 py-2 whitespace-nowrap">
                        <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-100 border border-green-200 dark:border-green-800">
                          {{ doctor.approved }}
                        </span>
                      </td>
                      <td class="px-3 py-2 whitespace-nowrap">
                        <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-100 border border-red-200 dark:border-red-800">
                          {{ doctor.rejected }}
                        </span>
                      </td>
                    </tr>
                  {% empty %}
                    <tr>
                      <td colspan="4" class="px-3 py-4 text-center text-sm text-gray-500 dark:text-gray-400">No doctor performance data available for this department.</td>
                    </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
          {% elif selected_department %}
            <div class="mt-6 p-6 bg-gray-50 dark:bg-gray-700/50 rounded-lg border border-gray-200 dark:border-gray-600 text-center">
              <i class="fas fa-info-circle text-indigo-500 dark:text-indigo-400 text-2xl mb-2"></i>
              <p class="text-sm text-gray-700 dark:text-gray-300 font-medium">No performance data available for doctors in the selected department.</p>
            </div>
          {% endif %}
        </div>
      </div>

      <!-- User Information Card -->
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden border-t-4 border-teal-500 h-full">
        <div class="px-6 py-4 bg-teal-100 dark:bg-teal-900/50 border-b border-gray-200 dark:border-gray-700">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
            <i class="fas fa-users text-teal-600 dark:text-teal-400 mr-2"></i> User Information
          </h3>
        </div>
        <div class="p-6">
          <div class="space-y-4">
            <div class="w-full">
              <label for="user_type" class="block text-sm font-medium text-gray-800 dark:text-gray-200 mb-2">
                Select User Type <span class="text-gray-600 dark:text-gray-400 text-xs">(View user details)</span>
              </label>
              <div class="relative rounded-md shadow-sm">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <i class="fas fa-user-tag text-teal-500 dark:text-teal-400"></i>
                </div>
                <select id="user_type" class="pl-10 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white focus:border-teal-500 focus:ring-teal-500 py-2">
                  <option value="">Select User Type</option>
                  <option value="student">Students</option>
                  <option value="doctor">Doctors</option>
                  <option value="staff">Staff</option>
                  <option value="admin">Admins</option>
                </select>
                <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700 dark:text-gray-300">

                </div>
              </div>
              <p class="mt-1 text-xs text-gray-600 dark:text-gray-400">View and export user information by type</p>
            </div>

            <div class="flex justify-end">
              <button id="loadUserData" class="px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white rounded-md shadow-sm transition-colors duration-200 flex items-center justify-center">
                <i class="fas fa-database mr-2"></i> Load Data
              </button>
            </div>
          </div>

          <!-- Loading Indicator with improved animation -->
          <div id="userDataLoading" class="mt-6 hidden animate-fadeIn">
            <div class="flex justify-center items-center p-6 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-200 dark:border-gray-600 overflow-hidden">
              <div class="flex items-center space-x-3">
                <div class="animate-spin rounded-full h-6 w-6 border-2 border-t-2 border-teal-500"></div>
                <div class="relative">
                  <span class="text-gray-800 dark:text-gray-200 text-sm font-medium">Loading user data</span>
                  <span class="loading-dots"></span>
                </div>
                <div class="absolute inset-0 loading-shimmer opacity-10"></div>
              </div>
            </div>
          </div>

          <!-- User Data Container with animation -->
          <div id="userDataContainer" class="mt-6 hidden animate-fadeIn">
            <div class="bg-gray-50 dark:bg-gray-700 p-5 rounded-lg border border-gray-200 dark:border-gray-600 shadow-sm">
              <div id="userDataContent" class="text-sm text-gray-800 dark:text-gray-200 overflow-x-auto">
                <!-- User data will be loaded here -->
                <div class="flex flex-col items-center justify-center py-6">
                  <div class="w-16 h-16 flex items-center justify-center rounded-full bg-teal-100 dark:bg-teal-900/50 mb-4 shadow-sm">
                    <i class="fas fa-users text-teal-500 dark:text-teal-400 text-2xl"></i>
                  </div>
                  <p class="text-center text-gray-700 dark:text-gray-300 font-medium max-w-md">Select a user type and click 'Load Data' to view information</p>
                  <p class="text-center text-gray-500 dark:text-gray-400 text-xs mt-2">Data will be cached for faster access</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Export Student Logs Section -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden border-t-4 border-emerald-500 mb-8 print-break-inside-avoid">
      <div class="px-6 py-4 bg-emerald-100 dark:bg-emerald-900/50 border-b border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
          <i class="fas fa-download text-emerald-600 dark:text-emerald-400 mr-2"></i> Export Student Logs
        </h3>
      </div>
      <div class="p-6">
        <form id="exportLogsForm" class="grid grid-cols-1 md:grid-cols-3 gap-6 items-end">

          <!-- Year Filter -->
          <div class="col-span-1">
            <label for="export_year" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Academic Year</label>
            <div class="relative rounded-md shadow-sm">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <i class="fas fa-calendar-alt text-emerald-500 dark:text-emerald-400"></i>
              </div>
              <select name="export_year" id="export_year" class="pl-10 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm focus:border-emerald-500 focus:ring focus:ring-emerald-500 focus:ring-opacity-50 py-2">
                <option value="">All Years</option>
                {% for year in years %}
                <option value="{{ year.id }}">{{ year.year_name }}</option>
                {% endfor %}
              </select>
            </div>
          </div>

          <!-- Section/Department Filter -->
          <div class="col-span-1">
            <label for="export_section" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Section/Department</label>
            <div class="relative rounded-md shadow-sm">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <i class="fas fa-building text-emerald-500 dark:text-emerald-400"></i>
              </div>
              <select name="export_section" id="export_section" class="pl-10 block w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm focus:border-emerald-500 focus:ring focus:ring-emerald-500 focus:ring-opacity-50 py-2">
                <option value="">All Sections</option>
                {% for dept in departments %}
                <option value="{{ dept.id }}">{{ dept.name }}</option>
                {% endfor %}
              </select>
            </div>
          </div>

          <!-- Export Buttons -->
          <div class="flex space-x-3">
            <button type="button" id="exportPdfBtn" class="flex-1 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md shadow-sm transition-colors duration-200 flex items-center justify-center">
              <i class="fas fa-file-pdf mr-2"></i>
              <span class="export-text">PDF</span>
              <span class="loading-text hidden">
                <i class="fas fa-spinner fa-spin mr-1"></i>...
              </span>
            </button>
            <button type="button" id="exportExcelBtn" class="flex-1 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md shadow-sm transition-colors duration-200 flex items-center justify-center">
              <i class="fas fa-file-excel mr-2"></i>
              <span class="export-text">Excel</span>
              <span class="loading-text hidden">
                <i class="fas fa-spinner fa-spin mr-1"></i>...
              </span>
            </button>
          </div>
        </form>

        <!-- Simple Export Info -->
        <div class="mt-4 p-3 bg-emerald-50 dark:bg-emerald-900/20 rounded-lg border border-emerald-200 dark:border-emerald-800">
          <div class="flex items-center">
            <i class="fas fa-info-circle text-emerald-600 dark:text-emerald-400 mr-2"></i>
            <div class="text-sm text-emerald-700 dark:text-emerald-200">
              <strong>Export all student logs</strong> (approved and not approved) with AGU logo. Filter by year and section as needed.
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Performance Charts -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-8 print:grid-cols-2 print-break-inside-avoid">
      <!-- Pie Chart: Review Status -->
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden border-t-4 border-rose-500 h-full">
        <div class="px-6 py-4 bg-rose-100 dark:bg-rose-900/50 border-b border-gray-200 dark:border-gray-700">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
            <i class="fas fa-chart-pie text-rose-600 dark:text-rose-400 mr-2"></i> Review Status
          </h3>
        </div>
        <div class="p-6">
          <div class="h-64 bg-white dark:bg-gray-800 rounded-lg border border-gray-100 dark:border-gray-700 p-2 relative chart-container">
            <!-- Loading placeholder -->
            <div class="absolute inset-0 flex items-center justify-center bg-gray-50 dark:bg-gray-700 rounded-lg z-10 chart-loading">
              <div class="flex flex-col items-center">
                <div class="animate-spin rounded-full h-8 w-8 border-2 border-t-2 border-rose-500 mb-2"></div>
                <span class="text-sm text-gray-600 dark:text-gray-300">Loading chart<span class="loading-dots"></span></span>
              </div>
            </div>
            <canvas id="reviewStatusChart" class="opacity-0 transition-opacity duration-300"></canvas>
          </div>
          <div class="grid grid-cols-3 gap-2 mt-4 text-center text-sm">
            <div class="p-2 rounded-lg bg-green-100 dark:bg-green-900/50 border border-green-200 dark:border-green-800">
              <div class="font-medium text-green-800 dark:text-green-100">Approved</div>
              <div class="text-lg font-bold text-green-700 dark:text-green-300">{{ approved_logs|default:"0" }}</div>
            </div>
            <div class="p-2 rounded-lg bg-amber-100 dark:bg-amber-900/50 border border-amber-200 dark:border-amber-800">
              <div class="font-medium text-amber-800 dark:text-amber-100">Pending</div>
              <div class="text-lg font-bold text-amber-700 dark:text-amber-300">{{ pending_logs|default:"0" }}</div>
            </div>
            <div class="p-2 rounded-lg bg-red-100 dark:bg-red-900/50 border border-red-200 dark:border-red-800">
              <div class="font-medium text-red-800 dark:text-red-100">Rejected</div>
              <div class="text-lg font-bold text-red-700 dark:text-red-300">{{ rejected_logs|default:"0" }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- Bar Chart: Department Performance -->
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden border-t-4 border-blue-500 h-full">
        <div class="px-6 py-4 bg-blue-100 dark:bg-blue-900/50 border-b border-gray-200 dark:border-gray-700">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
            <i class="fas fa-chart-bar text-blue-600 dark:text-blue-400 mr-2"></i> Department Performance
          </h3>
        </div>
        <div class="p-6">
          <div class="h-64 bg-white dark:bg-gray-800 rounded-lg border border-gray-100 dark:border-gray-700 p-2 relative chart-container">
            <!-- Loading placeholder -->
            <div class="absolute inset-0 flex items-center justify-center bg-gray-50 dark:bg-gray-700 rounded-lg z-10 chart-loading">
              <div class="flex flex-col items-center">
                <div class="animate-spin rounded-full h-8 w-8 border-2 border-t-2 border-blue-500 mb-2"></div>
                <span class="text-sm text-gray-600 dark:text-gray-300">Loading chart<span class="loading-dots"></span></span>
              </div>
            </div>
            <canvas id="departmentChart" class="opacity-0 transition-opacity duration-300"></canvas>
          </div>
          <div class="mt-4 text-center text-sm">
            {% if chart_data.department_stats %}
            <div class="inline-flex items-center px-3 py-1 rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-100 border border-blue-200 dark:border-blue-800">
              <i class="fas fa-info-circle mr-1"></i> Showing data for {{ chart_data.department_stats|length }} departments
            </div>
            {% else %}
            <div class="inline-flex items-center px-3 py-1 rounded-full bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200 border border-gray-200 dark:border-gray-600">
              <i class="fas fa-info-circle mr-1"></i> No department data available
            </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>

    <!-- Student Performance Search -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden border-t-4 border-orange-500 mb-8 print-break-inside-avoid">
      <div class="px-6 py-4 bg-orange-100 dark:bg-orange-900/50 border-b border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
          <i class="fas fa-user-graduate text-orange-600 dark:text-orange-400 mr-2"></i> Student Performance Search
        </h3>
      </div>
      <div class="p-6">
        <form method="get" action="{% url 'admin_section:admin_dash' %}" class="space-y-4">
          <div class="w-full">
            <label for="student_search" class="block text-sm font-medium text-gray-800 dark:text-gray-200 mb-2">
              Search by Name, ID or Email <span class="text-gray-600 dark:text-gray-400 text-xs">(Find student performance data)</span>
            </label>
            <div class="relative rounded-md shadow-sm">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <i class="fas fa-search text-orange-500 dark:text-orange-400"></i>
              </div>
              <input type="text" id="student_search" name="student_search" value="{{ student_search|default:'' }}"
                     class="pl-10 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white focus:border-orange-500 focus:ring-orange-500 py-2"
                     placeholder="Enter student name, ID or email address">
              {% if student_search %}
              <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                <a href="{% url 'admin_section:admin_dash' %}" class="text-orange-500 hover:text-orange-600 dark:text-orange-400 dark:hover:text-orange-300" title="Clear search">
                  <i class="fas fa-times-circle"></i>
                </a>
              </div>
              {% endif %}
            </div>
            <p class="mt-1 text-xs text-gray-600 dark:text-gray-400">Search for students to view their performance metrics across departments</p>
          </div>

          <div class="flex justify-end space-x-3">
            <button type="submit" class="px-4 py-2 bg-orange-600 hover:bg-orange-700 text-white rounded-md shadow-sm transition-colors duration-200 flex items-center justify-center">
              <i class="fas fa-search mr-2"></i> Search
            </button>
            {% if student_search %}
            <a href="{% url 'admin_section:admin_dash' %}" class="px-4 py-2 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-gray-200 rounded-md shadow-sm transition-colors duration-200 flex items-center justify-center">
              <i class="fas fa-times mr-2"></i> Clear
            </a>
            {% endif %}
          </div>
        </form>

      {% if student_data %}
        <div class="mt-6 bg-gray-50 dark:bg-gray-700/50 rounded-xl border border-gray-200 dark:border-gray-600 overflow-hidden" id="studentReportSection">
          <!-- Student Profile Header -->
          <div class="p-6 bg-gradient-to-r from-orange-500/20 to-amber-500/20 dark:from-orange-900/40 dark:to-amber-900/40 border-b border-gray-200 dark:border-gray-600">
            <div class="flex flex-col sm:flex-row items-center sm:items-start gap-4">
              <div class="w-16 h-16 rounded-full bg-gradient-to-br from-orange-500 to-amber-500 flex items-center justify-center text-white shadow-lg flex-shrink-0">
                <i class="fas fa-user-graduate text-2xl"></i>
              </div>
              <div class="text-center sm:text-left flex-grow">
                <h4 class="text-xl font-bold text-gray-900 dark:text-white">{{ student_data.name }}</h4>
                <div class="flex flex-col sm:flex-row sm:items-center flex-wrap gap-2 sm:gap-4 mt-1 text-sm text-gray-700 dark:text-gray-200">
                  <div class="flex items-center justify-center sm:justify-start">
                    <i class="fas fa-envelope text-orange-500 dark:text-orange-400 mr-1.5"></i>
                    <span>{{ student_data.email }}</span>
                  </div>
                  <div class="flex items-center justify-center sm:justify-start">
                    <i class="fas fa-id-card text-orange-500 dark:text-orange-400 mr-1.5"></i>
                    <span>ID: {{ student_data.id }}</span>
                  </div>
                </div>
              </div>
              <div class="ml-auto flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 print:hidden flex-shrink-0">
                <button onclick="generateStudentPDF()" class="inline-flex items-center justify-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-800 dark:text-gray-100 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 shadow-sm">
                  <i class="fas fa-file-pdf mr-1.5 text-red-600 dark:text-red-400"></i> Download PDF
                </button>
                <button onclick="printStudentReport()" class="inline-flex items-center justify-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 rounded-md text-sm font-medium text-gray-800 dark:text-gray-100 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 shadow-sm">
                  <i class="fas fa-print mr-1.5 text-blue-600 dark:text-blue-400"></i> Print
                </button>
              </div>
            </div>
          </div>

          <!-- Performance Stats -->
          <div class="grid grid-cols-1 sm:grid-cols-3 divide-y sm:divide-y-0 sm:divide-x divide-gray-200 dark:divide-gray-700 border-b border-gray-200 dark:border-gray-700">
            <div class="p-4 sm:p-6 flex items-center">
              <div class="p-3 rounded-full bg-green-100 dark:bg-green-900/50 mr-4 shadow-sm flex-shrink-0">
                <i class="fas fa-check-circle text-green-600 dark:text-green-300 text-xl"></i>
              </div>
              <div>
                <p class="text-sm font-medium text-gray-700 dark:text-gray-300">Approved Logs</p>
                <p class="text-2xl font-bold text-green-700 dark:text-green-300">{{ student_data.approved_logs|default:"0" }}</p>
              </div>
            </div>
            <div class="p-4 sm:p-6 flex items-center">
              <div class="p-3 rounded-full bg-amber-100 dark:bg-amber-900/50 mr-4 shadow-sm flex-shrink-0">
                <i class="fas fa-clock text-amber-600 dark:text-amber-300 text-xl"></i>
              </div>
              <div>
                <p class="text-sm font-medium text-gray-700 dark:text-gray-300">Pending Logs</p>
                <p class="text-2xl font-bold text-amber-700 dark:text-amber-300">{{ student_data.pending_logs|default:"0" }}</p>
              </div>
            </div>
            <div class="p-4 sm:p-6 flex items-center">
              <div class="p-3 rounded-full bg-red-100 dark:bg-red-900/50 mr-4 shadow-sm flex-shrink-0">
                <i class="fas fa-times-circle text-red-600 dark:text-red-300 text-xl"></i>
              </div>
              <div>
                <p class="text-sm font-medium text-gray-700 dark:text-gray-300">Rejected Logs</p>
                <p class="text-2xl font-bold text-red-700 dark:text-red-300">{{ student_data.rejected_logs|default:"0" }}</p>
              </div>
            </div>
          </div>

          {% if student_performance_data %}
          <!-- Student Performance by Department Chart -->
          <div class="p-6">
            <div class="flex items-center justify-between mb-4">
              <h5 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                <i class="fas fa-chart-line text-orange-600 dark:text-orange-400 mr-2"></i> Performance by Department
              </h5>
              <div class="text-sm text-gray-700 dark:text-gray-300">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-100 border border-blue-200 dark:border-blue-800">
                  <i class="fas fa-info-circle mr-1"></i> {{ student_performance_data|length }} departments
                </span>
              </div>
            </div>
            <div class="h-64 bg-white dark:bg-gray-800 p-4 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm relative chart-container">
              <!-- Loading placeholder -->
              <div class="absolute inset-0 flex items-center justify-center bg-gray-50 dark:bg-gray-700 rounded-lg z-10 chart-loading">
                <div class="flex flex-col items-center">
                  <div class="animate-spin rounded-full h-8 w-8 border-2 border-t-2 border-orange-500 mb-2"></div>
                  <span class="text-sm text-gray-600 dark:text-gray-300">Loading chart<span class="loading-dots"></span></span>
                </div>
              </div>
              <canvas id="studentPerformanceChart" class="opacity-0 transition-opacity duration-300"></canvas>
            </div>
          </div>

          <!-- Department Performance Table -->
          <div class="px-6 pb-6" id="departmentDetailsTableContainer">
            <div class="flex items-center justify-between mb-4">
              <h5 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                <i class="fas fa-table text-orange-600 dark:text-orange-400 mr-2"></i> Department Details
              </h5>
              <div class="flex space-x-2 print:hidden">
                <button onclick="exportTableToCSV('department_details', 'departmentDetailsTable')" class="inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-green-800 bg-green-100 hover:bg-green-200 dark:bg-green-900/50 dark:text-green-100 dark:hover:bg-green-900/70 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 shadow-sm">
                  <i class="fas fa-file-csv mr-1"></i> CSV
                </button>
                <button onclick="generateDepartmentPDF()" class="inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-red-800 bg-red-100 hover:bg-red-200 dark:bg-red-900/50 dark:text-red-100 dark:hover:bg-red-900/70 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 shadow-sm">
                  <i class="fas fa-file-pdf mr-1"></i> PDF
                </button>
              </div>
            </div>
            <div class="overflow-x-auto bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 shadow-sm max-h-[300px]">
              <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700" id="departmentDetailsTable">
                <thead class="bg-gray-50 dark:bg-gray-700 sticky top-0 z-10">
                  <tr>
                    <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-700 dark:text-gray-200 uppercase tracking-wider">Department</th>
                    <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-700 dark:text-gray-200 uppercase tracking-wider">Total</th>
                    <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-700 dark:text-gray-200 uppercase tracking-wider">Approved</th>
                    <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-700 dark:text-gray-200 uppercase tracking-wider">Pending</th>
                    <th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-700 dark:text-gray-200 uppercase tracking-wider">Rejected</th>
                  </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
                  {% for dept in student_performance_data %}
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150">
                      <td class="px-4 py-3 whitespace-nowrap">
                        <div class="text-sm font-medium text-gray-900 dark:text-white">{{ dept.department }}</div>
                      </td>
                      <td class="px-4 py-3 whitespace-nowrap">
                        <div class="text-sm font-medium text-gray-900 dark:text-white">{{ dept.total }}</div>
                      </td>
                      <td class="px-4 py-3 whitespace-nowrap">
                        <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-100 border border-green-200 dark:border-green-800">
                          {{ dept.approved }}
                        </span>
                      </td>
                      <td class="px-4 py-3 whitespace-nowrap">
                        <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-amber-100 text-amber-800 dark:bg-amber-900/50 dark:text-amber-100 border border-amber-200 dark:border-amber-800">
                          {{ dept.pending }}
                        </span>
                      </td>
                      <td class="px-4 py-3 whitespace-nowrap">
                        <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-100 border border-red-200 dark:border-red-800">
                          {{ dept.rejected }}
                        </span>
                      </td>
                    </tr>
                  {% empty %}
                    <tr>
                      <td colspan="5" class="px-4 py-4 text-center text-sm text-gray-500 dark:text-gray-400">No department performance data available for this student.</td>
                    </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
          </div>
          {% else %}
          <!-- No Performance Data -->
          <div class="p-6 text-center">
            <div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gray-100 dark:bg-gray-800 mb-4 shadow-sm">
              <i class="fas fa-chart-bar text-orange-500 dark:text-orange-400 text-2xl"></i>
            </div>
            <h5 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Performance Data Available</h5>
            <p class="text-gray-700 dark:text-gray-300 max-w-md mx-auto font-medium">This student has not submitted any logs yet or all logs are still pending review.</p>
          </div>
          {% endif %}
        </div>
      {% else %}
        {% if student_search %}
          <div class="mt-6 bg-gray-50 dark:bg-gray-700/50 rounded-xl border border-gray-200 dark:border-gray-600 overflow-hidden shadow-sm">
            <div class="p-8 text-center">
              <div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-orange-100 dark:bg-orange-900/50 mb-4 shadow-sm">
                <i class="fas fa-search text-orange-600 dark:text-orange-400 text-2xl"></i>
              </div>
              <h5 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Results Found</h5>
              <p class="text-gray-700 dark:text-gray-200 max-w-md mx-auto">No student found with the search term <span class="font-medium text-orange-700 dark:text-orange-300">"{{ student_search }}"</span></p>
              <p class="text-sm text-gray-600 dark:text-gray-300 mt-2 mb-4 font-medium">Try searching by name, student ID, or email address</p>
              <a href="{% url 'admin_section:admin_dash' %}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500">
                <i class="fas fa-times mr-2"></i> Clear Search
              </a>
            </div>
          </div>
        {% endif %}
      {% endif %}
    </div>

    <!-- Recent Logs Table -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden border-t-4 border-indigo-500 mb-8 print-break-inside-avoid">
      <div class="px-6 py-4 bg-indigo-100 dark:bg-indigo-900/50 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
          <i class="fas fa-clipboard-list text-indigo-600 dark:text-indigo-400 mr-2"></i> Recent Logs
          <span class="ml-2 px-2 py-1 text-xs rounded-full bg-white text-gray-800 dark:bg-gray-800 dark:text-gray-100 border border-indigo-200 dark:border-indigo-700">{{ recent_logs|length }}</span>
        </h3>
        <a href="{% url 'admin_section:admin_reviews' %}" class="inline-flex items-center px-3 py-1.5 border border-transparent text-sm leading-4 font-medium rounded-md text-indigo-800 bg-indigo-100 hover:bg-indigo-200 dark:bg-indigo-900/50 dark:text-indigo-100 dark:hover:bg-indigo-900/70 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 shadow-sm print:hidden">
          <i class="fas fa-external-link-alt mr-1.5"></i> View All
        </a>
      </div>

      {% if recent_logs %}
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead>
              <tr class="bg-gray-50 dark:bg-gray-700">
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 dark:text-gray-200 uppercase tracking-wider">
                  <div class="flex items-center">
                    <span>Student</span>
                    <i class="fas fa-sort ml-1 text-indigo-500 dark:text-indigo-400"></i>
                  </div>
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 dark:text-gray-200 uppercase tracking-wider">
                  <div class="flex items-center">
                    <span>Date</span>
                    <i class="fas fa-sort ml-1 text-indigo-500 dark:text-indigo-400"></i>
                  </div>
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 dark:text-gray-200 uppercase tracking-wider">
                  <div class="flex items-center">
                    <span>Department</span>
                    <i class="fas fa-sort ml-1 text-indigo-500 dark:text-indigo-400"></i>
                  </div>
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 dark:text-gray-200 uppercase tracking-wider">
                  <div class="flex items-center">
                    <span>Activity</span>
                    <i class="fas fa-sort ml-1 text-indigo-500 dark:text-indigo-400"></i>
                  </div>
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-700 dark:text-gray-200 uppercase tracking-wider">Status</th>
                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-700 dark:text-gray-200 uppercase tracking-wider print:hidden">Actions</th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-200 dark:divide-gray-700">
              {% for log in recent_logs %}
                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="flex-shrink-0 h-8 w-8 rounded-full bg-indigo-100 dark:bg-indigo-900/50 flex items-center justify-center">
                        <i class="fas fa-user-graduate text-indigo-500 dark:text-indigo-400"></i>
                      </div>
                      <div class="ml-3">
                        <div class="text-sm font-medium text-gray-900 dark:text-white">{{ log.student.user.get_full_name }}</div>
                        <div class="text-xs text-gray-700 dark:text-gray-300 font-medium">ID: {{ log.student.student_id }}</div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900 dark:text-white">{{ log.date|date:"M d, Y" }}</div>
                    <div class="text-xs text-gray-700 dark:text-gray-300 font-medium">{{ log.date|time:"h:i A" }}</div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-100 border border-blue-200 dark:border-blue-800">
                      {{ log.department.name }}
                    </span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium text-gray-900 dark:text-white">{{ log.activity_type.name }}</div>
                    <div class="text-xs text-gray-700 dark:text-gray-300 font-medium truncate max-w-[150px]" title="{{ log.core_diagnosis.name }}">
                      {{ log.core_diagnosis.name }}
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    {% if log.is_reviewed %}
                      {% if log.reviewer_comments and 'REJECTED:' in log.reviewer_comments %}
                        <span class="px-2 py-1 inline-flex items-center text-xs font-medium rounded-full bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-100 border border-red-200 dark:border-red-800">
                          <i class="fas fa-times-circle mr-1"></i> Rejected
                        </span>
                      {% else %}
                        <span class="px-2 py-1 inline-flex items-center text-xs font-medium rounded-full bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-100 border border-green-200 dark:border-green-800">
                          <i class="fas fa-check-circle mr-1"></i> Approved
                        </span>
                      {% endif %}
                    {% else %}
                      <span class="px-2 py-1 inline-flex items-center text-xs font-medium rounded-full bg-amber-100 text-amber-800 dark:bg-amber-900/50 dark:text-amber-100 border border-amber-200 dark:border-amber-800">
                        <i class="fas fa-clock mr-1"></i> Pending
                      </span>
                    {% endif %}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium print:hidden">
                    <a href="{% url 'admin_section:review_log' log.id %}"
                       class="p-1.5 bg-indigo-50 text-indigo-600 rounded hover:bg-indigo-100 dark:bg-indigo-900/30 dark:text-indigo-400 dark:hover:bg-indigo-900/50 transition-colors duration-200"
                       title="{% if log.is_reviewed %}View Details{% else %}Review Log{% endif %}">
                      <i class="fas {% if log.is_reviewed %}fa-eye{% else %}fa-clipboard-check{% endif %}"></i>
                    </a>
                  </td>
                </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      {% else %}
        <div class="p-8 text-center">
          <div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gray-100 dark:bg-gray-800 mb-4 shadow-sm">
            <i class="fas fa-clipboard-list text-indigo-500 dark:text-indigo-400 text-2xl"></i>
          </div>
          <h5 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Logs Found</h5>
          <p class="text-gray-700 dark:text-gray-300 max-w-md mx-auto font-medium">There are no recent logs to display at this time.</p>
        </div>
      {% endif %}
    </div>

    <!-- Quick Actions -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden border-t-4 border-emerald-500 mb-8 print:hidden">
      <div class="px-6 py-4 bg-emerald-100 dark:bg-emerald-900/50 border-b border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
          <i class="fas fa-bolt text-emerald-600 dark:text-emerald-400 mr-2"></i> Quick Actions
        </h3>
      </div>
      <div class="p-6">
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <a href="{% url 'admin_section:add_activity_type' %}" class="group relative flex flex-col items-center p-5 bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-all duration-200 overflow-hidden h-full">
            <div class="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-blue-500/10 dark:from-blue-500/10 dark:to-blue-500/20 transform scale-x-0 group-hover:scale-x-100 transition-transform origin-left duration-300"></div>
            <div class="relative z-10 flex flex-col items-center">
              <div class="w-12 h-12 flex items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900/50 mb-3 group-hover:scale-110 transition-transform duration-200">
                <i class="fas fa-clipboard-list text-blue-600 dark:text-blue-400 text-xl"></i>
              </div>
              <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-1">Activity Types</h4>
              <p class="text-xs text-gray-700 dark:text-gray-300 text-center font-medium">Manage activity categories</p>
            </div>
          </a>

          <a href="{% url 'admin_section:bulk_import_users' %}" class="group relative flex flex-col items-center p-5 bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-all duration-200 overflow-hidden h-full">
            <div class="absolute inset-0 bg-gradient-to-r from-teal-500/5 to-teal-500/10 dark:from-teal-500/10 dark:to-teal-500/20 transform scale-x-0 group-hover:scale-x-100 transition-transform origin-left duration-300"></div>
            <div class="relative z-10 flex flex-col items-center">
              <div class="w-12 h-12 flex items-center justify-center rounded-full bg-teal-100 dark:bg-teal-900/50 mb-3 group-hover:scale-110 transition-transform duration-200">
                <i class="fas fa-user-plus text-teal-600 dark:text-teal-400 text-xl"></i>
              </div>
              <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-1">Import Users</h4>
              <p class="text-xs text-gray-700 dark:text-gray-300 text-center font-medium">Bulk add users via CSV</p>
            </div>
          </a>

          <a href="{% url 'admin_section:core_dia_pro_session_list' %}" class="group relative flex flex-col items-center p-5 bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-all duration-200 overflow-hidden h-full">
            <div class="absolute inset-0 bg-gradient-to-r from-green-500/5 to-green-500/10 dark:from-green-500/10 dark:to-green-500/20 transform scale-x-0 group-hover:scale-x-100 transition-transform origin-left duration-300"></div>
            <div class="relative z-10 flex flex-col items-center">
              <div class="w-12 h-12 flex items-center justify-center rounded-full bg-green-100 dark:bg-green-900/50 mb-3 group-hover:scale-110 transition-transform duration-200">
                <i class="fas fa-calendar-plus text-green-600 dark:text-green-400 text-xl"></i>
              </div>
              <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-1">Session Activities</h4>
              <p class="text-xs text-gray-700 dark:text-gray-300 text-center font-medium">Manage clinical sessions</p>
            </div>
          </a>

          <a href="{% url 'admin_section:admin_reviews' %}" class="group relative flex flex-col items-center p-5 bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 shadow-sm hover:shadow-md transition-all duration-200 overflow-hidden h-full">
            <div class="absolute inset-0 bg-gradient-to-r from-purple-500/5 to-purple-500/10 dark:from-purple-500/10 dark:to-purple-500/20 transform scale-x-0 group-hover:scale-x-100 transition-transform origin-left duration-300"></div>
            <div class="relative z-10 flex flex-col items-center">
              <div class="w-12 h-12 flex items-center justify-center rounded-full bg-purple-100 dark:bg-purple-900/50 mb-3 group-hover:scale-110 transition-transform duration-200">
                <i class="fas fa-clipboard-check text-purple-600 dark:text-purple-400 text-xl"></i>
              </div>
              <h4 class="text-sm font-medium text-gray-900 dark:text-white mb-1">Review Logs</h4>
              <p class="text-xs text-gray-700 dark:text-gray-300 text-center font-medium">Approve student activities</p>
            </div>
          </a>
        </div>
      </div>
    </div>
  </div>

  <!-- Chart.js Script with direct loading for better reliability -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
  <!-- Add Chart.js Datalabels plugin for better visualization -->
  <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2.0.0"></script>
  <!-- Add jsPDF and html2canvas for PDF generation (Load dynamically later) -->
  <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script> -->
  <!-- <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script> -->

  <script>
    // Register ChartDataLabels plugin globally
    Chart.register(ChartDataLabels);

    // Initialize charts immediately when DOM is loaded
    document.addEventListener('DOMContentLoaded', () => {
      // Create an array to store chart instances
      const chartInstances = {}; // Use object for named instances

      // Function to show chart and hide loading indicator - improved version
      const showChart = (chartId) => {
        console.log('Attempting to show chart:', chartId);
        const canvas = document.getElementById(chartId);
        if (!canvas) {
          console.error('Canvas element not found for chart:', chartId);
          return;
        }

        // Find the closest container with the 'chart-container' class
        const container = canvas.closest('.chart-container');
        if (!container) {
          console.warn('Chart container not found for:', chartId, '. Looking for parent.');
          // Fallback to parent element if specific container class is missing
          const parentContainer = canvas.parentElement;
          if (parentContainer) {
              const loadingIndicator = parentContainer.querySelector('.chart-loading');
              if (loadingIndicator) {
                  loadingIndicator.style.display = 'none';
                  console.log('Loading indicator hidden (parent):', chartId);
              } else {
                  console.warn('Loading indicator not found in parent for:', chartId);
              }
          } else {
              console.error('Parent element not found for canvas:', chartId);
          }
          // Still make the canvas visible
          canvas.classList.remove('opacity-0');
          canvas.classList.add('opacity-100');
          return;
        }

        const loadingIndicator = container.querySelector('.chart-loading');
        if (!loadingIndicator) {
          console.warn('Loading indicator (.chart-loading) not found within container for:', chartId);
          // Still make the canvas visible
          canvas.classList.remove('opacity-0');
          canvas.classList.add('opacity-100');
          return;
        }

        // Hide the loading indicator
        loadingIndicator.style.display = 'none';
        console.log('Loading indicator hidden:', chartId);

        // Make the chart visible
        canvas.classList.remove('opacity-0');
        canvas.classList.add('opacity-100');
        console.log('Chart displayed successfully:', chartId);
      };


      // Initialize all charts directly
      const initializeAllCharts = () => {
        console.log('Initializing all charts');
        const chartCanvases = document.querySelectorAll('canvas[id$="Chart"]');
        console.log('Found chart canvases:', chartCanvases.length);

        chartCanvases.forEach(canvas => {
          console.log('Initializing chart:', canvas.id);
          initializeChart(canvas.id);
        });
      };

      // Initialize chart based on its ID - improved version
      const initializeChart = (chartId) => {
        console.log('Starting initialization for:', chartId);
        // Destroy existing chart instance if it exists
        if (chartInstances[chartId]) {
            console.log('Destroying existing chart instance for:', chartId);
            chartInstances[chartId].destroy();
        }

        try {
          let chart;
          const canvas = document.getElementById(chartId);
          if (!canvas) {
              console.error(`Canvas element not found for ${chartId}`);
              return; // Exit if canvas doesn't exist
          }

          switch(chartId) {
            case 'reviewStatusChart':
              chart = initReviewStatusChart();
              break;
            case 'departmentChart':
              // Check if data is available (assuming chart_data is passed correctly)
              {% if chart_data.department_stats %}
                chart = initDepartmentPerformanceChart();
              {% else %}
                console.log('No data for departmentChart, skipping initialization.');
              {% endif %}
              break;
            case 'doctorPerformanceChart':
              // Check if the element exists AND data is available
              {% if selected_department and doctor_performance %}
                if (canvas) {
                  chart = initDoctorPerformanceChart();
                }
              {% else %}
                 console.log('No data/element for doctorPerformanceChart, skipping initialization.');
              {% endif %}
              break;
            case 'studentPerformanceChart':
              // Check if the element exists AND data is available
              {% if student_data and student_performance_data %}
                if (canvas) {
                  chart = initStudentPerformanceChart();
                }
              {% else %}
                 console.log('No data/element for studentPerformanceChart, skipping initialization.');
              {% endif %}
              break;
            default:
              console.warn('Unknown chart ID:', chartId);
              return;
          }

          if (chart) {
            console.log('Chart created successfully:', chartId);
            chartInstances[chartId] = chart; // Store the new instance
            // Use a small timeout to ensure rendering completes before showing
            setTimeout(() => showChart(chartId), 50);
          } else {
            console.warn('Failed to create chart object for:', chartId);
            // If chart creation failed but canvas exists, ensure loading is hidden
             if (canvas) {
                const container = canvas.closest('.chart-container') || canvas.parentElement;
                const loadingIndicator = container?.querySelector('.chart-loading');
                if (loadingIndicator) loadingIndicator.style.display = 'none';
             }
          }
        } catch (error) {
          console.error('Error initializing chart:', chartId, error);
           // Ensure loading is hidden on error
           const canvas = document.getElementById(chartId);
           if (canvas) {
              const container = canvas.closest('.chart-container') || canvas.parentElement;
              const loadingIndicator = container?.querySelector('.chart-loading');
              if (loadingIndicator) loadingIndicator.style.display = 'none';
           }
        }
      };

      // Function to determine text color based on dark mode
      const isDarkMode = () => document.documentElement.classList.contains('dark');
      const textColor = () => (isDarkMode() ? '#E5E7EB' : '#1F2937'); // Use Tailwind gray colors
      const gridColor = () => (isDarkMode() ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)');

      // Review Status Pie Chart initialization function
      function initReviewStatusChart() {
        try {
          console.log('Initializing Review Status Chart');
          const canvas = document.getElementById('reviewStatusChart');
          if (!canvas) {
            console.error('Review Status Chart canvas not found');
            return null;
          }
          const reviewStatusCtx = canvas.getContext('2d');
          if (!reviewStatusCtx) {
            console.error('Could not get 2D context for Review Status Chart');
            return null;
          }

          const approved = {{ approved_logs|default:0 }};
          const pending = {{ pending_logs|default:0 }};
          const rejected = {{ rejected_logs|default:0 }};
          console.log('Review Status Chart data:', { approved, pending, rejected });

          if (approved === 0 && pending === 0 && rejected === 0) {
              console.log('No data for Review Status Chart.');
              // Optionally display a message on the canvas
              reviewStatusCtx.font = "16px Arial";
              reviewStatusCtx.fillStyle = textColor();
              reviewStatusCtx.textAlign = "center";
              reviewStatusCtx.fillText("No review data available", canvas.width / 2, canvas.height / 2);
              showChart('reviewStatusChart'); // Hide loader even if no data
              return null;
          }

          return new Chart(reviewStatusCtx, {
            type: 'pie',
            data: {
              labels: ['Approved', 'Pending', 'Rejected'],
              datasets: [{
                data: [approved, pending, rejected],
                backgroundColor: [
                  'rgba(16, 185, 129, 0.7)',  // Emerald-500
                  'rgba(245, 158, 11, 0.7)',  // Amber-500
                  'rgba(239, 68, 68, 0.7)'     // Red-500
                ],
                borderColor: [ // Use darker shades for borders
                  'rgba(5, 150, 105, 1)',   // Emerald-600
                  'rgba(217, 119, 6, 1)',    // Amber-600
                  'rgba(220, 38, 38, 1)'      // Red-600
                ],
                borderWidth: 1,
                hoverOffset: 8
              }]
            },
            options: {
              responsive: true,
              maintainAspectRatio: false,
              plugins: {
                legend: {
                  position: 'bottom',
                  labels: {
                    color: textColor(),
                    font: { size: 12 },
                    padding: 15,
                    usePointStyle: true,
                    pointStyle: 'circle'
                  }
                },
                title: {
                  display: false // Title is already in the card header
                },
                tooltip: {
                  backgroundColor: isDarkMode() ? 'rgba(31, 41, 55, 0.9)' : 'rgba(255, 255, 255, 0.9)',
                  titleColor: textColor(),
                  bodyColor: textColor(),
                  borderColor: gridColor(),
                  borderWidth: 1,
                  callbacks: {
                    label: function(context) {
                      const label = context.label || '';
                      const value = context.raw || 0;
                      const total = context.dataset.data.reduce((a, b) => a + b, 0);
                      const percentage = total > 0 ? Math.round((value / total) * 100) : 0;
                      return `${label}: ${value} (${percentage}%)`;
                    }
                  }
                },
                datalabels: { // Configure datalabels plugin
                    formatter: (value, ctx) => {
                        let sum = 0;
                        let dataArr = ctx.chart.data.datasets[0].data;
                        dataArr.map(data => {
                            sum += data;
                        });
                        let percentage = sum > 0 ? (value*100 / sum).toFixed(1)+"%" : '0%';
                        // Only show percentage if it's significant
                        return parseFloat(percentage) > 5 ? percentage : '';
                    },
                    color: '#fff', // White text for labels
                    font: {
                        weight: 'bold',
                        size: 12,
                    },
                    textShadowBlur: 2,
                    textShadowColor: 'rgba(0, 0, 0, 0.3)'
                }
              }
            }
            // Note: Plugin instance already registered globally
          });
        } catch (error) {
          console.error('Error creating Review Status Chart:', error);
          return null;
        }
      }

      // Department Performance Bar Chart initialization function
      function initDepartmentPerformanceChart() {
        try {
          console.log('Initializing Department Performance Chart');
          const canvas = document.getElementById('departmentChart');
          if (!canvas) {
            console.error('Department Performance Chart canvas not found');
            return null;
          }
          const departmentCtx = canvas.getContext('2d');
          if (!departmentCtx) {
            console.error('Could not get 2D context for Department Performance Chart');
            return null;
          }

          // Data generation wrapped in Django if
          {% if chart_data.department_stats %}
            const labels = [ {% for dept in chart_data.department_stats %} '{{ dept.name|escapejs }}', {% endfor %} ];
            const totalData = [ {% for dept in chart_data.department_stats %} {{ dept.total|default:0 }}, {% endfor %} ];
            const reviewedData = [ {% for dept in chart_data.department_stats %} {{ dept.reviewed|default:0 }}, {% endfor %} ];
            const pendingData = [ {% for dept in chart_data.department_stats %} {{ dept.pending|default:0 }}, {% endfor %} ];

            if (labels.length === 0) {
                console.log('No data for Department Performance Chart.');
                departmentCtx.font = "16px Arial";
                departmentCtx.fillStyle = textColor();
                departmentCtx.textAlign = "center";
                departmentCtx.fillText("No department performance data available", canvas.width / 2, canvas.height / 2);
                showChart('departmentChart'); // Hide loader
                return null;
            }

            return new Chart(departmentCtx, {
              type: 'bar',
              data: {
                labels: labels,
                datasets: [{
                  label: 'Total Logs',
                  data: totalData,
                  backgroundColor: 'rgba(59, 130, 246, 0.7)', // Blue-500
                  borderColor: 'rgba(37, 99, 235, 1)', // Blue-600
                  borderWidth: 1
                }, {
                  label: 'Reviewed',
                  data: reviewedData,
                  backgroundColor: 'rgba(16, 185, 129, 0.7)', // Emerald-500
                  borderColor: 'rgba(5, 150, 105, 1)', // Emerald-600
                  borderWidth: 1
                }, {
                  label: 'Pending',
                  data: pendingData,
                  backgroundColor: 'rgba(245, 158, 11, 0.7)', // Amber-500
                  borderColor: 'rgba(217, 119, 6, 1)', // Amber-600
                  borderWidth: 1
                }]
              },
              options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                  x: {
                    ticks: { color: textColor(), font: { size: 10 } }, // Smaller font for x-axis
                    grid: { color: gridColor() }
                  },
                  y: {
                    beginAtZero: true,
                    ticks: { color: textColor(), font: { weight: 'bold' } },
                    grid: { color: gridColor() }
                  }
                },
                plugins: {
                  legend: {
                    position: 'bottom',
                    labels: { color: textColor(), font: { size: 12 }, padding: 15, usePointStyle: true, pointStyle: 'rect' }
                  },
                  title: { display: false }, // Title is in card header
                  tooltip: {
                    backgroundColor: isDarkMode() ? 'rgba(31, 41, 55, 0.9)' : 'rgba(255, 255, 255, 0.9)',
                    titleColor: textColor(),
                    bodyColor: textColor(),
                    borderColor: gridColor(),
                    borderWidth: 1,
                    callbacks: {
                      label: function(context) {
                        return `${context.dataset.label || ''}: ${context.raw || 0}`;
                      }
                    }
                  },
                  datalabels: { // Disable datalabels for bar charts by default (can get cluttered)
                      display: false
                  }
                }
              }
            });
          {% else %}
            console.log('Department stats data not found in context.');
            departmentCtx.font = "16px Arial";
            departmentCtx.fillStyle = textColor();
            departmentCtx.textAlign = "center";
            departmentCtx.fillText("Department performance data unavailable", canvas.width / 2, canvas.height / 2);
            showChart('departmentChart'); // Hide loader
            return null;
          {% endif %}

        } catch (error) {
          console.error('Error creating Department Performance Chart:', error);
          return null;
        }
      }

      // Doctor Performance Chart initialization function
      function initDoctorPerformanceChart() {
        try {
          console.log('Initializing Doctor Performance Chart');
          const canvas = document.getElementById('doctorPerformanceChart');
          if (!canvas) {
            console.error('Doctor Performance Chart canvas not found');
            return null;
          }
          const doctorPerformanceCtx = canvas.getContext('2d');
          if (!doctorPerformanceCtx) {
            console.error('Could not get 2D context for Doctor Performance Chart');
            return null;
          }

          // Data generation wrapped in Django if
          {% if selected_department and doctor_performance %}
            const labels = [ {% for doctor in doctor_performance %} '{{ doctor.name|escapejs }}', {% endfor %} ];
            const reviewedData = [ {% for doctor in doctor_performance %} {{ doctor.reviewed|default:0 }}, {% endfor %} ];
            const approvedData = [ {% for doctor in doctor_performance %} {{ doctor.approved|default:0 }}, {% endfor %} ];
            const rejectedData = [ {% for doctor in doctor_performance %} {{ doctor.rejected|default:0 }}, {% endfor %} ];

            if (labels.length === 0) {
                console.log('No data for Doctor Performance Chart.');
                doctorPerformanceCtx.font = "16px Arial";
                doctorPerformanceCtx.fillStyle = textColor();
                doctorPerformanceCtx.textAlign = "center";
                doctorPerformanceCtx.fillText("No doctor performance data for this department", canvas.width / 2, canvas.height / 2);
                showChart('doctorPerformanceChart'); // Hide loader
                return null;
            }

            return new Chart(doctorPerformanceCtx, {
              type: 'bar',
              data: {
                labels: labels,
                datasets: [{
                  label: 'Reviewed',
                  data: reviewedData,
                  backgroundColor: 'rgba(59, 130, 246, 0.7)', // Blue-500
                  borderColor: 'rgba(37, 99, 235, 1)', // Blue-600
                  borderWidth: 1
                }, {
                  label: 'Approved',
                  data: approvedData,
                  backgroundColor: 'rgba(16, 185, 129, 0.7)', // Emerald-500
                  borderColor: 'rgba(5, 150, 105, 1)', // Emerald-600
                  borderWidth: 1
                }, {
                  label: 'Rejected',
                  data: rejectedData,
                  backgroundColor: 'rgba(239, 68, 68, 0.7)', // Red-500
                  borderColor: 'rgba(220, 38, 38, 1)', // Red-600
                  borderWidth: 1
                }]
              },
              options: {
                responsive: true,
                maintainAspectRatio: false,
                indexAxis: 'y', // Horizontal bar chart might be better for names
                scales: {
                  x: {
                    beginAtZero: true,
                    ticks: { color: textColor(), font: { weight: 'bold' } },
                    grid: { color: gridColor() }
                  },
                  y: {
                    ticks: { color: textColor(), font: { size: 10 } }, // Smaller font for names
                    grid: { display: false } // Hide y-axis grid lines for horizontal
                  }
                },
                plugins: {
                  legend: {
                    position: 'bottom',
                    labels: { color: textColor(), font: { size: 12 }, padding: 15, usePointStyle: true, pointStyle: 'rect' }
                  },
                  title: { display: false }, // Title is in card header
                  tooltip: {
                    backgroundColor: isDarkMode() ? 'rgba(31, 41, 55, 0.9)' : 'rgba(255, 255, 255, 0.9)',
                    titleColor: textColor(),
                    bodyColor: textColor(),
                    borderColor: gridColor(),
                    borderWidth: 1,
                    callbacks: {
                      label: function(context) {
                        return `${context.dataset.label || ''}: ${context.raw || 0}`;
                      }
                    }
                  },
                  datalabels: { display: false } // Disable datalabels
                }
              }
            });
          {% else %}
            console.log('Doctor performance data not found in context or no department selected.');
            // Don't draw text here, the HTML already handles the "no data" message
            showChart('doctorPerformanceChart'); // Hide loader if canvas exists
            return null;
          {% endif %}

        } catch (error) {
          console.error('Error creating Doctor Performance Chart:', error);
          return null;
        }
      }

      // Student Performance Chart initialization function
      function initStudentPerformanceChart() {
        try {
          console.log('Initializing Student Performance Chart');
          const canvas = document.getElementById('studentPerformanceChart');
          if (!canvas) {
            console.error('Student Performance Chart canvas not found');
            return null;
          }
          const studentPerformanceCtx = canvas.getContext('2d');
          if (!studentPerformanceCtx) {
            console.error('Could not get 2D context for Student Performance Chart');
            return null;
          }

          // Data generation wrapped in Django if
          {% if student_data and student_performance_data %}
            const labels = [ {% for dept_data in student_performance_data %} '{{ dept_data.department|escapejs }}', {% endfor %} ];
            const totalData = [ {% for dept_data in student_performance_data %} {{ dept_data.total|default:0 }}, {% endfor %} ];
            const approvedData = [ {% for dept_data in student_performance_data %} {{ dept_data.approved|default:0 }}, {% endfor %} ];
            const pendingData = [ {% for dept_data in student_performance_data %} {{ dept_data.pending|default:0 }}, {% endfor %} ];
            const rejectedData = [ {% for dept_data in student_performance_data %} {{ dept_data.rejected|default:0 }}, {% endfor %} ];

             if (labels.length === 0) {
                console.log('No data for Student Performance Chart.');
                studentPerformanceCtx.font = "16px Arial";
                studentPerformanceCtx.fillStyle = textColor();
                studentPerformanceCtx.textAlign = "center";
                studentPerformanceCtx.fillText("No performance data across departments for this student", canvas.width / 2, canvas.height / 2);
                showChart('studentPerformanceChart'); // Hide loader
                return null;
            }

            return new Chart(studentPerformanceCtx, {
              type: 'bar',
              data: {
                labels: labels,
                datasets: [{
                  label: 'Total',
                  data: totalData,
                  backgroundColor: 'rgba(59, 130, 246, 0.7)', // Blue-500
                  borderColor: 'rgba(37, 99, 235, 1)', // Blue-600
                  borderWidth: 1
                }, {
                  label: 'Approved',
                  data: approvedData,
                  backgroundColor: 'rgba(16, 185, 129, 0.7)', // Emerald-500
                  borderColor: 'rgba(5, 150, 105, 1)', // Emerald-600
                  borderWidth: 1
                }, {
                  label: 'Pending',
                  data: pendingData,
                  backgroundColor: 'rgba(245, 158, 11, 0.7)', // Amber-500
                  borderColor: 'rgba(217, 119, 6, 1)', // Amber-600
                  borderWidth: 1
                }, {
                  label: 'Rejected',
                  data: rejectedData,
                  backgroundColor: 'rgba(239, 68, 68, 0.7)', // Red-500
                  borderColor: 'rgba(220, 38, 38, 1)', // Red-600
                  borderWidth: 1
                }]
              },
              options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                  x: {
                    ticks: { color: textColor(), font: { size: 10 } },
                    grid: { color: gridColor() }
                  },
                  y: {
                    beginAtZero: true,
                    ticks: { color: textColor(), font: { weight: 'bold' } },
                    grid: { color: gridColor() }
                  }
                },
                plugins: {
                  legend: {
                    position: 'bottom',
                    labels: { color: textColor(), font: { size: 12 }, padding: 15, usePointStyle: true, pointStyle: 'rect' }
                  },
                  title: { display: false }, // Title is in card header
                  tooltip: {
                    backgroundColor: isDarkMode() ? 'rgba(31, 41, 55, 0.9)' : 'rgba(255, 255, 255, 0.9)',
                    titleColor: textColor(),
                    bodyColor: textColor(),
                    borderColor: gridColor(),
                    borderWidth: 1,
                    callbacks: {
                      label: function(context) {
                        return `${context.dataset.label || ''}: ${context.raw || 0}`;
                      }
                    }
                  },
                  datalabels: { display: false } // Disable datalabels
                }
              }
            });
          {% else %}
            console.log('Student performance data not found in context.');
             // Don't draw text here, the HTML already handles the "no data" message
            showChart('studentPerformanceChart'); // Hide loader if canvas exists
            return null;
          {% endif %}

        } catch (error) {
          console.error('Error creating Student Performance Chart:', error);
          return null;
        }
      }

      // Helper function to update common chart colors
      const updateChartAxisAndLegendColors = (chart, textColor, gridColor) => {
        if (!chart || !chart.options) return;
        let needsUpdate = false;

        // Update scales (axes and grid lines) if they exist
        if (chart.options.scales) {
          Object.values(chart.options.scales).forEach(axis => {
            if (axis.ticks && axis.ticks.color !== textColor) {
              axis.ticks.color = textColor;
              needsUpdate = true;
            }
            if (axis.grid && axis.grid.color !== gridColor) {
              axis.grid.color = gridColor;
              needsUpdate = true;
            }
          });
        }
        // Update legend and title colors
        if (chart.options.plugins) {
          if (chart.options.plugins.legend && chart.options.plugins.legend.labels.color !== textColor) {
            chart.options.plugins.legend.labels.color = textColor;
            needsUpdate = true;
          }
          if (chart.options.plugins.title && chart.options.plugins.title.color !== textColor) {
            chart.options.plugins.title.color = textColor;
            needsUpdate = true;
          }
          // Update datalabels color if plugin is used and configured
          if (chart.options.plugins.datalabels && chart.options.plugins.datalabels.color) {
             // Example: Keep datalabels white or adapt based on background? For now, keep as defined.
             // chart.options.plugins.datalabels.color = textColor(); // Or a fixed color like '#fff'
             // needsUpdate = true;
          }
        }
        // Use 'none' animation mode for better performance during updates
        if (needsUpdate) {
            console.log('Updating chart colors for:', chart.canvas.id);
            chart.update('none');
        }
      };

      // Update chart colors when theme changes
      const updateChartColors = () => {
        const newTextColor = textColor();
        const newGridColor = gridColor();
        console.log('Theme changed, updating chart colors. Dark mode:', isDarkMode());

        // Update all initialized charts
        Object.values(chartInstances).forEach(chart => {
            updateChartAxisAndLegendColors(chart, newTextColor, newGridColor);
        });
      };

      // Listen for theme changes triggered by AlpineJS
      // We need a way to observe the change Alpine makes to the <html> tag's class
      const observer = new MutationObserver((mutationsList) => {
          for(let mutation of mutationsList) {
              if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                  // Check if 'dark' class was added or removed
                  const isNowDark = document.documentElement.classList.contains('dark');
                  // Check if the theme actually changed compared to the last known state
                  // This avoids unnecessary updates if other classes change
                  if (isNowDark !== window.lastKnownDarkModeState) {
                      console.log('Detected theme change via MutationObserver.');
                      window.lastKnownDarkModeState = isNowDark;
                      // Use setTimeout to ensure Alpine/Tailwind styles are applied
                      setTimeout(updateChartColors, 50);
                  }
                  break; // Only need to detect the change once per mutation batch
              }
          }
      });

      // Start observing the <html> element for attribute changes
      observer.observe(document.documentElement, { attributes: true });
      // Store initial state
      window.lastKnownDarkModeState = isDarkMode();


      // Also listen for system preference changes (initial load and changes)
      const prefersDarkMQ = window.matchMedia('(prefers-color-scheme: dark)');
      // Function to handle system preference change
      const handleSystemThemeChange = () => {
          // This might conflict with manual toggle, prioritize manual toggle if implemented
          // For now, just log it. A real implementation might update localStorage and Alpine state.
          console.log('System color scheme changed. Dark:', prefersDarkMQ.matches);
          // Potentially trigger updateChartColors() if not using manual toggle or sync them
          // setTimeout(updateChartColors, 50); // Uncomment if you want system pref to override manual
      };
      // Listen for changes
      prefersDarkMQ.addEventListener('change', handleSystemThemeChange);


      // User Data Dropdown Functionality with debounce for better performance
      const userTypeSelect = document.getElementById('user_type');
      const loadUserDataBtn = document.getElementById('loadUserData');
      const userDataContainer = document.getElementById('userDataContainer');
      const userDataContent = document.getElementById('userDataContent');
      const userDataLoading = document.getElementById('userDataLoading');

      // Cache for user data to avoid unnecessary network requests
      const userDataCache = new Map();

      // Debounce function to prevent multiple rapid requests
      const debounce = (func, delay) => {
        let timeout;
        return (...args) => {
          clearTimeout(timeout);
          timeout = setTimeout(() => func.apply(this, args), delay);
        };
      };

      // Function to show a temporary notification
      const showNotification = (message, type = 'info') => {
          const notification = document.createElement('div');
          let bgColor, textColor, iconClass;

          switch(type) {
              case 'error':
                  bgColor = 'bg-red-100 dark:bg-red-900/50';
                  textColor = 'text-red-800 dark:text-red-100';
                  iconClass = 'fas fa-exclamation-triangle';
                  break;
              case 'warning':
                  bgColor = 'bg-amber-100 dark:bg-amber-900/50';
                  textColor = 'text-amber-800 dark:text-amber-100';
                  iconClass = 'fas fa-exclamation-circle';
                  break;
              default: // info
                  bgColor = 'bg-blue-100 dark:bg-blue-900/50';
                  textColor = 'text-blue-800 dark:text-blue-100';
                  iconClass = 'fas fa-info-circle';
          }

          notification.className = `fixed top-5 right-5 ${bgColor} ${textColor} px-4 py-3 rounded-lg shadow-lg border border-current z-50 animate-fadeIn max-w-sm`;
          notification.innerHTML = `<i class="${iconClass} mr-2"></i>${message}`;
          document.body.appendChild(notification);

          setTimeout(() => {
              notification.classList.remove('animate-fadeIn');
              notification.classList.add('animate-fadeOut');
              setTimeout(() => notification.remove(), 500); // Remove after fade out
          }, 3500); // Notification visible for 3.5 seconds
      };


      // Function to fetch user data with caching
      const fetchUserData = async (userType) => {
        if (!userType) {
          showNotification('Please select a user type.', 'warning');
          return;
        }

        // Check cache first
        if (userDataCache.has(userType)) {
          console.log('Loading user data from cache for type:', userType);
          userDataLoading.classList.add('hidden');
          userDataContainer.classList.remove('hidden');
          userDataContent.innerHTML = userDataCache.get(userType);
          // Re-attach dynamic listeners if needed (e.g., for search/export within the loaded content)
          attachUserDataListeners(userType);
          return;
        }

        console.log('Fetching user data from server for type:', userType);
        // Hide content and show loading indicator
        userDataContainer.classList.add('hidden');
        userDataLoading.classList.remove('hidden');
        userDataContent.innerHTML = ''; // Clear previous content

        try {
          // Make AJAX call to fetch user data with timeout
          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 second timeout

          const response = await fetch(`{% url 'admin_section:get_user_data' %}?user_type=${userType}`, {
            signal: controller.signal,
            headers: {
              'X-Requested-With': 'XMLHttpRequest',
              'Accept': 'application/json, text/html' // Accept both JSON and HTML
            }
          });

          clearTimeout(timeoutId);

          if (!response.ok) {
            let errorMessage = `Server responded with status ${response.status}`;
            try {
              const errorData = await response.json(); // Try parsing error as JSON
              errorMessage = errorData.error || errorMessage;
            } catch (e) {
              // If not JSON, maybe plain text error
              const textError = await response.text();
              if (textError) errorMessage = textError.substring(0, 100); // Limit length
            }
            throw new Error(errorMessage);
          }

          // Process successful response
          const contentType = response.headers.get("content-type");
          let htmlData;

          if (contentType && contentType.includes("application/json")) {
              const jsonData = await response.json();
              console.log('Received JSON data:', jsonData);
              // Format JSON data into a nice table
              htmlData = formatJsonToTable(jsonData, userType);
          } else {
              // Assume HTML response
              htmlData = await response.text();
              console.log('Received HTML data');
          }


          // Cache the result
          userDataCache.set(userType, htmlData);

          // Hide loading indicator and show content
          userDataLoading.classList.add('hidden');
          userDataContainer.classList.remove('hidden');

          // Inject the processed HTML
          userDataContent.innerHTML = htmlData;
          // Attach listeners for controls within the loaded content
          attachUserDataListeners(userType);

        } catch (error) {
          // Handle errors (including timeout)
          userDataLoading.classList.add('hidden');
          userDataContainer.classList.remove('hidden'); // Show container to display error

          let errorMessage = error.message;
          if (error.name === 'AbortError') {
            errorMessage = 'Request timed out. Please try again.';
          }

          userDataContent.innerHTML = `<div class="p-4 text-center text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/30 rounded border border-red-200 dark:border-red-800">
                                         <i class="fas fa-exclamation-triangle mr-2"></i>Error loading data: ${errorMessage}
                                       </div>`;
          console.error('Error fetching user data:', error);
          showNotification(`Error loading data: ${errorMessage}`, 'error');
        }
      };

      // Debounced version of fetchUserData
      const debouncedFetchUserData = debounce(fetchUserData, 300);

      // Add event listener for button click
      if (loadUserDataBtn) {
          loadUserDataBtn.addEventListener('click', () => debouncedFetchUserData(userTypeSelect.value));
      } else {
          console.error("Load User Data button not found");
      }

      // Also allow pressing Enter in the select to load data
      if (userTypeSelect) {
          userTypeSelect.addEventListener('change', () => {
              // Optional: Automatically load data on change? Or just enable the button?
              // For now, just changing the select doesn't load data automatically.
              console.log('User type selected:', userTypeSelect.value);
          });
          userTypeSelect.addEventListener('keyup', (e) => {
              if (e.key === 'Enter') {
                  debouncedFetchUserData(userTypeSelect.value);
              }
          });
      } else {
          console.error("User Type select dropdown not found");
      }


      // Function to format JSON data into a nice HTML table
      function formatJsonToTable(jsonData, userType) {
        // If jsonData is not an array or is empty, show message
        const dataArray = Array.isArray(jsonData) ? jsonData : (jsonData ? [jsonData] : []);
        if (dataArray.length === 0) {
            return `<div class="p-6 text-center text-gray-600 dark:text-gray-400">
                        <i class="fas fa-info-circle text-2xl mb-2"></i><br>No ${userType} data available.
                    </div>`;
        }

        // Get all unique keys from all objects, handling potential non-object items
        const allKeys = new Set();
        dataArray.forEach(item => {
          if (typeof item === 'object' && item !== null) {
            Object.keys(item).forEach(key => allKeys.add(key));
          }
        });

        // Convert Set to Array and sort keys (put common identifiers first)
        const priorityKeys = ['id', 'name', 'first_name', 'last_name', 'email', 'username', 'student_id', 'doctor_id', 'staff_id', 'department', 'phone_no', 'city', 'country', 'group', 'is_active', 'date_joined'];
        const keys = Array.from(allKeys).sort((a, b) => {
          const indexA = priorityKeys.indexOf(a);
          const indexB = priorityKeys.indexOf(b);
          if (indexA !== -1 && indexB !== -1) return indexA - indexB; // Both are priority
          if (indexA !== -1) return -1; // Only A is priority
          if (indexB !== -1) return 1;  // Only B is priority
          return a.localeCompare(b); // Neither is priority, sort alphabetically
        });

        // Create table header based on user type
        let userTypeTitle = userType.charAt(0).toUpperCase() + userType.slice(1) + 's'; // Simple pluralization
        if (userType === 'staff') userTypeTitle = 'Staff Members';
        if (userType === 'admin') userTypeTitle = 'Administrators';


        // Build the HTML table
        let html = `
          <div class="mb-4 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <h4 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
              <i class="fas fa-table mr-2 text-teal-500"></i> ${userTypeTitle} (<span id="userTableCount">${dataArray.length}</span>)
            </h4>
            <div class="flex space-x-2 flex-shrink-0">
              <button id="toggleUserSearch" class="px-3 py-1.5 bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-100 rounded-md text-sm font-medium hover:bg-blue-200 dark:hover:bg-blue-900/70 transition-colors duration-200 flex items-center">
                <i class="fas fa-search mr-1.5"></i> Search
              </button>
              <button id="exportUserDataBtn" data-filename="${userType}_data" class="px-3 py-1.5 bg-teal-100 text-teal-800 dark:bg-teal-900/50 dark:text-teal-100 rounded-md text-sm font-medium hover:bg-teal-200 dark:hover:bg-teal-900/70 transition-colors duration-200 flex items-center">
                <i class="fas fa-download mr-1.5"></i> Export CSV
              </button>
            </div>
          </div>

          <!-- Search Filter -->
          <div id="userSearchContainer" class="mb-4 hidden">
            <div class="p-4 bg-gray-100 dark:bg-gray-700/50 rounded-lg border border-gray-200 dark:border-gray-600">
              <div class="flex flex-col sm:flex-row gap-4">
                <div class="flex-1">
                  <label for="userSearchInput" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1 sr-only">Search Users</label>
                  <div class="relative rounded-md shadow-sm">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <i class="fas fa-search text-gray-400"></i>
                    </div>
                    <input type="text" id="userSearchInput" class="pl-10 block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-800 dark:text-white focus:border-teal-500 focus:ring-teal-500 py-2 text-sm" placeholder="Search table content...">
                  </div>
                </div>
                <div class="flex items-end space-x-2 flex-shrink-0">
                  <!-- Apply button removed, search happens on input -->
                  <button id="clearUserSearch" class="px-3 py-2 bg-gray-200 text-gray-800 dark:bg-gray-600 dark:text-gray-200 rounded-md shadow-sm hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors duration-200 flex items-center justify-center text-sm">
                    <i class="fas fa-times mr-1.5"></i> Clear
                  </button>
                </div>
              </div>
            </div>
          </div>

          <div class="overflow-x-auto rounded-lg border border-gray-200 dark:border-gray-700 max-h-[400px]">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700" id="userDataTable">
              <thead class="bg-gray-50 dark:bg-gray-700 sticky top-0 z-10">
                <tr>
        `;

        // Add table headers
        keys.forEach(key => {
          // Format header text (capitalize, replace underscores with spaces)
          const headerText = key.replace(/_/g, ' ')
            .split(' ')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
            .join(' ');

          html += `<th scope="col" class="px-4 py-3 text-left text-xs font-medium text-gray-700 dark:text-gray-200 uppercase tracking-wider">${headerText}</th>`;
        });

        html += `
                </tr>
              </thead>
              <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700" id="userTableBody">
        `;

        // Add table rows
        dataArray.forEach((item, index) => {
          // Ensure item is an object before trying to access keys
          if (typeof item !== 'object' || item === null) {
              console.warn("Skipping non-object item in data array:", item);
              return; // Skip this iteration
          }
          const rowClass = index % 2 === 0 ? '' : 'bg-gray-50 dark:bg-gray-700/50';
          html += `<tr class="${rowClass} hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-150">`;

          keys.forEach(key => {
            let cellValue = item[key];
            let cellClass = 'px-4 py-3 text-sm text-gray-900 dark:text-white whitespace-nowrap'; // Added whitespace-nowrap

            // Format cell value based on key and value type
            if (cellValue === null || cellValue === undefined) {
              cellValue = '<span class="text-gray-400 dark:text-gray-500">—</span>';
            } else if (typeof cellValue === 'boolean') {
              // More descriptive boolean display
              const isActive = key.includes('active'); // Guess if it's an 'active' status
              if (cellValue) {
                cellValue = `<span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-100"><i class="fas fa-check-circle mr-1"></i> ${isActive ? 'Active' : 'Yes'}</span>`;
              } else {
                cellValue = `<span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-100"><i class="fas fa-times-circle mr-1"></i> ${isActive ? 'Inactive' : 'No'}</span>`;
              }
            } else if (key === 'email') {
              cellValue = `<a href="mailto:${cellValue}" class="text-blue-600 dark:text-blue-400 hover:underline">${cellValue}</a>`;
            } else if (key === 'id' || key.endsWith('_id')) {
              cellClass += ' font-medium';
            } else if (key === 'department' && typeof cellValue === 'object' && cellValue !== null && cellValue.name) {
                // Handle nested department object if present
                cellValue = cellValue.name;
            } else if (key === 'group' && typeof cellValue === 'object' && cellValue !== null && cellValue.name) {
                // Handle nested group object if present
                cellValue = cellValue.name;
            } else if (key.includes('date') || key.includes('joined')) {
                // Basic date formatting attempt
                try {
                    cellValue = new Date(cellValue).toLocaleDateString();
                } catch (e) { /* ignore formatting error */ }
            } else if (typeof cellValue === 'object') {
              // Display complex objects/arrays simply
              cellValue = `<span class="text-gray-500 dark:text-gray-400 italic" title="${JSON.stringify(cellValue).replace(/"/g, '"')}">${Array.isArray(cellValue) ? '[Array]' : '{Object}'}</span>`;
              cellClass = 'px-4 py-3 text-sm text-gray-900 dark:text-white'; // Remove whitespace-nowrap for objects
            } else {
               // Escape HTML in string values to prevent XSS
               cellValue = String(cellValue).replace(/</g, "<").replace(/>/g, ">");
            }


            html += `<td class="${cellClass}" data-column="${key}">${cellValue}</td>`;
          });

          html += '</tr>';
        });

        html += `
              </tbody>
            </table>
          </div>
          <div class="mt-4 text-sm text-gray-600 dark:text-gray-400 flex flex-col sm:flex-row justify-between items-center gap-3">
            <span>Showing <span id="visibleRowCount">${dataArray.length}</span> of <span id="totalRowCount">${dataArray.length}</span> ${dataArray.length === 1 ? 'entry' : 'entries'}</span>
            <div class="flex space-x-2">
              <button id="refreshUserDataBtn" class="px-3 py-1.5 bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200 rounded-md text-sm font-medium hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200 flex items-center">
                <i class="fas fa-sync-alt mr-1.5"></i> Refresh
              </button>
            </div>
          </div>
        `;

        return html;
      }

      // Function to attach listeners to dynamically loaded user data controls
      function attachUserDataListeners(userType) {
          // Export button
          const exportBtn = document.getElementById('exportUserDataBtn');
          if (exportBtn) {
            exportBtn.addEventListener('click', () => {
                const filename = exportBtn.dataset.filename || `${userType}_export`;
                exportTableToCSV(filename, 'userDataTable');
            });
          }

          // Refresh button
          const refreshBtn = document.getElementById('refreshUserDataBtn');
          if (refreshBtn) {
            refreshBtn.addEventListener('click', () => {
              // Remove from cache to force refresh
              userDataCache.delete(userType);
              fetchUserData(userType); // Use non-debounced version for explicit refresh
            });
          }

          // Search toggle button
          const searchBtn = document.getElementById('toggleUserSearch');
          const searchContainer = document.getElementById('userSearchContainer');
          if (searchBtn && searchContainer) {
            searchBtn.addEventListener('click', () => {
              searchContainer.classList.toggle('hidden');
              if (!searchContainer.classList.contains('hidden')) {
                document.getElementById('userSearchInput').focus();
              }
            });
          }

          // Search functionality (live search on input)
          const searchInput = document.getElementById('userSearchInput');
          const clearSearchBtn = document.getElementById('clearUserSearch');
          const tableBody = document.getElementById('userTableBody');
          const visibleRowCountSpan = document.getElementById('visibleRowCount');
          const totalRowCountSpan = document.getElementById('totalRowCount'); // Assuming this exists or is added

          if (searchInput && clearSearchBtn && tableBody && visibleRowCountSpan && totalRowCountSpan) {
            const totalRows = parseInt(totalRowCountSpan.textContent, 10);

            // Debounced filter function
            const filterTable = debounce(() => {
              const searchTerm = searchInput.value.toLowerCase().trim();
              let visibleRows = 0;

              Array.from(tableBody.querySelectorAll('tr')).forEach(row => {
                // Search only in visible text content of cells
                const text = Array.from(row.querySelectorAll('td')).map(cell => cell.textContent.toLowerCase()).join(' ');
                const isVisible = searchTerm === '' || text.includes(searchTerm);
                row.style.display = isVisible ? '' : 'none'; // Use style.display instead of class for simplicity here
                if (isVisible) visibleRows++;
              });

              visibleRowCountSpan.textContent = visibleRows;
              // Update total count display if needed (e.g., "Showing X of Y entries")
              // totalRowCountSpan.textContent = totalRows; // Already set initially
            }, 250); // 250ms debounce delay

            // Apply search on input
            searchInput.addEventListener('input', filterTable);

            // Clear search
            clearSearchBtn.addEventListener('click', () => {
              searchInput.value = '';
              Array.from(tableBody.querySelectorAll('tr')).forEach(row => {
                row.style.display = ''; // Show all rows
              });
              visibleRowCountSpan.textContent = totalRows; // Reset count
            });
          }
      }


      // Function to export table data to CSV
      function exportTableToCSV(filename, tableId) {
        const table = document.getElementById(tableId);
        if (!table) {
            console.error(`Table with ID "${tableId}" not found for export.`);
            showNotification(`Could not find table "${tableId}" to export.`, 'error');
            return;
        }
        console.log(`Exporting table "${tableId}" to ${filename}.csv`);

        let csv = [];
        // Include headers
        const headers = [];
        table.querySelectorAll('thead th').forEach(th => {
            let text = th.textContent.trim();
            // Escape quotes and handle commas within headers
            text = text.replace(/"/g, '""');
            if (text.includes(',')) text = `"${text}"`;
            headers.push(text);
        });
        csv.push(headers.join(','));

        // Include visible rows (respecting search filter)
        const rows = table.querySelectorAll('tbody tr');
        rows.forEach(row => {
            // Check if row is visible (not display: none)
            if (row.style.display === 'none') return; // Skip hidden rows

            const rowData = [];
            row.querySelectorAll('td').forEach(td => {
                let text = td.textContent.trim();
                 // Escape quotes and handle commas within data
                text = text.replace(/"/g, '""');
                if (text.includes(',')) text = `"${text}"`;
                rowData.push(text);
            });
            csv.push(rowData.join(','));
        });


        if (csv.length <= 1) { // Only header row
            showNotification('No data available in the table to export.', 'warning');
            return;
        }

        // Download CSV file
        const csvString = csv.join('\n');
        const blob = new Blob([csvString], { type: 'text/csv;charset=utf-8;' });

        // Create download link
        const link = document.createElement('a');
        if (link.download !== undefined) {
          const url = URL.createObjectURL(blob);
          link.setAttribute('href', url);
          // Sanitize filename and add date
          const safeFilename = filename.replace(/[^a-z0-9]/gi, '_').toLowerCase();
          link.setAttribute('download', `${safeFilename}_${new Date().toISOString().slice(0, 10)}.csv`);
          link.style.visibility = 'hidden';
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
          console.log('CSV export initiated.');
        } else {
            showNotification('CSV download is not supported by your browser.', 'error');
        }
      }

      // Print dashboard function
      const printDashboardBtn = document.getElementById('printDashboard');
      if (printDashboardBtn) {
          printDashboardBtn.addEventListener('click', () => {
              console.log('Initiating print...');
              window.print();
          });
      }

      // --- PDF Generation Functions ---
      // Load jsPDF and html2canvas dynamically when needed
      let jsPDFLoaded = false;
      let html2canvasLoaded = false;
      const loadPdfLibs = (callback) => {
          const loadScript = (src, checker, loadedFlagSetter) => {
              return new Promise((resolve, reject) => {
                  if (checker()) {
                      loadedFlagSetter(true);
                      resolve();
                      return;
                  }
                  const script = document.createElement('script');
                  script.src = src;
                  script.onload = () => {
                      loadedFlagSetter(true);
                      console.log(src.split('/').pop(), 'loaded.');
                      resolve();
                  };
                  script.onerror = () => {
                      console.error('Failed to load script:', src);
                      showNotification(`Failed to load library: ${src.split('/').pop()}`, 'error');
                      reject(new Error(`Failed to load ${src}`));
                  };
                  document.head.appendChild(script);
              });
          };

          Promise.all([
              loadScript('https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js', () => typeof jspdf !== 'undefined', (val) => jsPDFLoaded = val),
              loadScript('https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js', () => typeof html2canvas !== 'undefined', (val) => html2canvasLoaded = val)
          ]).then(() => {
              if (jsPDFLoaded && html2canvasLoaded) {
                  callback();
              }
          }).catch(error => {
              console.error("Error loading PDF libraries:", error);
          });
      };

      // Function to print student report (using window.print for specific section)
      window.printStudentReport = function() {
        const studentSection = document.getElementById('studentReportSection');
        if (!studentSection) {
            showNotification('Student report section not found.', 'error');
            return;
        }

        // Create a temporary iframe for isolated printing
        const iframe = document.createElement('iframe');
        iframe.style.position = 'absolute';
        iframe.style.width = '0';
        iframe.style.height = '0';
        iframe.style.border = '0';
        document.body.appendChild(iframe);

        const pri = iframe.contentWindow;
        pri.document.open();
        pri.document.write(`
          <html>
            <head>
              <title>Student Report</title>
              <!-- Include Tailwind CDN or your compiled CSS link -->
              <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
              <!-- Include Font Awesome if needed -->
               <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
              <style>
                body { margin: 20px; font-family: sans-serif; }
                .print\\:hidden { display: none !important; } /* Ensure print:hidden works */
                /* Add any other print-specific styles here */
              </style>
            </head>
            <body>
              ${studentSection.innerHTML}
            </body>
          </html>
        `);
        pri.document.close();

        // Wait for content to load in iframe before printing
        iframe.onload = function() {
            pri.focus();
            pri.print();
            // Clean up the iframe after printing
            setTimeout(() => { document.body.removeChild(iframe); }, 1000);
        };
      }

      // Function to generate PDF for student report - simplified approach
      window.generateStudentPDF = function() {
          // Show initial notification
          showNotification('Preparing to generate PDF...', 'info');

          // Load PDF libraries first
          loadPdfLibs(() => {
              try {
                  const { jsPDF } = window.jspdf;
                  if (!jsPDF) {
                      console.error('jsPDF not properly loaded');
                      showNotification('PDF library not loaded properly. Please refresh the page and try again.', 'error');
                      return;
                  }

                  // Get the student report section
                  const studentElement = document.getElementById('studentReportSection');
                  if (!studentElement) {
                      showNotification('Student report section not found for PDF generation.', 'error');
                      return;
                  }

                  // Add loading indicator
                  const loadingIndicator = document.createElement('div');
                  loadingIndicator.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                  loadingIndicator.innerHTML = `
                    <div class="bg-white p-5 rounded-lg shadow-lg text-center">
                      <div class="animate-spin rounded-full h-10 w-10 border-4 border-blue-500 border-t-transparent mx-auto mb-3"></div>
                      <p class="text-gray-800 font-medium">Generating PDF...</p>
                      <p class="text-gray-600 text-sm mt-2">This may take a few moments</p>
                    </div>
                  `;
                  document.body.appendChild(loadingIndicator);

                  // Get student name for filename
                  const studentNameElement = studentElement.querySelector('h4');
                  const studentName = studentNameElement ? studentNameElement.textContent.trim() : 'student';
                  const sanitizedName = studentName.replace(/[^a-z0-9]/gi, '_').toLowerCase();
                  const filename = `${sanitizedName}_report_${new Date().toISOString().slice(0, 10)}.pdf`;

                  // Function to clean up resources
                  const cleanup = () => {
                      if (document.body.contains(loadingIndicator)) {
                          document.body.removeChild(loadingIndicator);
                      }
                  };

                  // APPROACH 1: Try a simpler method first - print to PDF directly
                  try {
                      // Create a temporary iframe for isolated printing
                      const iframe = document.createElement('iframe');
                      iframe.style.position = 'absolute';
                      iframe.style.width = '0';
                      iframe.style.height = '0';
                      iframe.style.border = '0';
                      document.body.appendChild(iframe);

                      // Get chart canvas and ensure it's visible
                      const chartCanvas = document.getElementById('studentPerformanceChart');
                      let chartImageData = null;

                      // If chart exists, get its image data
                      if (chartCanvas) {
                          try {
                              chartCanvas.classList.remove('opacity-0');
                              chartCanvas.classList.add('opacity-100');
                              chartImageData = chartCanvas.toDataURL('image/png');
                              console.log('Successfully captured chart image data');
                          } catch (e) {
                              console.error('Error capturing chart image:', e);
                          }
                      }

                      // Clone the student element content
                      const contentClone = studentElement.cloneNode(true);

                      // If we have chart image data, replace the canvas in the clone
                      if (chartImageData) {
                          const clonedCanvas = contentClone.querySelector('#studentPerformanceChart');
                          if (clonedCanvas) {
                              const img = document.createElement('img');
                              img.src = chartImageData;
                              img.style.width = '100%';
                              img.style.height = 'auto';
                              clonedCanvas.parentNode.replaceChild(img, clonedCanvas);
                              console.log('Replaced canvas with image in clone');
                          }
                      }

                      // Write content to iframe
                      const pri = iframe.contentWindow;
                      pri.document.open();
                      pri.document.write(`
                        <html>
                          <head>
                            <title>Student Report</title>
                            <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
                            <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
                            <style>
                              body { margin: 20px; font-family: sans-serif; }
                              .print\\:hidden { display: none !important; }
                              @media print {
                                body { -webkit-print-color-adjust: exact !important; print-color-adjust: exact !important; }
                                canvas { display: none !important; }
                              }
                            </style>
                          </head>
                          <body>
                            ${contentClone.outerHTML}
                          </body>
                        </html>
                      `);
                      pri.document.close();

                      // Wait for content to load in iframe
                      setTimeout(() => {
                          try {
                              // Try to print
                              pri.focus();
                              pri.print();
                              showNotification('Print dialog opened. Please save as PDF.', 'success');
                              cleanup();

                              // Clean up iframe after printing
                              setTimeout(() => {
                                  if (document.body.contains(iframe)) {
                                      document.body.removeChild(iframe);
                                  }
                              }, 2000);
                          } catch (printError) {
                              console.error('Error during print:', printError);
                              document.body.removeChild(iframe);

                              // Fall back to APPROACH 2 if printing fails
                              tryApproach2();
                          }
                      }, 1000);
                  } catch (error) {
                      console.error('Error in Approach 1:', error);
                      // Fall back to APPROACH 2 if approach 1 fails
                      tryApproach2();
                  }

                  // APPROACH 2: Use html2canvas as fallback
                  function tryApproach2() {
                      console.log('Falling back to html2canvas approach');
                      showNotification('Trying alternative PDF generation method...', 'info');

                      // Ensure chart is visible
                      const chartCanvas = document.getElementById('studentPerformanceChart');
                      if (chartCanvas) {
                          chartCanvas.classList.remove('opacity-0');
                          chartCanvas.classList.add('opacity-100');
                      }

                      // Hide elements that shouldn't be in the PDF
                      const hiddenElements = studentElement.querySelectorAll('.print\\:hidden');
                      hiddenElements.forEach(el => el.style.display = 'none');

                      // Use a simpler html2canvas configuration
                      setTimeout(() => {
                          html2canvas(studentElement, {
                              scale: 1.5,
                              useCORS: true,
                              allowTaint: true,
                              backgroundColor: '#ffffff',
                              logging: false,
                              removeContainer: false
                          }).then(canvas => {
                              // Restore hidden elements
                              hiddenElements.forEach(el => el.style.display = '');

                              try {
                                  // Create PDF
                                  const imgData = canvas.toDataURL('image/jpeg', 0.95);
                                  const pdf = new jsPDF({
                                      orientation: 'portrait',
                                      unit: 'mm',
                                      format: 'a4'
                                  });

                                  const pdfWidth = pdf.internal.pageSize.getWidth();
                                  const pdfHeight = pdf.internal.pageSize.getHeight();

                                  // Add AGU header
                                  pdf.setFontSize(16);
                                  pdf.setFont('helvetica', 'bold');
                                  pdf.text('Arabian Gulf University', pdfWidth / 2, 20, { align: 'center' });

                                  pdf.setFontSize(14);
                                  pdf.text('Student Report', pdfWidth / 2, 30, { align: 'center' });

                                  // Add generated date
                                  pdf.setFontSize(10);
                                  pdf.setFont('helvetica', 'normal');
                                  pdf.text(`Generated on: ${new Date().toLocaleDateString()}`, pdfWidth / 2, 40, { align: 'center' });

                                  // Adjust image position to account for header
                                  const imgWidth = pdfWidth - 20;
                                  const imgHeight = canvas.height * imgWidth / canvas.width;
                                  const startY = 50; // Start below header

                                  pdf.addImage(imgData, 'JPEG', 10, startY, imgWidth, imgHeight);

                                  // Handle multi-page if needed
                                  let heightLeft = imgHeight;
                                  let position = startY;
                                  heightLeft -= (pdfHeight - startY - 10);

                                  while (heightLeft > 0) {
                                      position = -(imgHeight - heightLeft - startY);
                                      pdf.addPage();

                                      // Add header to each page
                                      pdf.setFontSize(16);
                                      pdf.setFont('helvetica', 'bold');
                                      pdf.text('Arabian Gulf University', pdfWidth / 2, 20, { align: 'center' });
                                      pdf.setFontSize(14);
                                      pdf.text('Student Report (Continued)', pdfWidth / 2, 30, { align: 'center' });

                                      pdf.addImage(imgData, 'JPEG', 10, position, imgWidth, imgHeight);
                                      heightLeft -= (pdfHeight - startY - 10);
                                  }

                                  pdf.save(filename);
                                  showNotification('Student PDF download started.', 'success');
                              } catch (pdfError) {
                                  console.error('Error creating PDF in Approach 2:', pdfError);
                                  showNotification('Error creating PDF. Please try using the Print button instead.', 'error');
                              }

                              cleanup();
                          }).catch(err => {
                              console.error('html2canvas error in Approach 2:', err);
                              hiddenElements.forEach(el => el.style.display = '');
                              showNotification('Could not generate PDF. Please try using the Print button instead.', 'error');
                              cleanup();
                          });
                      }, 1000);
                  }
              } catch (error) {
                  console.error('Unexpected error in PDF generation:', error);
                  showNotification('An unexpected error occurred. Please try using the Print button instead.', 'error');
                  if (document.body.contains(loadingIndicator)) {
                      document.body.removeChild(loadingIndicator);
                  }
              }
          });
      }

      // Function to generate PDF for department details table
      window.generateDepartmentPDF = function() {
          loadPdfLibs(() => {
              const { jsPDF } = window.jspdf;
              const tableContainer = document.getElementById('departmentDetailsTableContainer'); // Use the container div
              if (!tableContainer) {
                  showNotification('Department details table not found for PDF generation.', 'error');
                  return;
              }
               showNotification('Generating Department PDF...', 'info');

              // Temporarily remove print:hidden for PDF generation if needed
              const hiddenElements = tableContainer.querySelectorAll('.print\\:hidden');
              hiddenElements.forEach(el => el.style.display = 'none');

              html2canvas(tableContainer, { // Capture the container including title and buttons (which will be hidden)
                  scale: 2,
                  useCORS: true,
                  logging: false,
                  backgroundColor: '#ffffff'
              }).then(canvas => {
                  hiddenElements.forEach(el => el.style.display = ''); // Restore visibility

                  const imgData = canvas.toDataURL('image/png');
                  const pdf = new jsPDF({
                      orientation: 'landscape', // Landscape might be better for tables
                      unit: 'mm',
                      format: 'a4'
                  });

                  const pdfWidth = pdf.internal.pageSize.getWidth();
                  const pdfHeight = pdf.internal.pageSize.getHeight();

                  // Add AGU header
                  pdf.setFontSize(16);
                  pdf.setFont('helvetica', 'bold');
                  pdf.text('Arabian Gulf University', pdfWidth / 2, 20, { align: 'center' });

                  pdf.setFontSize(14);
                  pdf.text('Department Details Report', pdfWidth / 2, 30, { align: 'center' });

                  // Add generated date
                  pdf.setFontSize(10);
                  pdf.setFont('helvetica', 'normal');
                  pdf.text(`Generated on: ${new Date().toLocaleDateString()}`, pdfWidth / 2, 40, { align: 'center' });

                  // Adjust image position to account for header
                  const imgWidth = pdfWidth - 20; // Margin
                  const imgHeight = canvas.height * imgWidth / canvas.width;
                  let heightLeft = imgHeight;
                  let position = 50; // Start below header

                  pdf.addImage(imgData, 'PNG', 10, position, imgWidth, imgHeight);
                  heightLeft -= (pdfHeight - 50);

                  while (heightLeft > 0) {
                      position = heightLeft - imgHeight + 50;
                      pdf.addPage();

                      // Add header to each page
                      pdf.setFontSize(16);
                      pdf.setFont('helvetica', 'bold');
                      pdf.text('Arabian Gulf University', pdfWidth / 2, 20, { align: 'center' });
                      pdf.setFontSize(14);
                      pdf.text('Department Details Report (Continued)', pdfWidth / 2, 30, { align: 'center' });

                      pdf.addImage(imgData, 'PNG', 10, position, imgWidth, imgHeight);
                      heightLeft -= (pdfHeight - 50);
                  }

                  // Get student name for filename if available
                  const studentNameElement = document.querySelector('#studentReportSection h4');
                  const studentName = studentNameElement ? studentNameElement.textContent : 'student';
                  const sanitizedName = studentName.replace(/[^a-z0-9]/gi, '_').toLowerCase();

                  pdf.save(`${sanitizedName}_department_details_${new Date().toISOString().slice(0, 10)}.pdf`);
                  showNotification('Department PDF download started.', 'info');

              }).catch(err => {
                  console.error("html2canvas error:", err);
                  showNotification('Error generating Department PDF.', 'error');
                  hiddenElements.forEach(el => el.style.display = ''); // Restore visibility on error
              });
          });
      }

      // Export Student Logs Functionality
      const exportPdfBtn = document.getElementById('exportPdfBtn');
      const exportExcelBtn = document.getElementById('exportExcelBtn');

      // Function to show loading state for export buttons
      function showExportLoading(button) {
          const exportText = button.querySelector('.export-text');
          const loadingText = button.querySelector('.loading-text');
          exportText.classList.add('hidden');
          loadingText.classList.remove('hidden');
          button.disabled = true;
      }

      // Function to hide loading state for export buttons
      function hideExportLoading(button) {
          const exportText = button.querySelector('.export-text');
          const loadingText = button.querySelector('.loading-text');
          exportText.classList.remove('hidden');
          loadingText.classList.add('hidden');
          button.disabled = false;
      }

      // Function to handle student logs export
      function handleStudentLogsExport(format, button) {
          showExportLoading(button);

          const year = document.getElementById('export_year').value;
          const section = document.getElementById('export_section').value;

          // Build URL parameters
          const params = new URLSearchParams();
          params.set('format', format);

          if (year) params.set('years', year);
          if (section) params.set('department', section);

          // Create export URL
          const exportUrl = `{% url "admin_section:export_department_logs" %}?${params.toString()}`;

          // Trigger download
          const link = document.createElement('a');
          link.href = exportUrl;
          link.style.display = 'none';
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);

          // Hide loading state after a short delay
          setTimeout(() => {
              hideExportLoading(button);
              showNotification(`${format.toUpperCase()} export started successfully!`, 'success');
          }, 1500);
      }

      // Event listeners for export buttons
      if (exportPdfBtn) {
          exportPdfBtn.addEventListener('click', function() {
              handleStudentLogsExport('pdf', this);
          });
      }

      if (exportExcelBtn) {
          exportExcelBtn.addEventListener('click', function() {
              handleStudentLogsExport('excel', this);
          });
      }

      // Initialize all charts on page load
      initializeAllCharts();

    }); // End DOMContentLoaded
  </script>
{% endblock %}