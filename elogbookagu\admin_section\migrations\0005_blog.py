# Generated by Django 5.1.4 on 2025-05-12 06:24

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('admin_section', '0004_daterestrictionsettings'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Blog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200)),
                ('content', models.TextField()),
                ('summary', models.CharField(help_text='A brief summary of the blog post (max 300 characters)', max_length=300)),
                ('category', models.CharField(choices=[('news', 'News'), ('announcement', 'Announcement'), ('feature', 'Feature'), ('update', 'Update')], default='news', max_length=20)),
                ('attachment', models.FileField(blank=True, null=True, upload_to='blog_attachments/')),
                ('attachment_name', models.CharField(blank=True, help_text='Name to display for the attachment', max_length=100)),
                ('featured_image', models.ImageField(blank=True, null=True, upload_to='blog_images/')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_published', models.BooleanField(default=True)),
                ('author', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='blogs', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Blog Post',
                'verbose_name_plural': 'Blog Posts',
                'ordering': ['-created_at'],
            },
        ),
    ]
