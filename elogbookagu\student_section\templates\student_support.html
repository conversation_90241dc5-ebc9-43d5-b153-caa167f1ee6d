{% extends 'base.html' %}

{% block title %}
  Student Support
{% endblock %}

{% block navbar %}
  {% include './components/student_auth_navbar.html' %}
{% endblock %}

<!-- Main Content -->

{% block content %}
  <div class="container mx-auto px-4 py-8">
    <div class="text-center mb-8">
      <h2 class="text-3xl font-bold text-gray-800 dark:text-white">Student Support Center</h2>
      <p class="text-gray-600 dark:text-gray-300 mt-2">Submit your issues or questions and we'll help you resolve them</p>
    </div>

    <!-- Messages section -->
    {% if messages %}
      <div class="mb-6">
        {% for message in messages %}
          <div class="p-4 mb-4 rounded-lg {% if message.tags == 'success' %}bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100{% elif message.tags == 'error' %}bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100{% else %}bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100{% endif %}">
            {{ message }}
          </div>
        {% endfor %}
      </div>
    {% endif %}

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <!-- Support Form -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <h3 class="text-xl font-semibold mb-4 text-gray-800 dark:text-white">Submit a Support Request</h3>
        <form method="post" class="space-y-4" id="supportForm">
          {% csrf_token %}
          <div>
            <label for="id_subject" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Subject</label>
            {{ form.subject }}
          </div>
          <div>
            <label for="id_description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Description</label>
            {{ form.description }}
          </div>
          <div class="pt-2">
            <button type="submit" id="submitButton" class="w-full py-2 px-4 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg shadow-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-75 transition-colors duration-200">
              Submit Request
            </button>
            <div id="loadingIndicator" class="hidden mt-2 text-center">
              <div class="inline-block animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-blue-500"></div>
              <span class="ml-2 text-sm text-gray-600 dark:text-gray-400">Processing your request...</span>
            </div>
          </div>
        </form>
      </div>

      <!-- Support Tickets Table -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <h3 class="text-xl font-semibold mb-4 text-gray-800 dark:text-white">Your Support Tickets</h3>

        {% if tickets %}
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead class="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Subject</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Date</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {% for ticket in tickets %}
                  <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">{{ ticket.subject }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">{{ ticket.date_created|date:"M d, Y" }}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {% if ticket.status == 'solved' %}bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100{% else %}bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100{% endif %}">
                        {{ ticket.status|title }}
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                      {% if ticket.status == 'pending' %}
                        <a href="{% url 'student_section:delete_support_ticket' ticket.id %}" class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300" onclick="return confirm('Are you sure you want to delete this ticket?')">
                          Delete
                        </a>
                      {% endif %}

                      {% if ticket.status == 'solved' and ticket.admin_comments %}
                        <button class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300" onclick="toggleComments('comments-{{ ticket.id }}')">
                          View Response
                        </button>
                      {% endif %}
                    </td>
                  </tr>
                  {% if ticket.status == 'solved' and ticket.admin_comments %}
                    <tr id="comments-{{ ticket.id }}" class="hidden bg-gray-50 dark:bg-gray-700">
                      <td colspan="4" class="px-6 py-4 text-sm text-gray-500 dark:text-gray-300">
                        <p class="font-semibold mb-1">Admin Response:</p>
                        <p>{{ ticket.admin_comments }}</p>
                        <p class="text-xs mt-2">Resolved on: {{ ticket.resolved_date|date:"M d, Y H:i" }}</p>
                      </td>
                    </tr>
                  {% endif %}
                {% endfor %}
              </tbody>
            </table>
          </div>
        {% else %}
          <p class="text-gray-500 dark:text-gray-400 text-center py-4">You haven't submitted any support tickets yet.</p>
        {% endif %}
      </div>
    </div>
  </div>

  <script>
    function toggleComments(id) {
      const commentsRow = document.getElementById(id);
      if (commentsRow.classList.contains('hidden')) {
        commentsRow.classList.remove('hidden');
      } else {
        commentsRow.classList.add('hidden');
      }
    }

    // Form submission handling with loading indicator
    document.addEventListener('DOMContentLoaded', function() {
      const form = document.getElementById('supportForm');
      const submitButton = document.getElementById('submitButton');
      const loadingIndicator = document.getElementById('loadingIndicator');

      if (form) {
        form.addEventListener('submit', function() {
          // Disable the submit button to prevent multiple submissions
          submitButton.disabled = true;
          submitButton.classList.add('opacity-50', 'cursor-not-allowed');

          // Show the loading indicator
          loadingIndicator.classList.remove('hidden');
        });
      }
    });
  </script>
{% endblock %}
