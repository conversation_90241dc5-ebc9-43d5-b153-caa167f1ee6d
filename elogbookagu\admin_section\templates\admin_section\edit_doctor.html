{% extends 'base.html' %}

{% block title %}
  Edit Doctor
{% endblock %}

{% block navbar %}
{% include 'components/admin_auth_navbar.html' %}
{% endblock %}

<!-- Main Content -->

{% block content %}
  <div class="container mx-auto px-4 py-8">
    <div class="flex items-center justify-between mb-8">
      <div>
        <h2 class="text-3xl font-bold text-gray-800 dark:text-white">Edit Doctor</h2>
        <p class="text-gray-600 dark:text-gray-300 mt-1">Update doctor information and department assignments</p>
      </div>
      <a href="{% url 'admin_section:add_doctor' %}" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600 transition-colors duration-200 flex items-center">
        <i class="fas fa-arrow-left mr-2"></i> Back to Doctors
      </a>
    </div>

    <!-- Success/Error Messages -->
    {% if messages %}
      <div class="mb-6">
        {% for message in messages %}
          <div class="p-4 mb-4 rounded-lg {% if message.tags == 'success' %}bg-green-100 text-green-800 dark:bg-green-200 dark:text-green-900{% elif message.tags == 'error' %}bg-red-100 text-red-800 dark:bg-red-200 dark:text-red-900{% elif message.tags == 'warning' %}bg-yellow-100 text-yellow-800 dark:bg-yellow-200 dark:text-yellow-900{% else %}bg-blue-100 text-blue-800 dark:bg-blue-200 dark:text-blue-900{% endif %}">
            {{ message }}
          </div>
        {% endfor %}
      </div>
    {% endif %}

    <!-- Edit Form -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6 max-w-4xl mx-auto">
      <form method="POST" action="{% url 'admin_section:edit_doctor' doctor.id %}">
        {% csrf_token %}

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- User Form Fields -->
          <div class="space-y-6">
            <h3 class="text-xl font-semibold text-gray-800 dark:text-white border-b pb-2">Personal Information</h3>

            <div>
              <label for="{{ user_form.username.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Username</label>
              {{ user_form.username }}
              {% if user_form.username.errors %}
                <p class="text-red-500 text-sm mt-1">{{ user_form.username.errors.0 }}</p>
              {% endif %}
            </div>

            <div>
              <label for="{{ user_form.email.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Email</label>
              {{ user_form.email }}
              {% if user_form.email.errors %}
                <p class="text-red-500 text-sm mt-1">{{ user_form.email.errors.0 }}</p>
              {% endif %}
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label for="{{ user_form.first_name.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">First Name</label>
                {{ user_form.first_name }}
                {% if user_form.first_name.errors %}
                  <p class="text-red-500 text-sm mt-1">{{ user_form.first_name.errors.0 }}</p>
                {% endif %}
              </div>
              <div>
                <label for="{{ user_form.last_name.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Last Name</label>
                {{ user_form.last_name }}
                {% if user_form.last_name.errors %}
                  <p class="text-red-500 text-sm mt-1">{{ user_form.last_name.errors.0 }}</p>
                {% endif %}
              </div>
            </div>

            <div>
              <label for="{{ user_form.speciality.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Speciality</label>
              {{ user_form.speciality }}
              {% if user_form.speciality.errors %}
                <p class="text-red-500 text-sm mt-1">{{ user_form.speciality.errors.0 }}</p>
              {% endif %}
            </div>
          </div>

          <div class="space-y-6">
            <h3 class="text-xl font-semibold text-gray-800 dark:text-white border-b pb-2">Contact & Professional Details</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label for="{{ user_form.phone_no.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Phone Number</label>
                {{ user_form.phone_no }}
                {% if user_form.phone_no.errors %}
                  <p class="text-red-500 text-sm mt-1">{{ user_form.phone_no.errors.0 }}</p>
                {% endif %}
              </div>
              <div>
                <label for="{{ user_form.city.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">City</label>
                {{ user_form.city }}
                {% if user_form.city.errors %}
                  <p class="text-red-500 text-sm mt-1">{{ user_form.city.errors.0 }}</p>
                {% endif %}
              </div>
            </div>

            <div>
              <label for="{{ user_form.country.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Country</label>
              {{ user_form.country }}
              {% if user_form.country.errors %}
                <p class="text-red-500 text-sm mt-1">{{ user_form.country.errors.0 }}</p>
              {% endif %}
            </div>

            <div>
              <label for="{{ user_form.bio.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Bio</label>
              {{ user_form.bio }}
              {% if user_form.bio.errors %}
                <p class="text-red-500 text-sm mt-1">{{ user_form.bio.errors.0 }}</p>
              {% endif %}
            </div>

            <!-- Doctor Form Fields -->
            <div>
              <label for="{{ doctor_form.departments.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Departments</label>
              {{ doctor_form.departments }}
              {% if doctor_form.departments.errors %}
                <p class="text-red-500 text-sm mt-1">{{ doctor_form.departments.errors.0 }}</p>
              {% endif %}
              <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Hold Ctrl/Cmd to select multiple departments</p>
            </div>
          </div>
        </div>

        <div class="flex justify-end mt-8 space-x-4">
          <a href="{% url 'admin_section:add_doctor' %}" class="px-6 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600 transition-colors duration-200">
            <i class="fas fa-times mr-1"></i> Cancel
          </a>
          <button type="submit" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200">
            <i class="fas fa-save mr-1"></i> Update Doctor
          </button>
        </div>
      </form>
    </div>
  </div>
{% endblock %}
