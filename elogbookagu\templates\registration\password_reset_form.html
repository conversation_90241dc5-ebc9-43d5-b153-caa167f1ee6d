{% extends 'base.html' %}

{% block title %}Reset Your Password - MedLogBook{% endblock %}

{% block extra_head %}
  <style>
    @keyframes float {
      0% { transform: translateY(0px); }
      50% { transform: translateY(-10px); }
      100% { transform: translateY(0px); }
    }

    .animate-float {
      animation: float 6s ease-in-out infinite;
    }

    .bg-pattern {
      background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    }

    .form-input:focus {
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.25);
    }
  </style>
{% endblock %}

{% block navbar %}
  {% include 'components/public_navbar.html' %}
{% endblock %}

{% block content_container %}
  <!-- Full-width Reset Password Section -->
  <section class="w-full min-h-screen bg-gradient-to-r from-blue-600 to-purple-700 dark:from-gray-800 dark:to-gray-900 bg-pattern py-16">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8 flex justify-center items-center">
      <div class="max-w-md w-full">
        <!-- Reset Password Card -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-2xl overflow-hidden">
          <!-- Card Header with Icon -->
          <div class="bg-blue-50 dark:bg-blue-900/30 p-6 text-center">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-blue-100 dark:bg-blue-800 rounded-full mb-4 animate-float">
              <i class="fas fa-key text-blue-600 dark:text-blue-400 text-2xl"></i>
            </div>
            <h2 class="text-2xl font-bold text-gray-900 dark:text-white">Reset Your Password</h2>
            <p class="mt-2 text-gray-600 dark:text-gray-300">Enter your email address and we'll send you a link to reset your password.</p>
          </div>

          <!-- Form Section -->
          <div class="p-6">
            {% if form.errors %}
              <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded-lg mb-6">
                <p class="font-medium">Please correct the errors below:</p>
                <ul class="list-disc list-inside text-sm mt-2">
                  {% for field in form %}
                    {% for error in field.errors %}
                      <li>{{ field.label }}: {{ error }}</li>
                    {% endfor %}
                  {% endfor %}
                  {% for error in form.non_field_errors %}
                    <li>{{ error }}</li>
                  {% endfor %}
                </ul>
              </div>
            {% endif %}

            <form method="post" class="space-y-6">
              {% csrf_token %}

              <!-- Email Field -->
              <div>
                <label for="id_email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Email Address</label>
                <div class="relative">
                  <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i class="fas fa-envelope text-gray-400"></i>
                  </div>
                  <input type="email" name="email" id="id_email" required
                         class="form-input w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:border-blue-500 dark:focus:border-blue-500"
                         placeholder="<EMAIL>">
                </div>
                <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">Enter the email address you used to register.</p>
              </div>

              <!-- Submit Button -->
              <div>
                <button type="submit" class="w-full flex justify-center items-center px-4 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg shadow-md transition duration-200 transform hover:translate-y-[-1px]">
                  <i class="fas fa-paper-plane mr-2"></i> Send Reset Link
                </button>
              </div>
            </form>

            <!-- Back to Login Link -->
            <div class="mt-6 text-center">
              <a href="{% url 'login' %}" class="text-sm font-medium text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300 flex items-center justify-center">
                <i class="fas fa-arrow-left mr-2"></i> Back to Login
              </a>
            </div>
          </div>

          <!-- Security Note -->
          <div class="px-6 pb-6">
            <div class="border-t border-gray-200 dark:border-gray-700 pt-4 mt-2">
              <div class="flex items-start">
                <div class="flex-shrink-0 mt-1">
                  <i class="fas fa-shield-alt text-gray-400"></i>
                </div>
                <p class="ml-3 text-xs text-gray-500 dark:text-gray-400">
                  For security reasons, we do not store your password. The reset link will expire after 24 hours.
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- Help Text -->
        <div class="mt-6 text-center">
          <p class="text-sm text-white/80">
            Need help? <a href="#" class="font-medium text-white hover:text-white/90 underline">Contact Support</a>
          </p>
        </div>
      </div>
    </div>
  </section>
{% endblock %}
