/* Sidebar transitions */
#sidebar {
  transition: transform 0.3s ease-in-out;
}

#sidebar-backdrop {
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
}

#sidebar-backdrop.opacity-100 {
  opacity: 1;
}

/* Content spacing */
.container {
  padding-top: 4rem; /* Height of fixed header */
}

@media (min-width: 768px) {
  #sidebar {
    transform: translateX(0%) !important;
  }

  .container {
    margin-left: 18rem !important; /* Width of sidebar */
    width: calc(100% - 18rem) !important;
  }
}

/* Form elements styling */
input,
select,
textarea {
  color: #000 !important;
  background-color: #fff !important;
  padding: 0.5rem;
  border: 1px solid #d1d5db !important;
  border-radius: 0.375rem !important;
}

.dark input,
.dark select,
.dark textarea {
  color: #fff !important;
  background-color: #374151 !important;
  border: 1px solid #4b5563 !important;
}

/* Focus states for better accessibility */
button:focus, a:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.4);
}

/* Sidebar styling */
#sidebar {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Navbar styling */
.navbar-icon {
  transition: all 0.2s ease-in-out;
}

.navbar-icon:hover {
  transform: scale(1.1);
}

/* Notification badge */
.notification-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 20px;
  width: 20px;
  border-radius: 9999px;
  background-color: #ef4444;
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
}

/* Menu item hover effects */
.menu-item {
  transition: all 0.2s ease;
}

.menu-item:hover {
  background-color: rgba(99, 102, 241, 0.1);
}

.menu-item:hover .menu-icon {
  color: #4f46e5;
}

/* Logout button styling */
.logout-button {
  transition: all 0.2s ease;
}

.logout-button:hover {
  background-color: #ef4444;
  color: white;
}

.logout-button:hover .logout-icon {
  color: white;
}
