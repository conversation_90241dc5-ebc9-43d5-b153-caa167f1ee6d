{% extends 'base.html' %}

{% block title %}
  Manage Students
{% endblock %}

{% block navbar %}
{% include 'components/admin_auth_navbar.html' %}
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
  <!-- Page Header -->
  <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
    <div>
      <h2 class="text-3xl font-bold text-gray-800 dark:text-white">Manage Students</h2>
      <p class="text-gray-600 dark:text-gray-400 mt-1">Add, edit, and manage student records</p>
    </div>
    <div class="flex gap-3">
      <a href="{% url 'admin_section:student_download_sample_csv' %}" class="flex items-center px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600 transition-colors duration-200">
        <i class="fas fa-download mr-2"></i> Download CSV Template
      </a>
      <a href="{% url 'admin_section:add_user' %}" class="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200">
        <i class="fas fa-plus-circle mr-2"></i> Add Student
      </a>
    </div>
  </div>

  <!-- Forms Section (Hidden) - Now we redirect to add_user.html instead -->
  <div id="formSection" class="hidden mb-8">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <!-- Add Individual Student Form -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 border-t-4 border-blue-500">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-xl font-semibold text-gray-800 dark:text-white">Add New Student</h3>
        <span class="text-sm text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">Individual</span>
      </div>
      <form id="addStudentForm" method="POST" action="{% url 'admin_section:add_student' %}" class="space-y-4">
        {% csrf_token %}
        <input type="hidden" name="add_student" value="1">
        <!-- Debug info -->
        <div class="hidden">
          <p>Form action: {% url 'admin_section:add_student' %}</p>
          <p>CSRF Token: {{ csrf_token }}</p>
        </div>

        <!-- User Form Fields -->
        <div class="space-y-4">
          {% if user_form.errors or student_form.errors %}
          <div class="p-4 mb-4 bg-red-50 dark:bg-red-900 rounded-lg">
            <p class="text-sm text-red-800 dark:text-red-200 font-medium">
              <i class="fas fa-exclamation-triangle mr-2"></i> Please correct the errors below
            </p>
            {% if user_form.non_field_errors %}
              <ul class="mt-2 list-disc pl-5 text-sm text-red-700 dark:text-red-300">
                {% for error in user_form.non_field_errors %}
                  <li>{{ error }}</li>
                {% endfor %}
              </ul>
            {% endif %}
            {% if student_form.non_field_errors %}
              <ul class="mt-2 list-disc pl-5 text-sm text-red-700 dark:text-red-300">
                {% for error in student_form.non_field_errors %}
                  <li>{{ error }}</li>
                {% endfor %}
              </ul>
            {% endif %}
          </div>
          {% endif %}

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label for="{{ user_form.first_name.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">First Name</label>
              {{ user_form.first_name }}
              {% if user_form.first_name.errors %}
                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ user_form.first_name.errors.0 }}</p>
              {% endif %}
            </div>
            <div>
              <label for="{{ user_form.last_name.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Last Name</label>
              {{ user_form.last_name }}
              {% if user_form.last_name.errors %}
                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ user_form.last_name.errors.0 }}</p>
              {% endif %}
            </div>
          </div>

          <div>
            <label for="{{ user_form.email.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Email</label>
            {{ user_form.email }}
            {% if user_form.email.errors %}
              <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ user_form.email.errors.0 }}</p>
            {% endif %}
          </div>

          <div>
            <label for="{{ user_form.username.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Username</label>
            {{ user_form.username }}
            {% if user_form.username.errors %}
              <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ user_form.username.errors.0 }}</p>
            {% endif %}
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label for="{{ user_form.password1.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Password</label>
              {{ user_form.password1 }}
              {% if user_form.password1.errors %}
                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ user_form.password1.errors.0 }}</p>
              {% endif %}
            </div>
            <div>
              <label for="{{ user_form.password2.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Confirm Password</label>
              {{ user_form.password2 }}
              {% if user_form.password2.errors %}
                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ user_form.password2.errors.0 }}</p>
              {% endif %}
            </div>
          </div>

          <div>
            <label for="{{ student_form.student_id.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Student ID</label>
            {{ student_form.student_id }}
            {% if student_form.student_id.errors %}
              <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ student_form.student_id.errors.0 }}</p>
            {% endif %}
          </div>

          <div>
            <label for="{{ student_form.group.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Group (Optional)</label>
            <select name="{{ student_form.group.html_name }}" id="{{ student_form.group.id_for_label }}" class="w-full px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
              <option value="">---------</option>
              {% for value, text in student_form.group.field.choices %}
                {% if value %}
                  <option value="{{ value }}" {% if student_form.group.value == value %}selected{% endif %}>{{ text }}</option>
                {% endif %}
              {% endfor %}
            </select>
            {% if student_form.group.errors %}
              <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ student_form.group.errors.0 }}</p>
            {% endif %}
          </div>
        </div>

        <button type="button" onclick="document.getElementById('addStudentForm').submit();" class="w-full mt-6 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 cursor-pointer font-medium">
          <i class="fas fa-user-plus mr-2"></i> Add Student
        </button>
      </form>
    </div>

    <!-- Bulk Upload Form -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 border-t-4 border-green-500">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-xl font-semibold text-gray-800 dark:text-white">Bulk Upload Students</h3>
        <span class="text-sm text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">Multiple</span>
      </div>
      <form id="bulkUploadForm" method="POST" action="{% url 'admin_section:add_student' %}" enctype="multipart/form-data" class="space-y-4">
        {% csrf_token %}
        <input type="hidden" name="bulk_upload" value="1">

        <div class="p-4 bg-blue-50 dark:bg-blue-900 rounded-lg mb-4">
          <p class="text-sm text-blue-800 dark:text-blue-200">
            <i class="fas fa-info-circle mr-2"></i> Upload a CSV file with student information. Make sure to follow the template format.
          </p>
        </div>

        <div>
          <label for="{{ bulk_form.csv_file.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">CSV File</label>
          <input type="file" name="{{ bulk_form.csv_file.html_name }}" id="{{ bulk_form.csv_file.id_for_label }}" accept=".csv" class="w-full px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 dark:file:bg-blue-900 dark:file:text-blue-200 hover:file:bg-blue-100 dark:hover:file:bg-blue-800">
          <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">{{ bulk_form.csv_file.help_text }}</p>
        </div>

        <div class="flex items-center mt-4">
          <a href="{% url 'admin_section:student_download_sample_csv' %}" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm flex items-center">
            <i class="fas fa-download mr-1"></i> Download Sample CSV
          </a>
        </div>

        <button type="button" onclick="document.getElementById('bulkUploadForm').submit();" class="w-full mt-6 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors duration-200 cursor-pointer font-medium">
          <i class="fas fa-file-upload mr-2"></i> Upload Students
        </button>
      </form>
    </div>
    </div>
  </div>

  <!-- Students List Section -->
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow">
    <!-- Search and Filter -->
    <div class="p-4 border-b border-gray-200 dark:border-gray-700">
      <form method="GET" class="space-y-4">
        <div class="flex flex-col md:flex-row gap-4">
          <!-- Group Filter -->
          <div class="w-full md:w-1/4">
            <label for="group-filter" class="block text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">Filter by Group</label>
            <select id="group-filter" name="group" class="w-full px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
              <option value="">All Groups</option>
              {% for group in groups %}
                <option value="{{ group.id }}" {% if selected_group == group.id|stringformat:"s" %}selected{% endif %}>
                  {{ group.group_name }}
                </option>
              {% endfor %}
            </select>
          </div>

          <!-- Search Input -->
          <div class="flex-1">
            <label for="search-input" class="block text-xs font-medium text-gray-500 dark:text-gray-400 mb-1">Search Students</label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <i class="fas fa-search text-gray-400"></i>
              </div>
              <input id="search-input" type="text" name="q" value="{{ search_query }}"
                     class="w-full pl-10 pr-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                     placeholder="Search by student ID, email or name">
            </div>
          </div>

          <div class="md:self-end">
            <button type="submit" class="w-full md:w-auto px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 flex items-center justify-center">
              <i class="fas fa-search mr-2"></i> Search
            </button>
          </div>
        </div>

        {% if search_query or selected_group %}
        <div class="flex items-center justify-between">
          <div class="text-sm text-gray-600 dark:text-gray-400">
            <span class="font-medium">{{ students.paginator.count }}</span> students found
            {% if search_query %}<span class="ml-1">for "{{ search_query }}"</span>{% endif %}
            {% if selected_group %}<span class="ml-1">in selected group</span>{% endif %}
          </div>
          <a href="{% url 'admin_section:add_student' %}" class="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
            <i class="fas fa-times-circle mr-1"></i> Clear filters
          </a>
        </div>
        {% endif %}
      </form>
    </div>

    <!-- Students Table -->
    <div class="overflow-x-auto">
      <table class="w-full">
        <thead class="bg-gray-50 dark:bg-gray-700">
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              <div class="flex items-center">
                <span>Student ID</span>
                <i class="fas fa-sort ml-1 text-gray-400"></i>
              </div>
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              <div class="flex items-center">
                <span>Name</span>
                <i class="fas fa-sort ml-1 text-gray-400"></i>
              </div>
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              <div class="flex items-center">
                <span>Email</span>
                <i class="fas fa-sort ml-1 text-gray-400"></i>
              </div>
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              <div class="flex items-center">
                <span>Group</span>
                <i class="fas fa-sort ml-1 text-gray-400"></i>
              </div>
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
              Actions
            </th>
          </tr>
        </thead>
        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
          {% for student in students %}
            <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150">
              <td class="px-6 py-4 whitespace-nowrap font-medium text-gray-900 dark:text-white">{{ student.student_id }}</td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="flex-shrink-0 h-10 w-10 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center text-gray-500 dark:text-gray-300">
                    <span class="text-sm font-medium">{{ student.user.first_name|slice:"0:1" }}{{ student.user.last_name|slice:"0:1" }}</span>
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900 dark:text-white">{{ student.user.get_full_name }}</div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">ID: {{ student.student_id }}</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">{{ student.user.email }}</td>
              <td class="px-6 py-4 whitespace-nowrap">
                {% if student.group %}
                  <span class="px-2 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 rounded-full text-xs font-medium">
                    {{ student.group.group_name }}
                  </span>
                {% else %}
                  <span class="px-2 py-1 bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200 rounded-full text-xs font-medium">
                    No Group
                  </span>
                {% endif %}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm">
                <div class="flex space-x-2">
                  <a href="{% url 'admin_section:edit_student' student.id %}"
                     class="p-1.5 bg-blue-50 text-blue-600 rounded hover:bg-blue-100 dark:bg-blue-900/30 dark:text-blue-400 dark:hover:bg-blue-900/50 transition-colors duration-200"
                     title="Edit Student">
                    <i class="fas fa-edit"></i>
                  </a>

                  <a href="{% url 'admin_section:delete_student' student.id %}"
                     class="p-1.5 bg-red-50 text-red-600 rounded hover:bg-red-100 dark:bg-red-900/30 dark:text-red-400 dark:hover:bg-red-900/50 transition-colors duration-200"
                     onclick="return confirm('Are you sure you want to delete this student?')"
                     title="Delete Student">
                    <i class="fas fa-trash"></i>
                  </a>

                  {% if student.group %}
                    <a href="{% url 'admin_section:remove_from_group' student.id %}"
                       class="p-1.5 bg-yellow-50 text-yellow-600 rounded hover:bg-yellow-100 dark:bg-yellow-900/30 dark:text-yellow-400 dark:hover:bg-yellow-900/50 transition-colors duration-200"
                       onclick="return confirm('Are you sure you want to remove this student from their group?')"
                       title="Remove from Group">
                      <i class="fas fa-user-minus"></i>
                    </a>
                  {% endif %}
                </div>
              </td>
            </tr>
          {% empty %}
            <tr>
              <td colspan="5" class="px-6 py-8 text-center">
                <div class="flex flex-col items-center justify-center text-gray-500 dark:text-gray-400">
                  <i class="fas fa-user-slash text-4xl mb-3"></i>
                  <p class="text-lg font-medium">No students found</p>
                  <p class="text-sm mt-1">Try adjusting your search or filter criteria</p>
                </div>
              </td>
            </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>

    <!-- Pagination -->
    {% if students.has_other_pages %}
      <div class="px-6 py-4 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
        <div class="flex flex-col sm:flex-row justify-between items-center">
          <div class="text-sm text-gray-500 dark:text-gray-400 mb-4 sm:mb-0">
            Showing <span class="font-medium">{{ students.start_index }}</span> to <span class="font-medium">{{ students.end_index }}</span> of <span class="font-medium">{{ students.paginator.count }}</span> students
          </div>

          <nav class="flex justify-center">
            <ul class="flex items-center space-x-1">
              {% if students.has_previous %}
                <li>
                  <a href="?page=1{% if search_query %}&q={{ search_query }}{% endif %}{% if selected_group %}&group={{ selected_group }}{% endif %}"
                     class="px-2 py-1 rounded-md bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors duration-200"
                     title="First Page">
                    <i class="fas fa-angle-double-left"></i>
                  </a>
                </li>
                <li>
                  <a href="?page={{ students.previous_page_number }}{% if search_query %}&q={{ search_query }}{% endif %}{% if selected_group %}&group={{ selected_group }}{% endif %}"
                     class="px-3 py-1 rounded-md bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors duration-200">
                    <i class="fas fa-angle-left mr-1"></i> Prev
                  </a>
                </li>
              {% endif %}

              {% for i in students.paginator.page_range %}
                {% if students.number == i %}
                  <li>
                    <span class="px-3 py-1 rounded-md bg-blue-600 text-white font-medium">
                      {{ i }}
                    </span>
                  </li>
                {% elif i > students.number|add:'-3' and i < students.number|add:'3' %}
                  <li>
                    <a href="?page={{ i }}{% if search_query %}&q={{ search_query }}{% endif %}{% if selected_group %}&group={{ selected_group }}{% endif %}"
                       class="px-3 py-1 rounded-md bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors duration-200">
                      {{ i }}
                    </a>
                  </li>
                {% endif %}
              {% endfor %}

              {% if students.has_next %}
                <li>
                  <a href="?page={{ students.next_page_number }}{% if search_query %}&q={{ search_query }}{% endif %}{% if selected_group %}&group={{ selected_group }}{% endif %}"
                     class="px-3 py-1 rounded-md bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors duration-200">
                    Next <i class="fas fa-angle-right ml-1"></i>
                  </a>
                </li>
                <li>
                  <a href="?page={{ students.paginator.num_pages }}{% if search_query %}&q={{ search_query }}{% endif %}{% if selected_group %}&group={{ selected_group }}{% endif %}"
                     class="px-2 py-1 rounded-md bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors duration-200"
                     title="Last Page">
                    <i class="fas fa-angle-double-right"></i>
                  </a>
                </li>
              {% endif %}
            </ul>
          </nav>
        </div>
      </div>
    {% endif %}
  </div>

  <!-- Display Bulk Upload Results -->
  {% if bulk_results %}
    <div class="mt-8 bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <h3 class="text-xl font-semibold mb-4 text-gray-800 dark:text-white">Upload Results</h3>
      <div class="space-y-2">
        <p>Successfully added: {{ bulk_results.success_count }} students</p>
        <p>Failed: {{ bulk_results.error_count }} students</p>
        {% if bulk_results.error_messages %}
          <div class="mt-4">
            <h4 class="font-semibold mb-2">Errors:</h4>
            <ul class="list-disc pl-5 text-red-600">
              {% for error in bulk_results.error_messages %}
                <li>{{ error }}</li>
              {% endfor %}
            </ul>
            {% if bulk_results.total_errors > bulk_results.error_messages|length %}
              <p class="mt-2 text-gray-500">
                (Showing first 10 errors of {{ bulk_results.total_errors }} total)
              </p>
            {% endif %}
          </div>
        {% endif %}
      </div>
    </div>
  {% endif %}
</div>

<!-- Display Messages -->
{% if messages %}
  <div class="fixed top-4 right-4 z-50">
    {% for message in messages %}
      <div class="{% if message.tags == 'success' %}bg-green-500{% elif message.tags == 'error' %}bg-red-500{% else %}bg-blue-500{% endif %} text-white px-6 py-4 rounded-lg shadow-lg mb-4">
        {{ message }}
      </div>
    {% endfor %}
  </div>
{% endif %}
{% endblock %}

{% block scripts %}
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Form section is now hidden by default since we're redirecting to add_user.html
    const formSection = document.getElementById('formSection');
    if (formSection && !formSection.classList.contains('hidden')) {
      formSection.classList.add('hidden');
    }

    // Auto-hide messages after 5 seconds
    const messages = document.querySelectorAll('.fixed.top-4.right-4 > div');
    if (messages.length > 0) {
      setTimeout(function() {
        messages.forEach(function(message) {
          message.style.opacity = '0';
          message.style.transition = 'opacity 0.5s ease-out';
          setTimeout(function() {
            message.remove();
          }, 500);
        });
      }, 5000);
    }
  });
</script>
{% endblock %}