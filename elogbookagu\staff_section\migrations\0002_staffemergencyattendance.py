# Generated by Django 5.1.4 on 2025-06-02 09:19

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0008_alter_staff_departments'),
        ('admin_section', '0008_mappedattendance'),
        ('staff_section', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='StaffEmergencyAttendance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('status', models.CharField(choices=[('present', 'Present'), ('absent', 'Absent')], max_length=10)),
                ('marked_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('notes', models.TextField(blank=True, help_text='Emergency attendance notes')),
                ('is_emergency', models.BooleanField(default=True, help_text='Marks this as emergency attendance')),
                ('department', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='staff_attendances', to='admin_section.department')),
                ('group', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='staff_attendances', to='admin_section.group')),
                ('staff', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='marked_attendances', to='accounts.staff')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='staff_attendances', to='accounts.student')),
                ('training_site', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='staff_attendances', to='admin_section.trainingsite')),
            ],
            options={
                'verbose_name': 'Staff Emergency Attendance',
                'verbose_name_plural': 'Staff Emergency Attendances',
                'ordering': ['-date', '-marked_at'],
                'unique_together': {('student', 'date', 'department')},
            },
        ),
    ]
