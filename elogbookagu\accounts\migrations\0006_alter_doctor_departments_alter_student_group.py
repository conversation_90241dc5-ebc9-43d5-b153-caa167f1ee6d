# Generated by Django 5.1.4 on 2025-03-16 09:12

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0005_rename_phone_customuser_phone_no'),
        ('admin_section', '0002_department_logyear_alter_activitytype_department_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='doctor',
            name='departments',
            field=models.ManyToManyField(blank=True, related_name='doctors', to='admin_section.department'),
        ),
        migrations.AlterField(
            model_name='student',
            name='group',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='students', to='admin_section.group'),
        ),
    ]
