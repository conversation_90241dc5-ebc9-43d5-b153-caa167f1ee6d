{% extends 'base.html' %}

{% block title %}Password Reset Complete - MedLogBook{% endblock %}

{% block extra_head %}
  <style>
    @keyframes float {
      0% { transform: translateY(0px); }
      50% { transform: translateY(-10px); }
      100% { transform: translateY(0px); }
    }

    .animate-float {
      animation: float 6s ease-in-out infinite;
    }

    .bg-pattern {
      background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    }

    @keyframes checkmark {
      0% {
        stroke-dashoffset: 100;
        opacity: 0;
      }
      100% {
        stroke-dashoffset: 0;
        opacity: 1;
      }
    }

    .checkmark {
      stroke-dasharray: 100;
      stroke-dashoffset: 100;
      animation: checkmark 1s ease-in-out forwards;
      animation-delay: 0.5s;
    }

    .checkmark-circle {
      stroke-dasharray: 380;
      stroke-dashoffset: 380;
      animation: checkmark 1s ease-in-out forwards;
    }
  </style>
{% endblock %}

{% block navbar %}
  {% include 'components/public_navbar.html' %}
{% endblock %}

{% block content_container %}
  <!-- Full-width Reset Password Section -->
  <section class="w-full min-h-screen bg-gradient-to-r from-blue-600 to-purple-700 dark:from-gray-800 dark:to-gray-900 bg-pattern py-16">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8 flex justify-center items-center">
      <div class="max-w-md w-full">
        <!-- Success Card -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-2xl overflow-hidden">
          <!-- Card Header with Success Animation -->
          <div class="bg-green-50 dark:bg-green-900/30 p-6 text-center">
            <div class="inline-flex items-center justify-center w-20 h-20 mb-4 animate-float">
              <svg class="w-full h-full" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
                <circle class="checkmark-circle" cx="50" cy="50" r="46" fill="none" stroke="#4ade80" stroke-width="4" />
                <path class="checkmark" d="M28 50 L42 64 L72 36" fill="none" stroke="#4ade80" stroke-width="6" stroke-linecap="round" stroke-linejoin="round" />
              </svg>
            </div>
            <h2 class="text-2xl font-bold text-gray-900 dark:text-white">Password Reset Complete</h2>
            <p class="mt-2 text-gray-600 dark:text-gray-300">Your password has been successfully reset.</p>
          </div>

          <!-- Content Section -->
          <div class="p-6">
            <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 text-blue-700 dark:text-blue-400 px-4 py-3 rounded-lg mb-6">
              <div class="flex">
                <div class="flex-shrink-0">
                  <i class="fas fa-info-circle text-blue-600 dark:text-blue-400 mt-0.5"></i>
                </div>
                <div class="ml-3">
                  <p class="text-sm">
                    Your password has been set. You may go ahead and log in now.
                  </p>
                </div>
              </div>
            </div>

            <div class="space-y-4 text-gray-600 dark:text-gray-300">
              <p>
                Your account is now secure with your new password. Here are some security tips:
              </p>

              <ul class="space-y-2">
                <li class="flex items-start">
                  <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                  <span>Never share your password with anyone</span>
                </li>
                <li class="flex items-start">
                  <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                  <span>Use a unique password for each website</span>
                </li>
                <li class="flex items-start">
                  <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                  <span>Consider using a password manager</span>
                </li>
                <li class="flex items-start">
                  <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                  <span>Change your password periodically</span>
                </li>
              </ul>
            </div>

            <!-- Login Button -->
            <div class="mt-8 text-center">
              <a href="{% url 'login' %}" class="inline-flex items-center justify-center px-6 py-3 border border-transparent rounded-lg shadow-sm text-base font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200">
                <i class="fas fa-sign-in-alt mr-2"></i> Log In Now
              </a>
            </div>
          </div>

          <!-- Support Section -->
          <div class="px-6 pb-6">
            <div class="border-t border-gray-200 dark:border-gray-700 pt-4 mt-2">
              <div class="flex items-start">
                <div class="flex-shrink-0 mt-1">
                  <i class="fas fa-headset text-gray-400"></i>
                </div>
                <p class="ml-3 text-xs text-gray-500 dark:text-gray-400">
                  If you have any questions or need assistance, our support team is here to help.
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- Help Text -->
        <div class="mt-6 text-center">
          <p class="text-sm text-white/80">
            Need help? <a href="#" class="font-medium text-white hover:text-white/90 underline">Contact Support</a>
          </p>
        </div>
      </div>
    </div>
  </section>
{% endblock %}
