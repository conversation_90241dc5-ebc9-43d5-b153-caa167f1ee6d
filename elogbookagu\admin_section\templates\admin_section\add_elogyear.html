{% extends 'base.html' %}

{% block title %}
  Manage Year Sections
{% endblock %}

{% block navbar %}
{% include 'components/admin_auth_navbar.html' %}
{% endblock %}

<!-- Main Content -->

{% block content %}
  <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-10 lg:py-12">
    <!-- <PERSON> Header -->
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
      <div>
        <h1 class="text-2xl sm:text-3xl font-bold text-gray-800 dark:text-gray-100">
          <i class="fas fa-layer-group text-blue-500 mr-2"></i> Year Sections
        </h1>
        <p class="text-gray-600 dark:text-gray-400 mt-1">Manage year sections for student records and activities</p>
      </div>
    </div>

    <!-- Messages section -->
    {% if messages %}
      <div class="fixed top-4 right-4 z-50 w-full max-w-sm">
        {% for message in messages %}
          <div class="p-4 mb-3 rounded-lg shadow-lg flex items-start {% if message.tags == 'success' %}bg-green-100 text-green-800 dark:bg-green-900/70 dark:text-green-200{% elif message.tags == 'error' %}bg-red-100 text-red-800 dark:bg-red-900/70 dark:text-red-200{% else %}bg-blue-100 text-blue-800 dark:bg-blue-900/70 dark:text-blue-200{% endif %} transform transition-all duration-300 animate-fade-in-down">
            <div class="flex-shrink-0">
              <i class="fas {% if message.tags == 'success' %}fa-check-circle text-green-500{% elif message.tags == 'error' %}fa-exclamation-circle text-red-500{% else %}fa-info-circle text-blue-500{% endif %} mt-0.5"></i>
            </div>
            <div class="ml-3 flex-grow">
              <p class="text-sm font-medium">{{ message }}</p>
            </div>
            <button type="button" class="ml-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 close-message">
              <i class="fas fa-times"></i>
            </button>
          </div>
        {% endfor %}
      </div>
    {% endif %}

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Year Section Form -->
      <div class="lg:col-span-1">
        <div class="bg-white dark:bg-gray-800 shadow-lg rounded-lg overflow-hidden border-t-4 {% if is_edit %}border-yellow-500{% else %}border-blue-500{% endif %}">
          <div class="p-6">
            <div class="flex items-center mb-6">
              <div class="flex-shrink-0 mr-4 {% if is_edit %}text-yellow-500{% else %}text-blue-500{% endif %}">
                <i class="fas {% if is_edit %}fa-edit text-2xl{% else %}fa-plus-circle text-2xl{% endif %}"></i>
              </div>
              <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-100">
                {{ is_edit|yesno:'Edit Existing,Add New' }} Year Section
              </h2>
            </div>

            <!-- Form Errors Summary -->
            {% if form.errors or form.non_field_errors %}
            <div class="mb-6 p-4 bg-red-50 dark:bg-red-900/20 rounded-lg border-l-4 border-red-500">
              <div class="flex items-start">
                <div class="flex-shrink-0">
                  <i class="fas fa-exclamation-circle text-red-600 dark:text-red-400"></i>
                </div>
                <div class="ml-3">
                  <h4 class="text-sm font-medium text-red-800 dark:text-red-300">Please correct the following errors:</h4>
                  {% if form.non_field_errors %}
                  <ul class="mt-1 text-sm text-red-700 dark:text-red-300 list-disc list-inside">
                    {% for error in form.non_field_errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                  </ul>
                  {% endif %}
                </div>
              </div>
            </div>
            {% endif %}

            <form method="post" action="{% if is_edit %}{% url 'admin_section:edit_elogyear' year_section.id %}{% else %}{% url 'admin_section:add_elogyear' %}{% endif %}" id="yearSectionForm">
              {% csrf_token %}

              <!-- Academic Year Field -->
              <div class="space-y-1 mb-6">
                <label for="id_year_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Academic Year <span class="text-red-500">*</span>
                </label>
                {{ form.year_name }}
                {% if form.year_name.errors %}
                  <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.year_name.errors.0 }}</p>
                {% else %}
                  <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Select the academic year for this section</p>
                {% endif %}
              </div>

              <!-- Section Name Field -->
              <div class="space-y-1 mb-6">
                <label for="id_year_section_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Section Name <span class="text-red-500">*</span>
                </label>
                {{ form.year_section_name }}
                {% if form.year_section_name.errors %}
                  <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.year_section_name.errors.0 }}</p>
                {% else %}
                  <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Enter 'Year 5' or 'Year 6' only</p>
                {% endif %}
              </div>

              <!-- Form Actions -->
              <div class="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
                {% if is_edit %}
                  <a href="{% url 'admin_section:add_elogyear' %}" class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-800 rounded-lg border border-gray-300 dark:border-gray-600 hover:bg-gray-200 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors duration-200">
                    <i class="fas fa-times mr-1"></i> Cancel
                  </a>
                  <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-yellow-500 rounded-lg hover:bg-yellow-600 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 transition-colors duration-200">
                    <i class="fas fa-save mr-1"></i> Update Section
                  </button>
                {% else %}
                  <button type="reset" class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-800 rounded-lg border border-gray-300 dark:border-gray-600 hover:bg-gray-200 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors duration-200">
                    <i class="fas fa-undo mr-1"></i> Reset
                  </button>
                  <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200">
                    <i class="fas fa-plus-circle mr-1"></i> Add Section
                  </button>
                {% endif %}
              </div>
            </form>
          </div>
        </div>
      </div>

      <!-- Year Sections Table -->
      <div class="lg:col-span-2">
        <div class="bg-white dark:bg-gray-800 shadow-lg rounded-lg overflow-hidden border-t-4 border-green-500">
          <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
            <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-100 flex items-center">
              <i class="fas fa-list-ul text-green-500 mr-2"></i> Year Sections
              <span class="ml-2 px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">{{ year_sections.paginator.count }}</span>
            </h3>
          </div>

          <div class="overflow-x-auto">
            <table id="sections-table" class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead class="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    <div class="flex items-center">
                      <span>ID</span>
                      <i class="fas fa-sort ml-1 text-gray-400"></i>
                    </div>
                  </th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    <div class="flex items-center">
                      <span>Academic Year</span>
                      <i class="fas fa-sort ml-1 text-gray-400"></i>
                    </div>
                  </th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    <div class="flex items-center">
                      <span>Section Name</span>
                      <i class="fas fa-sort ml-1 text-gray-400"></i>
                    </div>
                  </th>
                  <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {% for section in year_sections %}
                  <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150">
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">{{ section.id }}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span class="px-2 py-1 text-xs rounded-full bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                        {{ section.year_name.year_name }}
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm font-medium text-gray-900 dark:text-white">{{ section.year_section_name }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div class="flex justify-end space-x-2">
                        <a href="{% url 'admin_section:edit_elogyear' section.id %}"
                           class="p-1.5 bg-blue-50 text-blue-600 rounded hover:bg-blue-100 dark:bg-blue-900/30 dark:text-blue-400 dark:hover:bg-blue-900/50 transition-colors duration-200"
                           title="Edit Section">
                          <i class="fas fa-edit"></i>
                        </a>
                        <button type="button"
                                class="delete-section-btn p-1.5 bg-red-50 text-red-600 rounded hover:bg-red-100 dark:bg-red-900/30 dark:text-red-400 dark:hover:bg-red-900/50 transition-colors duration-200"
                                data-section-id="{{ section.id }}"
                                data-section-name="{{ section.year_section_name }}"
                                title="Delete Section">
                          <i class="fas fa-trash"></i>
                        </button>
                      </div>
                    </td>
                  </tr>
                {% empty %}
                  <tr>
                    <td colspan="4" class="px-6 py-8 text-center">
                      <div class="flex flex-col items-center justify-center text-gray-500 dark:text-gray-400">
                        <i class="fas fa-layer-group text-4xl mb-3"></i>
                        <p class="text-lg font-medium">No year sections found</p>
                        <p class="text-sm mt-1">Create your first year section using the form</p>
                      </div>
                    </td>
                  </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>

          <!-- Pagination -->
          {% if year_sections.has_other_pages %}
            <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
              <div class="flex flex-col sm:flex-row justify-between items-center">
                <div class="text-sm text-gray-700 dark:text-gray-300 mb-4 sm:mb-0">
                  Showing <span class="font-medium">{{ year_sections.start_index }}</span> to <span class="font-medium">{{ year_sections.end_index }}</span> of <span class="font-medium">{{ year_sections.paginator.count }}</span> sections
                </div>
                <nav class="flex justify-center">
                  <ul class="flex items-center space-x-1">
                    {% if year_sections.has_previous %}
                      <li>
                        <a href="?page=1" class="px-2 py-1 rounded-md bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors duration-200" title="First Page">
                          <i class="fas fa-angle-double-left"></i>
                        </a>
                      </li>
                      <li>
                        <a href="?page={{ year_sections.previous_page_number }}" class="px-3 py-1 rounded-md bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors duration-200">
                          <i class="fas fa-angle-left mr-1"></i> Prev
                        </a>
                      </li>
                    {% endif %}

                    {% for num in year_sections.paginator.page_range %}
                      {% if year_sections.number == num %}
                        <li>
                          <span class="px-3 py-1 rounded-md bg-blue-600 text-white font-medium">
                            {{ num }}
                          </span>
                        </li>
                      {% elif num > year_sections.number|add:-3 and num < year_sections.number|add:3 %}
                        <li>
                          <a href="?page={{ num }}" class="px-3 py-1 rounded-md bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors duration-200">
                            {{ num }}
                          </a>
                        </li>
                      {% endif %}
                    {% endfor %}

                    {% if year_sections.has_next %}
                      <li>
                        <a href="?page={{ year_sections.next_page_number }}" class="px-3 py-1 rounded-md bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors duration-200">
                          Next <i class="fas fa-angle-right ml-1"></i>
                        </a>
                      </li>
                      <li>
                        <a href="?page={{ year_sections.paginator.num_pages }}" class="px-2 py-1 rounded-md bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors duration-200" title="Last Page">
                          <i class="fas fa-angle-double-right"></i>
                        </a>
                      </li>
                    {% endif %}
                  </ul>
                </nav>
              </div>
            </div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>

  <!-- Delete Confirmation Modal -->
  <div id="deleteModal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 transition-opacity duration-300">
    <div class="flex items-center justify-center min-h-screen px-4">
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-md p-6 transform transition-all duration-300 scale-95 opacity-0" id="modalContent">
        <div class="flex items-center mb-4">
          <div class="flex-shrink-0 mr-3 text-red-500">
            <i class="fas fa-exclamation-triangle text-xl"></i>
          </div>
        <h3 class="text-lg font-bold text-gray-900 dark:text-white">Confirm Deletion</h3>
        </div>
        <p class="text-gray-700 dark:text-gray-300 mb-6 pl-8">Are you sure you want to delete the year section <span id="sectionNameToDelete" class="font-semibold text-red-600 dark:text-red-400"></span>? This action cannot be undone.</p>
        <div class="flex justify-end space-x-3">
          <button id="cancelDelete" class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-800 rounded-lg border border-gray-300 dark:border-gray-600 hover:bg-gray-200 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors duration-200">
            <i class="fas fa-times mr-1"></i> Cancel
          </button>
          <form id="deleteForm" method="post">
            {% csrf_token %}
            <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors duration-200">
              <i class="fas fa-trash mr-1"></i> Delete
            </button>
          </form>
        </div>
      </div>
    </div>
  </div>

  <!-- JavaScript for enhanced functionality -->
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Delete confirmation modal functionality
      const deleteModal = document.getElementById('deleteModal');
      const modalContent = document.getElementById('modalContent');
      const sectionNameToDelete = document.getElementById('sectionNameToDelete');
      const deleteForm = document.getElementById('deleteForm');
      const cancelDelete = document.getElementById('cancelDelete');
      const closeMessageBtns = document.querySelectorAll('.close-message');

      // Show delete confirmation modal with animation
      document.querySelectorAll('.delete-section-btn').forEach(button => {
        button.addEventListener('click', function() {
          const sectionId = this.getAttribute('data-section-id');
          const sectionName = this.getAttribute('data-section-name');

          sectionNameToDelete.textContent = sectionName;
          deleteForm.action = `/admin_section/delete-elogyear/${sectionId}/`;

          // Show modal with animation
          deleteModal.classList.remove('hidden');
          setTimeout(() => {
            deleteModal.classList.add('opacity-100');
            modalContent.classList.remove('scale-95', 'opacity-0');
            modalContent.classList.add('scale-100', 'opacity-100');
          }, 10);
        });
      });

      // Hide delete confirmation modal with animation
      function hideModal() {
        modalContent.classList.remove('scale-100', 'opacity-100');
        modalContent.classList.add('scale-95', 'opacity-0');
        deleteModal.classList.remove('opacity-100');

        setTimeout(() => {
          deleteModal.classList.add('hidden');
        }, 300);
      }

      // Hide modal events
      cancelDelete.addEventListener('click', hideModal);

      // Close modal when clicking outside
      deleteModal.addEventListener('click', function(event) {
        if (event.target === deleteModal) {
          hideModal();
        }
      });

      // Close message functionality
      if (closeMessageBtns.length > 0) {
        closeMessageBtns.forEach(btn => {
          btn.addEventListener('click', function() {
            const message = this.closest('.mb-3');
            if (message) {
              message.style.opacity = '0';
              setTimeout(() => {
                message.remove();
              }, 300);
            }
          });
        });

        // Auto-hide messages after 5 seconds
        setTimeout(function() {
          document.querySelectorAll('.fixed.top-4.right-4 .mb-3').forEach(function(message) {
            message.style.opacity = '0';
            setTimeout(function() {
              message.remove();
            }, 300);
          });
        }, 5000);
      }

      // Form validation
      const yearSectionForm = document.getElementById('yearSectionForm');
      const yearNameSelect = document.querySelector('#id_year_name');
      const sectionNameInput = document.querySelector('#id_year_section_name');

      if (yearSectionForm && yearNameSelect && sectionNameInput) {
        yearSectionForm.addEventListener('submit', function(e) {
          let isValid = true;

          // Validate year name
          if (!yearNameSelect.value) {
            isValid = false;
            yearNameSelect.classList.add('border-red-500');

            // Add error message if it doesn't exist
            let errorElement = yearNameSelect.parentNode.querySelector('.error-message');
            if (!errorElement) {
              errorElement = document.createElement('p');
              errorElement.className = 'mt-1 text-sm text-red-600 dark:text-red-400 error-message';
              yearNameSelect.parentNode.appendChild(errorElement);
            }
            errorElement.textContent = 'Please select an academic year';
          }

          // Validate section name
          if (!sectionNameInput.value.trim()) {
            isValid = false;
            sectionNameInput.classList.add('border-red-500');

            // Add error message if it doesn't exist
            let errorElement = sectionNameInput.parentNode.querySelector('.error-message');
            if (!errorElement) {
              errorElement = document.createElement('p');
              errorElement.className = 'mt-1 text-sm text-red-600 dark:text-red-400 error-message';
              sectionNameInput.parentNode.appendChild(errorElement);
            }
            errorElement.textContent = 'Please enter a section name';
          }

          if (!isValid) {
            e.preventDefault();
          }
        });

        // Remove error styling on input
        yearNameSelect.addEventListener('change', function() {
          if (this.value) {
            this.classList.remove('border-red-500');
            const errorElement = this.parentNode.querySelector('.error-message');
            if (errorElement) {
              errorElement.remove();
            }
          }
        });

        sectionNameInput.addEventListener('input', function() {
          if (this.value.trim()) {
            this.classList.remove('border-red-500');
            const errorElement = this.parentNode.querySelector('.error-message');
            if (errorElement) {
              errorElement.remove();
            }
          }
        });
      }
    });
  </script>
{% endblock %}
