{% load static %}
<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="X-UA-Compatible" content="ie=edge" />
    <meta name="description" content="MedLogBook - Medical Education Platform for Arabian Gulf University" />

    <!-- Theme Setup - Apply theme immediately to prevent flash -->
    <script>
      // Apply theme immediately based on localStorage or system preference
      ;(function () {
        const savedTheme = localStorage.getItem('theme')
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
        const isDark = savedTheme === 'dark' || (!savedTheme && prefersDark)
        document.documentElement.classList.toggle('dark', isDark)
      })()
    </script>

    <!-- Preload critical assets -->
    <link rel="preload" href="{% static 'css/main.css' %}" as="style" />
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" as="style" />

    <!-- Styles -->
    <link rel="stylesheet" href="{% static 'css/main.css' %}" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet" />

    <!-- HTMX for dynamic content -->
    <script src="https://unpkg.com/htmx.org@1.9.6" defer></script>

    <!-- Alpine.js for interactive components -->
    <script src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js" defer></script>

    <!-- Page title -->
    <title>{% block title %}MedLogBook{% endblock %}</title>

    {% block extra_head %}{% endblock %}
  </head>

  <body class="bg-white dark:bg-gray-900 text-gray-900 dark:text-white transition-colors duration-300 min-h-screen flex flex-col">
    <!-- Navigation -->
    {% block navbar %}
    <!-- Navigation will be included here -->
    {% endblock %}

    <!-- Main Content -->
    <main class="flex-grow w-full transition-all duration-300">
      {% block content_container %}
      <div class="container mx-auto px-4 sm:px-6 lg:px-8">
        {% block content %}
        <!-- Content will be rendered here -->
        {% endblock %}
      </div>
      {% endblock %}
    </main>

    <!-- Footer -->
    {% include './components/footer.html' %}

    <!-- Core JavaScript -->
    <script>
      // Theme management function
      function updateTheme(isDark) {
        // Update DOM
        document.documentElement.classList.toggle('dark', isDark);
        localStorage.setItem('theme', isDark ? 'dark' : 'light');

        // Update all theme toggle icons across the site
        const darkIcons = document.querySelectorAll('[id^="theme-toggle-dark-icon"]');
        const lightIcons = document.querySelectorAll('[id^="theme-toggle-light-icon"]');
        darkIcons.forEach((icon) => icon.classList.toggle('hidden', !isDark));
        lightIcons.forEach((icon) => icon.classList.toggle('hidden', isDark));

        // Notify other components about theme change
        window.dispatchEvent(new Event('themeChanged'));
      }

      // Toggle theme function used by theme buttons
      function toggleTheme() {
        const isDark = document.documentElement.classList.contains('dark');
        updateTheme(!isDark);
      }

      // Make toggleTheme available globally
      window.toggleTheme = toggleTheme;

      // Ensure theme toggle buttons work on page load
      document.addEventListener('DOMContentLoaded', () => {
        const isDark = document.documentElement.classList.contains('dark');
        updateTheme(isDark); // Sync UI with initial state
      });
    </script>

    <!-- Extra scripts from child templates -->
    {% block extra_scripts %}{% endblock %}
  </body>
</html>
