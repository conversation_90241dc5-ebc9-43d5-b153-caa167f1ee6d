{% extends 'base.html' %}
{% load static %}

{% block title %}Student Report Dashboard{% endblock %}

{% block navbar %}
{% include 'components/admin_auth_navbar.html' %}
{% endblock %}

{% block content %}
<div class="px-4 sm:px-6 lg:px-8 py-8 transition-all duration-300 w-full mx-auto" x-data="{ darkMode: localStorage.getItem('darkMode') === 'true' }" :class="{ 'dark': darkMode }">

  <!-- Header Section -->
  <div class="mb-8">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
      <div>
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white flex items-center">
          <i class="fas fa-user-graduate text-green-600 dark:text-green-400 mr-3"></i>
          Student Report Dashboard
        </h1>
        <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
          Comprehensive analysis of student logs, case types, training sites, and performance metrics.
        </p>
      </div>
      <div class="mt-4 sm:mt-0 flex space-x-3">
        <button onclick="printReport()" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200">
          <i class="fas fa-print mr-2"></i> Print Report
        </button>
        <button onclick="exportReport()" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200">
          <i class="fas fa-download mr-2"></i> Export
        </button>
      </div>
    </div>
  </div>

  <!-- Filters Section -->
  <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg mb-8 print:hidden">
    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
        <i class="fas fa-filter text-green-600 dark:text-green-400 mr-2"></i> Filters
      </h3>
    </div>
    <div class="p-6">
      <form method="GET" id="filterForm">
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <!-- Department Filter -->
          <div class="lg:col-span-1">
            <label for="department" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              <i class="fas fa-building text-blue-500 mr-1"></i> Department
            </label>
            <select name="department" id="department" class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm focus:border-green-500 focus:ring focus:ring-green-500 focus:ring-opacity-50" onchange="this.form.submit()">
              <option value="">All Departments</option>
              {% for dept in departments %}
                <option value="{{ dept.id }}" {% if selected_department == dept.id|stringformat:"s" %}selected{% endif %}>
                  {{ dept.name }}
                </option>
              {% endfor %}
            </select>
          </div>

          <!-- Student Specific Filter -->
          <div class="lg:col-span-1">
            <label for="student" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              <i class="fas fa-user text-green-500 mr-1"></i> Student (Specific)
            </label>
            <div class="relative">
              <select name="student" id="student" class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm focus:border-green-500 focus:ring focus:ring-green-500 focus:ring-opacity-50" onchange="this.form.submit()">
                <option value="">All Students</option>
                {% for student in students %}
                  <option value="{{ student.id }}" {% if selected_student == student.id|stringformat:"s" %}selected{% endif %}>
                    {{ student.user.get_full_name|default:student.user.username }} ({{ student.student_id }})
                  </option>
                {% endfor %}
              </select>
            </div>
          </div>

          <!-- Search Student Filter -->
          <div class="lg:col-span-1">
            <label for="q" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              <i class="fas fa-search text-purple-500 mr-1"></i> Search Student
            </label>
            <div class="relative">
              <input type="text" name="q" id="q" value="{{ search_query }}" placeholder="Name, ID, or email..." class="w-full pl-10 pr-4 py-2 rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white shadow-sm focus:border-green-500 focus:ring focus:ring-green-500 focus:ring-opacity-50">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <i class="fas fa-search text-gray-400"></i>
              </div>
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="sm:col-span-2 lg:col-span-1 flex flex-col justify-end">
            <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
              <button type="submit" class="flex-1 inline-flex items-center justify-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200">
                <i class="fas fa-search mr-1"></i> Apply Filters
              </button>
              <a href="{% url 'admin_section:student_report' %}" class="flex-1 inline-flex items-center justify-center px-3 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200">
                <i class="fas fa-times mr-1"></i> Clear All
              </a>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>

  <!-- Active Filters Info -->
  {% if selected_department or selected_student or search_query %}
  <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-6">
    <div class="flex items-center">
      <i class="fas fa-info-circle text-blue-600 dark:text-blue-400 mr-2"></i>
      <span class="text-sm font-medium text-blue-800 dark:text-blue-200">Active Filters:</span>
      <div class="ml-2 flex flex-wrap gap-2">
        {% if selected_department %}
          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-800 text-blue-800 dark:text-blue-200">
            Department: {% for dept in departments %}{% if dept.id|stringformat:"s" == selected_department %}{{ dept.name }}{% endif %}{% endfor %}
          </span>
        {% endif %}
        {% if selected_student_obj %}
          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-800 text-green-800 dark:text-green-200">
            Student: {{ selected_student_obj.user.get_full_name|default:selected_student_obj.user.username }} ({{ selected_student_obj.student_id }})
          </span>
        {% endif %}
        {% if search_query %}
          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 dark:bg-purple-800 text-purple-800 dark:text-purple-200">
            {% if selected_student_obj and not selected_student %}
              Search Result: "{{ search_query }}"
            {% else %}
              Search: "{{ search_query }}"
            {% endif %}
          </span>
        {% endif %}
      </div>
    </div>
  </div>
  {% endif %}

  <!-- Selected Student Profile -->
  {% if selected_student_obj %}
  <div class="bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 border border-green-200 dark:border-green-800 rounded-xl shadow-lg p-6 mb-8">
    <div class="flex flex-col sm:flex-row items-start sm:items-center space-y-4 sm:space-y-0 sm:space-x-6">
      <!-- Profile Picture -->
      <div class="flex-shrink-0 mx-auto sm:mx-0">
        <div class="h-20 w-20 rounded-full overflow-hidden bg-gradient-to-br from-green-400 to-blue-500 flex items-center justify-center shadow-lg">
          {% if selected_student_obj.user.profile_photo and selected_student_obj.user.profile_photo.url != '/media/profiles/default.jpg' %}
            <img src="{{ selected_student_obj.user.profile_photo.url }}" alt="{{ selected_student_obj.user.get_full_name }}" class="h-full w-full object-cover">
          {% else %}
            <span class="text-2xl font-bold text-white">
              {{ selected_student_obj.user.first_name|first|default:selected_student_obj.user.username|first|upper }}{{ selected_student_obj.user.last_name|first|upper }}
            </span>
          {% endif %}
        </div>
      </div>

      <!-- Student Information -->
      <div class="flex-1 text-center sm:text-left">
        <div class="flex flex-col sm:flex-row sm:items-center sm:space-x-3 mb-3">
          <h3 class="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mb-2 sm:mb-0">
            {{ selected_student_obj.user.get_full_name|default:selected_student_obj.user.username }}
          </h3>
          <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 dark:bg-green-900/50 text-green-800 dark:text-green-200 mx-auto sm:mx-0 w-fit">
            <i class="fas fa-user-graduate mr-1"></i> Student
          </span>
        </div>

        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 text-sm">
          <div class="flex items-center justify-center sm:justify-start text-gray-600 dark:text-gray-400">
            <i class="fas fa-id-card text-blue-500 mr-2"></i>
            <span class="font-medium">ID:</span>
            <span class="ml-1 text-gray-900 dark:text-white font-semibold">{{ selected_student_obj.student_id }}</span>
          </div>

          <div class="flex items-center justify-center sm:justify-start text-gray-600 dark:text-gray-400">
            <i class="fas fa-envelope text-purple-500 mr-2"></i>
            <span class="font-medium">Email:</span>
            <span class="ml-1 text-gray-900 dark:text-white truncate">{{ selected_student_obj.user.email }}</span>
          </div>

          <div class="flex items-center justify-center sm:justify-start text-gray-600 dark:text-gray-400">
            <i class="fas fa-users text-orange-500 mr-2"></i>
            <span class="font-medium">Group:</span>
            <span class="ml-1 text-gray-900 dark:text-white font-semibold">
              {{ selected_student_obj.group.group_name|default:"No Group Assigned" }}
            </span>
          </div>
        </div>
      </div>

      <!-- Quick Stats -->
      <div class="flex-shrink-0 mx-auto sm:mx-0">
        <div class="text-center bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm">
          <div class="text-2xl sm:text-3xl font-bold text-green-600 dark:text-green-400">{{ total_logs }}</div>
          <div class="text-xs sm:text-sm text-gray-600 dark:text-gray-400">Total Logs</div>
        </div>
      </div>
    </div>
  </div>
  {% endif %}

  <!-- Search Results Info -->
  {% if search_results_info and search_results_info.count > 1 %}
  <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-xl shadow-lg p-6 mb-8">
    <div class="flex items-start space-x-4">
      <div class="flex-shrink-0">
        <div class="h-12 w-12 rounded-full bg-blue-100 dark:bg-blue-900/50 flex items-center justify-center">
          <i class="fas fa-search text-blue-600 dark:text-blue-400 text-xl"></i>
        </div>
      </div>
      <div class="flex-1">
        <h3 class="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-2">
          Search Results for "{{ search_query }}"
        </h3>
        <p class="text-blue-700 dark:text-blue-300 mb-4">
          Found {{ search_results_info.count }} student{{ search_results_info.count|pluralize }} matching your search.
          {% if search_results_info.count > 5 %}Showing first 5 results.{% endif %}
        </p>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {% for student in search_results_info.students %}
          <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-blue-200 dark:border-blue-700 hover:shadow-md transition-shadow duration-200">
            <div class="flex items-center space-x-3">
              <div class="flex-shrink-0">
                <div class="h-10 w-10 rounded-full overflow-hidden bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center">
                  {% if student.user.profile_photo and student.user.profile_photo.url != '/media/profiles/default.jpg' %}
                    <img src="{{ student.user.profile_photo.url }}" alt="{{ student.user.get_full_name }}" class="h-full w-full object-cover">
                  {% else %}
                    <span class="text-sm font-bold text-white">
                      {{ student.user.first_name|first|default:student.user.username|first|upper }}{{ student.user.last_name|first|upper }}
                    </span>
                  {% endif %}
                </div>
              </div>
              <div class="flex-1 min-w-0">
                <p class="text-sm font-medium text-gray-900 dark:text-white truncate">
                  {{ student.user.get_full_name|default:student.user.username }}
                </p>
                <p class="text-xs text-gray-500 dark:text-gray-400">{{ student.student_id }}</p>
                <p class="text-xs text-gray-500 dark:text-gray-400">{{ student.group.group_name|default:"No Group" }}</p>
              </div>
              <div class="flex-shrink-0">
                <a href="?student={{ student.id }}" class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200 dark:bg-blue-900/50 dark:text-blue-300 dark:hover:bg-blue-900/70 transition-colors duration-200">
                  Select
                </a>
              </div>
            </div>
          </div>
          {% endfor %}
        </div>

        {% if search_results_info.count > 5 %}
        <p class="text-sm text-blue-600 dark:text-blue-400 mt-4">
          <i class="fas fa-info-circle mr-1"></i>
          Use a more specific search term or select a student from the dropdown to see individual results.
        </p>
        {% endif %}
      </div>
    </div>
  </div>
  {% endif %}

  <!-- Summary Cards -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border-l-4 border-blue-500">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Logs</p>
          <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ total_logs }}</p>
        </div>
        <div class="p-3 rounded-full bg-blue-100 dark:bg-blue-900/50">
          <i class="fas fa-file-medical text-blue-600 dark:text-blue-400"></i>
        </div>
      </div>
    </div>

    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border-l-4 border-green-500">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Total Doctors</p>
          <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ total_doctors }}</p>
        </div>
        <div class="p-3 rounded-full bg-green-100 dark:bg-green-900/50">
          <i class="fas fa-user-md text-green-600 dark:text-green-400"></i>
        </div>
      </div>
    </div>

    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border-l-4 border-purple-500">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Approved Logs</p>
          <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ approved_logs }}</p>
        </div>
        <div class="p-3 rounded-full bg-purple-100 dark:bg-purple-900/50">
          <i class="fas fa-check-circle text-purple-600 dark:text-purple-400"></i>
        </div>
      </div>
    </div>
    
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border-l-4 border-orange-500">
      <div class="flex items-center justify-between">
        <div>
          <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Pending Logs</p>
          <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ pending_logs }}</p>
        </div>
        <div class="p-3 rounded-full bg-orange-100 dark:bg-orange-900/50">
          <i class="fas fa-clock text-orange-600 dark:text-orange-400"></i>
        </div>
      </div>
    </div>
  </div>

  <!-- Charts and Visualizations -->
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">

    <!-- Case Types Pie Chart -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
          <i class="fas fa-chart-pie text-blue-600 dark:text-blue-400 mr-2"></i> Case Types Distribution
        </h3>
      </div>
      <div class="p-6">
        <div class="h-64 relative">
          <canvas id="caseTypesChart"></canvas>
        </div>
      </div>
    </div>

    <!-- Training Sites Pie Chart -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
          <i class="fas fa-chart-pie text-purple-600 dark:text-purple-400 mr-2"></i> Training Sites Distribution
        </h3>
      </div>
      <div class="p-6">
        <div class="h-64 relative">
          <canvas id="trainingSitesChart"></canvas>
        </div>
      </div>
    </div>

    <!-- Activity Types with Filter -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
          <i class="fas fa-chart-pie text-orange-600 dark:text-orange-400 mr-2"></i> Activity Types
        </h3>
        <select id="activityTypeFilter" class="text-sm rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white" onchange="updateActivityTypeChart()">
          <option value="">All Activity Types</option>
        </select>
      </div>
      <div class="p-6">
        <div class="h-64 relative">
          <canvas id="activityTypesChart"></canvas>
        </div>
      </div>
    </div>

    <!-- Participation Types with Filter -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
          <i class="fas fa-chart-pie text-green-600 dark:text-green-400 mr-2"></i> Participation Types
        </h3>
        <select id="participationFilter" class="text-sm rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-800 dark:text-white" onchange="updateParticipationChart()">
          <option value="">All Participation Types</option>
          <option value="observed">Observed</option>
          <option value="assisted">Assisted</option>
        </select>
      </div>
      <div class="p-6">
        <div class="h-64 relative">
          <canvas id="participationChart"></canvas>
        </div>
      </div>
    </div>

  </div>

  <!-- Monthly Cases Line Chart -->
  <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden mb-8">
    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
        <i class="fas fa-chart-line text-indigo-600 dark:text-indigo-400 mr-2"></i> Cases Encountered by Month
      </h3>
    </div>
    <div class="p-6">
      <div class="h-80 relative">
        <canvas id="monthlyChart"></canvas>
      </div>
    </div>
  </div>

  <!-- Approval Status Chart -->
  <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden mb-8">
    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
        <i class="fas fa-chart-bar text-emerald-600 dark:text-emerald-400 mr-2"></i> Approval Status Overview
      </h3>
    </div>
    <div class="p-6">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="h-64 relative">
          <canvas id="approvalStatusChart"></canvas>
        </div>
        <div class="space-y-4">
          <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
            <div class="flex items-center justify-between">
              <span class="text-green-800 dark:text-green-200 font-medium">Approved Cases</span>
              <span class="text-2xl font-bold text-green-600 dark:text-green-400" id="approvedCount">{{ approved_logs }}</span>
            </div>
          </div>
          <div class="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg">
            <div class="flex items-center justify-between">
              <span class="text-yellow-800 dark:text-yellow-200 font-medium">Pending Cases</span>
              <span class="text-2xl font-bold text-yellow-600 dark:text-yellow-400" id="pendingCount">{{ pending_logs }}</span>
            </div>
          </div>
          <div class="bg-red-50 dark:bg-red-900/20 p-4 rounded-lg">
            <div class="flex items-center justify-between">
              <span class="text-red-800 dark:text-red-200 font-medium">Rejected Cases</span>
              <span class="text-2xl font-bold text-red-600 dark:text-red-400" id="rejectedCount">{{ rejected_logs }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Doctor Names Section -->
  <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
        <i class="fas fa-user-md text-green-600 dark:text-green-400 mr-2"></i>
        Doctors Involved
        <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/50 text-green-800 dark:text-green-200">
          {{ total_doctors }} doctor{{ total_doctors|pluralize }}
        </span>
      </h3>
    </div>
    <div class="p-6">
      {% if doctor_names %}
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {% for doctor in doctor_names %}
            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 flex items-center hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors duration-200">
              <div class="flex-shrink-0 h-10 w-10">
                <div class="h-10 w-10 rounded-full bg-green-100 dark:bg-green-900/50 flex items-center justify-center">
                  <i class="fas fa-user-md text-green-600 dark:text-green-400"></i>
                </div>
              </div>
              <div class="ml-3">
                <p class="text-sm font-medium text-gray-900 dark:text-white">Dr. {{ doctor }}</p>
                <p class="text-xs text-gray-500 dark:text-gray-400">Supervising Doctor</p>
              </div>
            </div>
          {% endfor %}
        </div>
      {% else %}
        <div class="text-center text-gray-500 dark:text-gray-400 py-8">
          <i class="fas fa-user-md text-4xl mb-2"></i>
          <p class="text-lg font-medium">No doctors found</p>
          <p class="text-sm">
            {% if selected_department or selected_student or search_query %}
              Try adjusting your filters to see more results.
            {% else %}
              No log entries with assigned doctors found in the system.
            {% endif %}
          </p>
        </div>
      {% endif %}
    </div>
  </div>

</div>
{% endblock %}

{% block extra_scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
  // Chart instances
  let caseTypesChart, trainingSitesChart, activityTypesChart, participationChart, monthlyChart, approvalStatusChart;

  // Chart data from the backend
  const chartData = {
    caseTypes: {{ case_types_data|safe }},
    trainingSites: {{ training_sites_data|safe }},
    activityTypes: {{ activity_types_data|safe }},
    participation: {{ participation_data|safe }},
    monthly: {{ monthly_data|safe }},
    approvalStatus: {{ approval_status_data|safe }}
  };

  // Initialize all charts
  document.addEventListener('DOMContentLoaded', function() {
    console.log('Chart Data:', chartData);

    initializeCaseTypesChart();
    initializeTrainingSitesChart();
    initializeActivityTypesChart();
    initializeParticipationChart();
    initializeMonthlyChart();
    initializeApprovalStatusChart();
    populateActivityTypeFilter();
  });

  function initializeCaseTypesChart() {
    const ctx = document.getElementById('caseTypesChart').getContext('2d');
    const hasData = chartData.caseTypes.labels && chartData.caseTypes.labels.length > 0;

    caseTypesChart = new Chart(ctx, {
      type: 'pie',
      data: {
        labels: hasData ? chartData.caseTypes.labels : ['No Data Available'],
        datasets: [{
          data: hasData ? chartData.caseTypes.data : [1],
          backgroundColor: hasData ? [
            '#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6',
            '#06B6D4', '#84CC16', '#F97316', '#EC4899', '#6366F1'
          ] : ['#E5E7EB'],
          borderWidth: 2,
          borderColor: '#ffffff'
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'bottom',
            labels: {
              padding: 20,
              usePointStyle: true,
              color: hasData ? '#374151' : '#9CA3AF'
            }
          },
          tooltip: {
            enabled: hasData
          }
        }
      }
    });
  }

  function initializeTrainingSitesChart() {
    const ctx = document.getElementById('trainingSitesChart').getContext('2d');
    const hasData = chartData.trainingSites.labels && chartData.trainingSites.labels.length > 0;

    trainingSitesChart = new Chart(ctx, {
      type: 'pie',
      data: {
        labels: hasData ? chartData.trainingSites.labels : ['No Data Available'],
        datasets: [{
          data: hasData ? chartData.trainingSites.data : [1],
          backgroundColor: hasData ? [
            '#8B5CF6', '#06B6D4', '#84CC16', '#F97316', '#EC4899',
            '#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#6366F1'
          ] : ['#E5E7EB'],
          borderWidth: 2,
          borderColor: '#ffffff'
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'bottom',
            labels: {
              padding: 20,
              usePointStyle: true,
              color: hasData ? '#374151' : '#9CA3AF'
            }
          },
          tooltip: {
            enabled: hasData
          }
        }
      }
    });
  }

  function initializeActivityTypesChart() {
    const ctx = document.getElementById('activityTypesChart').getContext('2d');
    activityTypesChart = new Chart(ctx, {
      type: 'pie',
      data: {
        labels: chartData.activityTypes.labels || ['No Data'],
        datasets: [{
          data: chartData.activityTypes.data || [1],
          backgroundColor: [
            '#F97316', '#EC4899', '#6366F1', '#3B82F6', '#10B981',
            '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4', '#84CC16'
          ],
          borderWidth: 2,
          borderColor: '#ffffff'
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'bottom',
            labels: {
              padding: 20,
              usePointStyle: true
            }
          }
        }
      }
    });
  }

  function initializeParticipationChart() {
    const ctx = document.getElementById('participationChart').getContext('2d');
    participationChart = new Chart(ctx, {
      type: 'pie',
      data: {
        labels: chartData.participation.labels || ['No Data'],
        datasets: [{
          data: chartData.participation.data || [1],
          backgroundColor: [
            '#10B981', '#F59E0B', '#EF4444', '#3B82F6', '#8B5CF6'
          ],
          borderWidth: 2,
          borderColor: '#ffffff'
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'bottom',
            labels: {
              padding: 20,
              usePointStyle: true
            }
          }
        }
      }
    });
  }

  function initializeMonthlyChart() {
    const ctx = document.getElementById('monthlyChart').getContext('2d');
    const hasData = chartData.monthly.labels && chartData.monthly.labels.length > 0;

    monthlyChart = new Chart(ctx, {
      type: 'line',
      data: {
        labels: hasData ? chartData.monthly.labels : ['No Data Available'],
        datasets: [{
          label: 'Cases per Month',
          data: hasData ? chartData.monthly.data : [0],
          borderColor: hasData ? '#6366F1' : '#E5E7EB',
          backgroundColor: hasData ? 'rgba(99, 102, 241, 0.1)' : 'rgba(229, 231, 235, 0.1)',
          borderWidth: 3,
          fill: true,
          tension: 0.4,
          pointBackgroundColor: hasData ? '#6366F1' : '#E5E7EB',
          pointBorderColor: '#ffffff',
          pointBorderWidth: 2,
          pointRadius: hasData ? 6 : 0
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: hasData
          },
          tooltip: {
            enabled: hasData
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            grid: {
              color: 'rgba(0, 0, 0, 0.1)'
            },
            ticks: {
              color: hasData ? '#374151' : '#9CA3AF'
            }
          },
          x: {
            grid: {
              color: 'rgba(0, 0, 0, 0.1)'
            },
            ticks: {
              color: hasData ? '#374151' : '#9CA3AF'
            }
          }
        }
      }
    });
  }

  function initializeApprovalStatusChart() {
    const ctx = document.getElementById('approvalStatusChart').getContext('2d');
    approvalStatusChart = new Chart(ctx, {
      type: 'bar',
      data: {
        labels: ['Approved', 'Pending', 'Rejected'],
        datasets: [{
          data: [
            chartData.approvalStatus.approved || 0,
            chartData.approvalStatus.pending || 0,
            chartData.approvalStatus.rejected || 0
          ],
          backgroundColor: ['#10B981', '#F59E0B', '#EF4444'],
          borderWidth: 0,
          borderRadius: 8
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            grid: {
              color: 'rgba(0, 0, 0, 0.1)'
            }
          },
          x: {
            grid: {
              display: false
            }
          }
        }
      }
    });
  }

  function populateActivityTypeFilter() {
    const filter = document.getElementById('activityTypeFilter');
    if (chartData.activityTypes.labels) {
      chartData.activityTypes.labels.forEach(label => {
        const option = document.createElement('option');
        option.value = label;
        option.textContent = label;
        filter.appendChild(option);
      });
    }
  }

  function updateActivityTypeChart() {
    const filter = document.getElementById('activityTypeFilter');
    const selectedType = filter.value;

    if (selectedType === '') {
      // Show all data
      activityTypesChart.data.labels = chartData.activityTypes.labels || ['No Data'];
      activityTypesChart.data.datasets[0].data = chartData.activityTypes.data || [1];
    } else {
      // Filter data for selected type
      const index = chartData.activityTypes.labels.indexOf(selectedType);
      if (index !== -1) {
        activityTypesChart.data.labels = [selectedType];
        activityTypesChart.data.datasets[0].data = [chartData.activityTypes.data[index]];
      }
    }
    activityTypesChart.update();
  }

  function updateParticipationChart() {
    const filter = document.getElementById('participationFilter');
    const selectedType = filter.value;

    if (selectedType === '') {
      // Show all data
      participationChart.data.labels = chartData.participation.labels || ['No Data'];
      participationChart.data.datasets[0].data = chartData.participation.data || [1];
    } else {
      // Filter data for selected type
      const index = chartData.participation.labels.map(l => l.toLowerCase()).indexOf(selectedType);
      if (index !== -1) {
        participationChart.data.labels = [chartData.participation.labels[index]];
        participationChart.data.datasets[0].data = [chartData.participation.data[index]];
      }
    }
    participationChart.update();
  }

  // Print functionality
  function printReport() {
    window.print();
  }

  // Export functionality (placeholder)
  function exportReport() {
    alert('Export functionality will be implemented soon');
  }

  // Search functionality with debounce
  let searchTimeout;
  document.getElementById('q').addEventListener('input', function() {
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(() => {
      if (this.value.length >= 2 || this.value.length === 0) {
        this.form.submit();
      }
    }, 500); // Wait 500ms after user stops typing
  });

  // Auto-submit on Enter key
  document.getElementById('q').addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
      clearTimeout(searchTimeout);
      this.form.submit();
    }
  });
</script>
{% endblock %}
