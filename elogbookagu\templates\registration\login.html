{% extends 'base.html' %}

{% block title %}Login - MedLogBook{% endblock %}

{% block extra_head %}
  <!-- Additional styles for login page -->
  <style>
    .login-container {
      min-height: calc(100vh - 64px - 80px); /* Subtract header and footer heights */
    }
    
    .login-card {
      transition: all 0.3s ease;
    }
    
    .login-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }
    
    .form-input {
      transition: border-color 0.2s ease;
    }
    
    .form-input:focus {
      border-color: #3b82f6;
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
    }
    
    .login-btn {
      transition: all 0.3s ease;
    }
    
    .login-btn:hover {
      transform: translateY(-2px);
    }
    
    @keyframes float {
      0% { transform: translateY(0px); }
      50% { transform: translateY(-10px); }
      100% { transform: translateY(0px); }
    }
    
    .animate-float {
      animation: float 6s ease-in-out infinite;
    }
    
    .bg-pattern {
      background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    }
  </style>
{% endblock %}

{% block navbar %}
  {% include './components/public_navbar.html' %}
{% endblock %}

{% block content_container %}
  <!-- Full-width Login Section -->
  <section class="w-full bg-gradient-to-r from-blue-600 to-purple-700 dark:from-gray-800 dark:to-gray-900 bg-pattern">
    <div class="login-container w-full flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-7xl w-full">
        <!-- Left Column - Login Form -->
        <div class="flex flex-col justify-center">
          <div class="login-card bg-white dark:bg-gray-800 rounded-xl shadow-xl p-8 md:p-10">
            <div class="text-center mb-8">
              <h2 class="text-3xl font-bold text-gray-900 dark:text-white">Welcome Back</h2>
              <p class="mt-2 text-gray-600 dark:text-gray-300">Sign in to your MedLogBook account</p>
            </div>
            
            <!-- Login Form -->
            <form method="post" action="{% url 'login' %}" class="space-y-6">
              {% csrf_token %}
              
              <!-- Error Messages -->
              {% if form.errors %}
                <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded-lg mb-6">
                  <p class="font-medium">Please correct the errors below:</p>
                  <ul class="list-disc list-inside text-sm mt-2">
                    {% for field in form %}
                      {% for error in field.errors %}
                        <li>{{ field.label }}: {{ error }}</li>
                      {% endfor %}
                    {% endfor %}
                    {% for error in form.non_field_errors %}
                      <li>{{ error }}</li>
                    {% endfor %}
                  </ul>
                </div>
              {% endif %}
              
              <!-- Username Field -->
              <div>
                <label for="id_username" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Username</label>
                <input type="text" name="username" id="id_username" autocomplete="username" required 
                       class="form-input w-full px-4 py-3 border border-gray-300 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none">
              </div>
              
              <!-- Password Field -->
              <div>
                <div class="flex items-center justify-between">
                  <label for="id_password" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Password</label>
                  <a href="{% url 'password_reset' %}" class="text-sm font-medium text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300">
                    Forgot password?
                  </a>
                </div>
                <input type="password" name="password" id="id_password" autocomplete="current-password" required 
                       class="form-input w-full px-4 py-3 border border-gray-300 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none">
              </div>
              
              <!-- Remember Me Checkbox -->
              <div class="flex items-center">
                <input id="remember_me" name="remember_me" type="checkbox" 
                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                <label for="remember_me" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                  Remember me
                </label>
              </div>
              
              <!-- Hidden Next Field -->
              <input type="hidden" name="next" value="{{ next }}">
              
              <!-- Submit Button -->
              <div>
                <button type="submit" 
                        class="login-btn w-full flex justify-center items-center px-6 py-3 border border-transparent rounded-lg shadow-sm text-base font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200">
                  <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"></path>
                  </svg>
                  Sign in
                </button>
              </div>
            </form>
            
            <!-- Divider -->
            <div class="mt-8 mb-6 flex items-center">
              <div class="flex-grow border-t border-gray-200 dark:border-gray-700"></div>
              <span class="flex-shrink mx-4 text-gray-400 dark:text-gray-500 text-sm">or continue with</span>
              <div class="flex-grow border-t border-gray-200 dark:border-gray-700"></div>
            </div>
            
            <!-- Social Login Buttons -->
            <div class="grid grid-cols-3 gap-3">
              <button type="button" class="inline-flex justify-center items-center py-2.5 px-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600 transition-all duration-200">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M6.29 18.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0020 3.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.073 4.073 0 01.8 7.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 010 16.407a11.616 11.616 0 006.29 1.84"></path>
                </svg>
              </button>
              <button type="button" class="inline-flex justify-center items-center py-2.5 px-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600 transition-all duration-200">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 0C4.477 0 0 4.477 0 10c0 4.42 2.865 8.166 6.839 9.489.5.092.682-.217.682-.482 0-.237-.008-.866-.013-1.7-2.782.603-3.369-1.342-3.369-1.342-.454-1.155-1.11-1.462-1.11-1.462-.908-.62.069-.608.069-.608 1.003.07 1.531 1.03 1.531 1.03.892 1.529 2.341 1.087 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.11-4.555-4.943 0-1.091.39-1.984 1.029-2.683-.103-.253-.446-1.27.098-2.647 0 0 .84-.268 2.75 1.026A9.578 9.578 0 0110 4.836c.85.004 1.705.114 2.504.336 1.909-1.294 2.747-1.026 2.747-1.026.546 1.377.203 2.394.1 2.647.64.699 1.028 1.592 1.028 2.683 0 3.842-2.339 4.687-4.566 4.935.359.309.678.919.678 1.852 0 1.336-.012 2.415-.012 2.743 0 .267.18.578.688.48C17.14 18.163 20 14.418 20 10c0-5.523-4.477-10-10-10z" clip-rule="evenodd"></path>
                </svg>
              </button>
              <button type="button" class="inline-flex justify-center items-center py-2.5 px-3 border border-gray-300 dark:border-gray-600 rounded-lg shadow-sm bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600 transition-all duration-200">
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 0C4.477 0 0 4.477 0 10c0 5.523 4.477 10 10 10 5.523 0 10-4.477 10-10 0-5.523-4.477-10-10-10zm6.536 15.95a7.5 7.5 0 01-13.072 0A7.5 7.5 0 0110 2.5a7.5 7.5 0 016.536 13.45zM10 12a2 2 0 100-4 2 2 0 000 4z" clip-rule="evenodd"></path>
                </svg>
              </button>
            </div>
            
            <!-- Registration Link -->
            <div class="mt-6 text-center">
              <p class="text-sm text-gray-600 dark:text-gray-400">
                Don't have an account?
                <a href="#" class="font-medium text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300">
                  Contact your administrator
                </a>
              </p>
            </div>
          </div>
        </div>
        
        <!-- Right Column - Image and Text -->
        <div class="hidden md:flex flex-col justify-center">
          <div class="text-center md:text-left text-white">
            <h1 class="text-4xl font-bold mb-6">Streamline Your Medical Education</h1>
            <p class="text-xl opacity-90 mb-8">Access your medical logs, resources, and educational materials all in one place.</p>
            
            <!-- Feature List -->
            <div class="space-y-4 mb-8">
              <div class="flex items-start">
                <div class="flex-shrink-0 h-6 w-6 text-blue-300">
                  <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                </div>
                <p class="ml-3 text-white opacity-90">Secure access to your medical logs and records</p>
              </div>
              <div class="flex items-start">
                <div class="flex-shrink-0 h-6 w-6 text-blue-300">
                  <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                </div>
                <p class="ml-3 text-white opacity-90">Comprehensive medical resources and journals</p>
              </div>
              <div class="flex items-start">
                <div class="flex-shrink-0 h-6 w-6 text-blue-300">
                  <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                </div>
                <p class="ml-3 text-white opacity-90">Seamless collaboration with supervisors and peers</p>
              </div>
            </div>
            
            <!-- Image -->
            <div class="relative mt-8 animate-float">
              <img src="https://images.unsplash.com/photo-1576091160550-2173dba999ef?q=80&w=2070" alt="Medical Education" class="rounded-lg shadow-2xl w-full object-cover h-auto" />
              <div class="absolute -bottom-4 -right-4 bg-white p-4 rounded-lg shadow-lg">
                <div class="flex items-center">
                  <div class="bg-green-500 p-2 rounded-full">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                    </svg>
                  </div>
                  <div class="ml-3">
                    <p class="text-sm font-medium text-gray-900">Trusted by Medical Institutions</p>
                    <p class="text-xs text-gray-500">Secure & Compliant</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
{% endblock %}

{% block extra_scripts %}
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Focus on username field when page loads
    document.getElementById('id_username').focus();
    
    // Add floating animation to form on hover
    const loginCard = document.querySelector('.login-card');
    if (loginCard) {
      loginCard.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-10px)';
      });
      
      loginCard.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(-5px)';
      });
    }
  });
</script>
{% endblock %}
