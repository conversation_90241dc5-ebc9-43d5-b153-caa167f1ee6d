{% extends 'base.html' %}
{% load static %}

{% block title %}Attendance History{% endblock %}

{% block extra_head %}
<style>
  .history-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }
  
  .status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
  }
  
  .status-present {
    background-color: #dcfce7;
    color: #166534;
  }
  
  .status-absent {
    background-color: #fef2f2;
    color: #991b1b;
  }
  
  .filter-card {
    background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  }

  .student-avatar {
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .avatar-initials {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }
</style>
{% endblock %}

{% block navbar %}
  {% include 'components/doc_auth_navbar.html' %}
{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    
    <!-- Header Section -->
    <div class="history-card rounded-lg p-6 mb-8 text-white">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold">Attendance History</h1>
          <p class="mt-2 text-blue-100">
            View, filter, and export attendance records you have marked
          </p>
        </div>
        <div class="flex space-x-3">
          <a href="{% url 'doctor_section:take_attendance' %}" 
             class="inline-flex items-center px-4 py-2 bg-white text-blue-600 rounded-lg hover:bg-gray-100 transition-colors duration-200 font-medium">
            <i class="fas fa-clipboard-check mr-2"></i>
            Take Attendance
          </a>
          <a href="{% url 'doctor_section:attendance_summary' %}" 
             class="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200 font-medium">
            <i class="fas fa-chart-bar mr-2"></i>
            View Summary
          </a>
        </div>
      </div>
    </div>

    <!-- Filters Section -->
    <div class="filter-card rounded-lg p-6 mb-8">
      <h3 class="text-lg font-semibold text-gray-900 dark:text-black mb-4 flex items-center ">
        <i class="fas fa-filter mr-2 text-blue-600"></i>
        Filter Attendance Records
      </h3>
      
      <form method="get" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        <!-- Date Range Filters -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-900 mb-2">Start Date</label>
          <input type="date" name="start_date" value="{{ start_date }}"
                 class="w-full px-3 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500">
        </div>

        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-900 mb-2">End Date</label>
          <input type="date" name="end_date" value="{{ end_date }}"
                 class="w-full px-3 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500">
        </div>

        <!-- Training Site Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-900 mb-2">Training Site</label>
          <select name="training_site" class="w-full px-3 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500">
            <option value="">All Training Sites</option>
            {% for site in training_sites %}
              <option value="{{ site.id }}" {% if selected_training_site == site.id|stringformat:"s" %}selected{% endif %}>
                {{ site.name }}
              </option>
            {% endfor %}
          </select>
        </div>

        <!-- Status Filter -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-900 mb-2">Status</label>
          <select name="status" class="w-full px-3 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500">
            <option value="all" {% if selected_status == 'all' or not selected_status %}selected{% endif %}>All Status</option>
            <option value="present" {% if selected_status == 'present' %}selected{% endif %}>Present</option>
            <option value="absent" {% if selected_status == 'absent' %}selected{% endif %}>Absent</option>
          </select>
        </div>

        <!-- Student Search -->
        <div class="md:col-span-2 lg:col-span-1">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-900 mb-2">Student Search</label>
          <input type="text" name="student_search" value="{{ student_search }}" placeholder="Name, ID, or username"
                 class="w-full px-3 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500">
        </div>

        <!-- Filter Buttons -->
        <div class="flex items-end space-x-2 md:col-span-2 lg:col-span-1">
          <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200 font-medium">
            <i class="fas fa-search mr-2"></i>Apply Filters
          </button>
          <a href="{% url 'doctor_section:attendance_history' %}" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors duration-200 font-medium">
            <i class="fas fa-times mr-2"></i>Clear
          </a>
        </div>

        <!-- Single Date Filter (for backward compatibility) -->
        <div class="hidden">
          <input type="date" name="date" value="{{ selected_date }}">
        </div>
      </form>
    </div>

    <!-- Export Section -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm mb-6">
      <div class="px-6 py-4">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center">
          <i class="fas fa-download mr-2 text-green-600"></i>
          Export Attendance Records
        </h3>
        <div class="flex flex-wrap gap-3">
          {% if attendances %}
            <a href="{% url 'doctor_section:export_attendance' %}?format=csv&{{ request.GET.urlencode }}" class="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors duration-200 font-medium">
              <i class="fas fa-file-csv mr-2"></i>Export CSV
            </a>
            <a href="{% url 'doctor_section:export_attendance' %}?format=excel&{{ request.GET.urlencode }}" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200 font-medium">
              <i class="fas fa-file-excel mr-2"></i>Export Excel
            </a>
            <a href="{% url 'doctor_section:export_attendance' %}?format=pdf&{{ request.GET.urlencode }}" class="inline-flex items-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors duration-200 font-medium">
              <i class="fas fa-file-pdf mr-2"></i>Export PDF
            </a>
          {% else %}
            <button class="inline-flex items-center px-4 py-2 bg-gray-400 cursor-not-allowed text-white rounded-lg font-medium" disabled>
              <i class="fas fa-file-csv mr-2"></i>Export CSV
            </button>
            <button class="inline-flex items-center px-4 py-2 bg-gray-400 cursor-not-allowed text-white rounded-lg font-medium" disabled>
              <i class="fas fa-file-excel mr-2"></i>Export Excel
            </button>
            <button class="inline-flex items-center px-4 py-2 bg-gray-400 cursor-not-allowed text-white rounded-lg font-medium" disabled>
              <i class="fas fa-file-pdf mr-2"></i>Export PDF
            </button>
          {% endif %}
        </div>
        <p class="text-sm text-gray-500 dark:text-gray-400 mt-2">
          {% if attendances %}
            Export will include all filtered records ({{ total_records }} records)
          {% else %}
            No records available to export. Please add some attendance records first.
          {% endif %}
        </p>
      </div>
    </div>

    <!-- Attendance Records -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
          Attendance Records
          {% if attendances %}
            <span class="text-sm font-normal text-gray-500 dark:text-gray-400">
              ({{ attendances|length }} of {{ total_records }} records)
            </span>
          {% endif %}
        </h3>
      </div>

      {% if attendances %}
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Student
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Training Site
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Group
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Date
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Status
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Marked At
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Notes
                </th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {% for attendance in attendances %}
                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="student-avatar flex-shrink-0 h-10 w-10 rounded-full overflow-hidden mr-3 border-2 border-gray-200 dark:border-gray-600">
                        {% if attendance.student.user.profile_photo %}
                          <img src="{{ attendance.student.user.profile_photo.url }}"
                               alt="{{ attendance.student.user.get_full_name|default:attendance.student.user.username }}"
                               class="w-full h-full object-cover">
                        {% else %}
                          <div class="avatar-initials w-full h-full flex items-center justify-center">
                            <span class="text-white font-semibold text-xs">
                              {{ attendance.student.user.get_full_name|default:attendance.student.user.username|first|upper }}
                            </span>
                          </div>
                        {% endif %}
                      </div>
                      <div>
                        <div class="text-sm font-medium text-gray-900 dark:text-white">
                          {{ attendance.student.user.get_full_name|default:attendance.student.user.username }}
                        </div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">
                          ID: {{ attendance.student.student_id }}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900 dark:text-white">
                      {{ attendance.training_site.name }}
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900 dark:text-white">
                      {{ attendance.group.group_name }}
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900 dark:text-white">
                      {{ attendance.date|date:"M d, Y" }}
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    {% if attendance.status == 'present' %}
                      <span class="status-badge status-present">
                        <i class="fas fa-check-circle mr-1"></i>Present
                      </span>
                    {% else %}
                      <span class="status-badge status-absent">
                        <i class="fas fa-times-circle mr-1"></i>Absent
                      </span>
                    {% endif %}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900 dark:text-white">
                      {{ attendance.marked_at|date:"M d, Y H:i" }}
                    </div>
                  </td>
                  <td class="px-6 py-4">
                    {% if attendance.notes %}
                      <div class="text-sm text-gray-900 dark:text-white max-w-xs truncate" title="{{ attendance.notes }}">
                        {{ attendance.notes }}
                      </div>
                    {% else %}
                      <span class="text-sm text-gray-400 dark:text-gray-500">No notes</span>
                    {% endif %}
                  </td>
                </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>

        <!-- Pagination Controls -->
        {% if attendances.has_other_pages %}
          <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
              <!-- Per Page Selector -->
              <div class="flex items-center space-x-2">
                <label for="per-page" class="text-sm text-gray-700 dark:text-gray-300">Show:</label>
                <select id="per-page" onchange="changePerPage(this.value)"
                        class="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                  <option value="10" {% if per_page == 10 %}selected{% endif %}>10</option>
                  <option value="20" {% if per_page == 20 %}selected{% endif %}>20</option>
                  <option value="50" {% if per_page == 50 %}selected{% endif %}>50</option>
                  <option value="100" {% if per_page == 100 %}selected{% endif %}>100</option>
                </select>
                <span class="text-sm text-gray-700 dark:text-gray-300">per page</span>
              </div>

              <!-- Pagination Info -->
              <div class="text-sm text-gray-700 dark:text-gray-300">
                Showing {{ attendances.start_index }} to {{ attendances.end_index }} of {{ total_records }} results
              </div>

              <!-- Pagination Navigation -->
              <div class="flex items-center space-x-1">
                {% if attendances.has_previous %}
                  <a href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page=1"
                     class="px-3 py-2 text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 transition-colors duration-200">
                    <i class="fas fa-angle-double-left"></i>
                  </a>
                  <a href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ attendances.previous_page_number }}"
                     class="px-3 py-2 text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 transition-colors duration-200">
                    <i class="fas fa-angle-left"></i>
                  </a>
                {% else %}
                  <span class="px-3 py-2 text-sm text-gray-300 dark:text-gray-600">
                    <i class="fas fa-angle-double-left"></i>
                  </span>
                  <span class="px-3 py-2 text-sm text-gray-300 dark:text-gray-600">
                    <i class="fas fa-angle-left"></i>
                  </span>
                {% endif %}

                <!-- Page Numbers -->
                {% for num in attendances.paginator.page_range %}
                  {% if num == attendances.number %}
                    <span class="px-3 py-2 text-sm bg-blue-600 text-white rounded-md">{{ num }}</span>
                  {% elif num > attendances.number|add:'-3' and num < attendances.number|add:'3' %}
                    <a href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ num }}"
                       class="px-3 py-2 text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors duration-200">
                      {{ num }}
                    </a>
                  {% endif %}
                {% endfor %}

                {% if attendances.has_next %}
                  <a href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ attendances.next_page_number }}"
                     class="px-3 py-2 text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 transition-colors duration-200">
                    <i class="fas fa-angle-right"></i>
                  </a>
                  <a href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ attendances.paginator.num_pages }}"
                     class="px-3 py-2 text-sm text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 transition-colors duration-200">
                    <i class="fas fa-angle-double-right"></i>
                  </a>
                {% else %}
                  <span class="px-3 py-2 text-sm text-gray-300 dark:text-gray-600">
                    <i class="fas fa-angle-right"></i>
                  </span>
                  <span class="px-3 py-2 text-sm text-gray-300 dark:text-gray-600">
                    <i class="fas fa-angle-double-right"></i>
                  </span>
                {% endif %}
              </div>
            </div>
          </div>
        {% endif %}
      {% else %}
        <div class="px-6 py-12 text-center">
          <div class="text-gray-500 dark:text-gray-400">
            <i class="fas fa-clipboard-list text-4xl mb-4"></i>
            <p class="text-lg font-medium">No attendance records found</p>
            {% if selected_date or selected_training_site %}
              <p class="text-sm mt-1">Try adjusting your filters or</p>
              <a href="{% url 'doctor_section:attendance_history' %}" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm">
                clear all filters
              </a>
            {% else %}
              <p class="text-sm mt-1">Start by taking attendance for your students.</p>
              <a href="{% url 'doctor_section:take_attendance' %}"
                 class="inline-flex items-center mt-3 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors duration-200">
                <i class="fas fa-clipboard-check mr-2"></i>
                Take Attendance
              </a>
            {% endif %}
          </div>
        </div>
      {% endif %}
    </div>

    <!-- Quick Stats -->
    {% if attendances %}
      <div class="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-blue-100 dark:bg-blue-900">
              <i class="fas fa-clipboard-list text-blue-600 dark:text-blue-400 text-xl"></i>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Records</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ total_records }}</p>
            </div>
          </div>
        </div>
        
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-green-100 dark:bg-green-900">
              <i class="fas fa-check-circle text-green-600 dark:text-green-400 text-xl"></i>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Present</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ present_count }}</p>
              {% if total_records > 0 %}
                <p class="text-sm text-green-600 dark:text-green-400">
                  {% widthratio present_count total_records 100 %}% of total
                </p>
              {% endif %}
            </div>
          </div>
        </div>
        
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-red-100 dark:bg-red-900">
              <i class="fas fa-times-circle text-red-600 dark:text-red-400 text-xl"></i>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Absent</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ absent_count }}</p>
              {% if total_records > 0 %}
                <p class="text-sm text-red-600 dark:text-red-400">
                  {% widthratio absent_count total_records 100 %}% of total
                </p>
              {% endif %}
            </div>
          </div>
        </div>
      </div>
    {% endif %}
  </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- JavaScript for Export Functionality and Pagination -->
<script>
// Function to change per page value
function changePerPage(perPage) {
    const url = new URL(window.location);
    url.searchParams.set('per_page', perPage);
    url.searchParams.delete('page'); // Reset to first page when changing per_page
    window.location.href = url.toString();
}

document.addEventListener('DOMContentLoaded', function() {
    // Export functionality
    const exportCsvBtn = document.getElementById('export-csv');
    const exportExcelBtn = document.getElementById('export-excel');
    const exportPdfBtn = document.getElementById('export-pdf');

    // Function to show loading state
    function showLoading(button) {
        const exportText = button.querySelector('.export-text');
        const loadingText = button.querySelector('.loading-text');
        exportText.classList.add('hidden');
        loadingText.classList.remove('hidden');
        button.disabled = true;
    }

    // Function to hide loading state
    function hideLoading(button) {
        const exportText = button.querySelector('.export-text');
        const loadingText = button.querySelector('.loading-text');
        exportText.classList.remove('hidden');
        loadingText.classList.add('hidden');
        button.disabled = false;
    }

    // Function to show notification
    function showNotification(message, type = 'success') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-lg shadow-lg text-white ${
            type === 'success' ? 'bg-green-500' :
            type === 'error' ? 'bg-red-500' : 'bg-blue-500'
        }`;
        notification.innerHTML = `
            <div class="flex items-center">
                <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'} mr-2"></i>
                <span>${message}</span>
            </div>
        `;

        document.body.appendChild(notification);

        // Remove notification after 3 seconds
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    // Function to handle export
    function handleExport(format, button) {
        console.log(`Starting export for format: ${format}`);
        showLoading(button);

        const params = new URLSearchParams(window.location.search);
        params.set('format', format);

        // Use window.open to trigger download
        const exportUrl = '/doctors/export-attendance/?' + params.toString();
        console.log(`Export URL: ${exportUrl}`);

        // Try to open in new window/tab for download
        const downloadWindow = window.open(exportUrl, '_blank');

        // If popup blocked, use direct navigation
        if (!downloadWindow) {
            console.log('Popup blocked, using direct navigation');
            window.location.href = exportUrl;
        }

        // Hide loading state after a short delay
        setTimeout(() => {
            hideLoading(button);
            showNotification(`${format.toUpperCase()} export started successfully!`);
        }, 1500);
    }

    // Event listeners for export buttons
    if (exportCsvBtn) {
        console.log('CSV export button found, adding event listener');
        exportCsvBtn.addEventListener('click', function() {
            if (this.disabled) {
                console.log('CSV export button is disabled');
                showNotification('No records available to export', 'error');
                return;
            }
            console.log('CSV export button clicked');
            handleExport('csv', this);
        });
    } else {
        console.log('CSV export button not found');
    }

    if (exportExcelBtn) {
        console.log('Excel export button found, adding event listener');
        exportExcelBtn.addEventListener('click', function() {
            if (this.disabled) {
                console.log('Excel export button is disabled');
                showNotification('No records available to export', 'error');
                return;
            }
            console.log('Excel export button clicked');
            handleExport('excel', this);
        });
    } else {
        console.log('Excel export button not found');
    }

    if (exportPdfBtn) {
        console.log('PDF export button found, adding event listener');
        exportPdfBtn.addEventListener('click', function() {
            if (this.disabled) {
                console.log('PDF export button is disabled');
                showNotification('No records available to export', 'error');
                return;
            }
            console.log('PDF export button clicked');
            handleExport('pdf', this);
        });
    } else {
        console.log('PDF export button not found');
    }
});
</script>
{% endblock %}
