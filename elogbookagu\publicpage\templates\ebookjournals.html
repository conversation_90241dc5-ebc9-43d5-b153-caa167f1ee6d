{% extends 'base.html' %}

{% block title %}
  Medical eBooks & Journals - MedLogBook
{% endblock %}

{% block extra_head %}
  <!-- Additional styles for ebooks & journals page -->
  <style>
    @keyframes float {
      0% { transform: translateY(0px); }
      50% { transform: translateY(-10px); }
      100% { transform: translateY(0px); }
    }

    .animate-float {
      animation: float 6s ease-in-out infinite;
    }

    .resource-card:hover .resource-icon {
      transform: scale(1.1);
    }

    .resource-icon {
      transition: transform 0.3s ease;
    }

    .category-pill.active {
      background-color: #3b82f6;
      color: white;
    }

    .category-pill:not(.active):hover {
      background-color: #e5e7eb;
    }

    .search-input:focus {
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
    }

    .resource-card {
      transition: all 0.3s ease;
    }

    .resource-card:hover {
      transform: translateY(-8px);
    }

    .resource-card .download-btn {
      transition: all 0.3s ease;
    }

    .resource-card:hover .download-btn {
      transform: translateY(-5px);
    }
  </style>

  <!-- Preload critical images -->
  <link rel="preload" as="image" href="https://images.unsplash.com/photo-1532153975070-2e9ab71f1b14?q=80&w=2070" fetchpriority="high">
{% endblock %}

{% block navbar %}
  {% include './components/public_navbar.html' %}
{% endblock %}

{% block content_container %}
  <!-- Enhanced Hero Section with Visual Elements -->
  <section class="relative bg-gradient-to-r from-blue-600 to-purple-700 dark:from-gray-800 dark:to-gray-900 py-24 md:py-32 w-full overflow-hidden">
    <!-- Decorative Elements -->
    <div class="absolute inset-0 overflow-hidden z-0">
      <div class="absolute top-0 right-0 -mt-16 -mr-16 w-64 h-64 bg-white bg-opacity-10 rounded-full"></div>
      <div class="absolute bottom-0 left-0 -mb-16 -ml-16 w-80 h-80 bg-white bg-opacity-10 rounded-full"></div>
      <div class="absolute top-1/2 left-1/4 transform -translate-y-1/2 w-40 h-40 bg-white bg-opacity-5 rounded-full"></div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-20">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
        <div class="text-center md:text-left relative z-30">
          <span class="inline-block px-4 py-1 bg-white bg-opacity-20 rounded-full text-white text-sm font-medium mb-6 animate-fade-in">AGU Medical Library</span>
          <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold text-white leading-tight animate-fade-in">
            Medical <span class="text-yellow-300">eBooks</span> & <span class="text-yellow-300">Journals</span>
          </h1>
          <p class="mt-6 text-xl text-white opacity-90 animate-fade-in delay-100 leading-relaxed">
            Access our comprehensive collection of medical literature, research papers, and educational resources to enhance your medical education.
          </p>

          <!-- Search Bar -->
          <div class="mt-8 relative max-w-md mx-auto md:mx-0 animate-fade-in delay-200">
            <div class="relative">
              <input type="text" placeholder="Search for eBooks & journals..." class="search-input w-full px-5 py-4 pr-12 rounded-lg border-2 border-white border-opacity-20 bg-white bg-opacity-10 text-white placeholder-white placeholder-opacity-75 focus:outline-none focus:border-white transition duration-200">
              <button class="absolute right-3 top-1/2 transform -translate-y-1/2 text-white hover:text-yellow-300 transition duration-200">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
              </button>
            </div>
            <div class="mt-3 flex flex-wrap gap-2 justify-center md:justify-start">
              <span class="text-sm text-white opacity-80">Popular:</span>
              <a href="#" class="text-sm text-white hover:text-yellow-300 transition duration-200">Cardiology</a>
              <a href="#" class="text-sm text-white hover:text-yellow-300 transition duration-200">Pediatrics</a>
              <a href="#" class="text-sm text-white hover:text-yellow-300 transition duration-200">Surgery</a>
            </div>
          </div>
        </div>

        <!-- Hero Image -->
        <div class="hidden md:block animate-float">
          <div class="relative">
            <img src="https://images.unsplash.com/photo-1532153975070-2e9ab71f1b14?q=80&w=2070" alt="Medical eBooks & Journals" class="rounded-lg shadow-2xl w-full object-cover h-auto" loading="eager" />
            <div class="absolute -bottom-4 -right-4 bg-white dark:bg-gray-800 p-4 rounded-lg shadow-lg">
              <div class="flex items-center">
                <div class="bg-purple-500 p-2 rounded-full">
                  <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 4.804A7.968 7.968 0 005.5 4c-1.255 0-2.443.29-3.5.804v10A7.969 7.969 0 015.5 14c1.669 0 3.218.51 4.5 1.385A7.962 7.962 0 0114.5 14c1.255 0 2.443.29 3.5.804v-10A7.968 7.968 0 0014.5 4c-1.255 0-2.443.29-3.5.804V12a1 1 0 11-2 0V4.804z"></path>
                  </svg>
                </div>
                <div class="ml-3">
                  <p class="text-sm font-medium text-gray-900 dark:text-white">500+ Resources</p>
                  <p class="text-xs text-gray-500 dark:text-gray-400">Updated Weekly</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  {% block content %}

  <!-- Categories Section -->
  <section class="py-16 w-full bg-white dark:bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-12">
        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white relative inline-block">
          Browse by Category
          <span class="absolute -bottom-2 left-0 w-full h-1 bg-blue-500 rounded-full"></span>
        </h2>
        <p class="mt-6 text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
          Explore our comprehensive collection of medical literature organized by specialty.
        </p>
      </div>

      <!-- Category Pills -->
      <div class="flex flex-wrap justify-center gap-3 mb-12">
        <button class="category-pill active px-6 py-2 rounded-full bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200 font-medium text-sm transition-all duration-200 hover:shadow-md">
          All Categories
        </button>
        <button class="category-pill px-6 py-2 rounded-full bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200 font-medium text-sm transition-all duration-200 hover:shadow-md">
          Cardiology
        </button>
        <button class="category-pill px-6 py-2 rounded-full bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200 font-medium text-sm transition-all duration-200 hover:shadow-md">
          Pediatrics
        </button>
        <button class="category-pill px-6 py-2 rounded-full bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200 font-medium text-sm transition-all duration-200 hover:shadow-md">
          Surgery
        </button>
        <button class="category-pill px-6 py-2 rounded-full bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200 font-medium text-sm transition-all duration-200 hover:shadow-md">
          Neurology
        </button>
        <button class="category-pill px-6 py-2 rounded-full bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200 font-medium text-sm transition-all duration-200 hover:shadow-md">
          Dermatology
        </button>
        <button class="category-pill px-6 py-2 rounded-full bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200 font-medium text-sm transition-all duration-200 hover:shadow-md">
          Psychiatry
        </button>
      </div>

      <!-- Featured Resources -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
        <!-- Featured Resource Card 1 -->
        <div class="resource-card bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300">
          <div class="relative">
            <img src="https://images.unsplash.com/photo-1576091160550-2173dba999ef?q=80&w=2070" alt="Medical Research Journal" class="w-full h-52 object-cover transition-transform duration-500 group-hover:scale-105" loading="lazy" />
            <div class="absolute top-4 left-4 bg-blue-600 text-white text-xs font-bold px-3 py-1 rounded-full">
              FEATURED
            </div>
            <div class="absolute top-4 right-4 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 text-xs font-medium px-3 py-1 rounded-full flex items-center">
              <svg class="w-3 h-3 mr-1 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
              </svg>
              4.9 (128)
            </div>
          </div>
          <div class="p-6">
            <div class="flex items-center mb-3">
              <span class="text-blue-600 dark:text-blue-400 text-sm font-medium bg-blue-100 dark:bg-blue-900/30 px-2.5 py-0.5 rounded-full">Medical Research</span>
              <span class="ml-2 text-xs text-gray-500 dark:text-gray-400">Updated 2 weeks ago</span>
            </div>
            <h3 class="text-xl font-bold mb-2 text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-200">Advanced Medical Research Journal</h3>
            <p class="text-gray-600 dark:text-gray-300 mb-4">Comprehensive collection of cutting-edge medical research papers and case studies from leading institutions.</p>
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="Author" class="w-8 h-8 rounded-full mr-2" />
                <span class="text-sm text-gray-600 dark:text-gray-300">Dr. Michael Chen</span>
              </div>
              <a href="{% url 'ebookjournals_download' 'pediatrics' %}" class="download-btn flex items-center text-white bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg transition-colors duration-200">
                <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
                Download
              </a>
            </div>
          </div>
        </div>

        <!-- Featured Resource Card 2 -->
        <div class="resource-card bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300">
          <div class="relative">
            <img src="https://images.unsplash.com/photo-1505751172876-fa1923c5c528?q=80&w=2070" alt="Pediatric Care Handbook" class="w-full h-52 object-cover transition-transform duration-500 group-hover:scale-105" loading="lazy" />
            <div class="absolute top-4 left-4 bg-green-600 text-white text-xs font-bold px-3 py-1 rounded-full">
              NEW
            </div>
            <div class="absolute top-4 right-4 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 text-xs font-medium px-3 py-1 rounded-full flex items-center">
              <svg class="w-3 h-3 mr-1 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
              </svg>
              4.7 (93)
            </div>
          </div>
          <div class="p-6">
            <div class="flex items-center mb-3">
              <span class="text-green-600 dark:text-green-400 text-sm font-medium bg-green-100 dark:bg-green-900/30 px-2.5 py-0.5 rounded-full">Pediatrics</span>
              <span class="ml-2 text-xs text-gray-500 dark:text-gray-400">Updated 3 days ago</span>
            </div>
            <h3 class="text-xl font-bold mb-2 text-gray-900 dark:text-white group-hover:text-green-600 dark:group-hover:text-green-400 transition-colors duration-200">Pediatric Care Handbook</h3>
            <p class="text-gray-600 dark:text-gray-300 mb-4">Complete guide to pediatric care including assessment techniques, treatment protocols, and developmental milestones.</p>
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="Author" class="w-8 h-8 rounded-full mr-2" />
                <span class="text-sm text-gray-600 dark:text-gray-300">Dr. Sarah Johnson</span>
              </div>
              <a href="{% url 'ebookjournals_download' 'pediatrics' %}" class="download-btn flex items-center text-white bg-green-600 hover:bg-green-700 px-4 py-2 rounded-lg transition-colors duration-200">
                <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
                Download
              </a>
            </div>
          </div>
        </div>

        <!-- Featured Resource Card 3 -->
        <div class="resource-card bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300">
          <div class="relative">
            <img src="https://images.unsplash.com/photo-1559757175-7cb057fba93c?q=80&w=2073" alt="Neurological Disorders Handbook" class="w-full h-52 object-cover transition-transform duration-500 group-hover:scale-105" loading="lazy" />
            <div class="absolute top-4 right-4 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 text-xs font-medium px-3 py-1 rounded-full flex items-center">
              <svg class="w-3 h-3 mr-1 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
              </svg>
              4.8 (156)
            </div>
          </div>
          <div class="p-6">
            <div class="flex items-center mb-3">
              <span class="text-purple-600 dark:text-purple-400 text-sm font-medium bg-purple-100 dark:bg-purple-900/30 px-2.5 py-0.5 rounded-full">Neurology</span>
              <span class="ml-2 text-xs text-gray-500 dark:text-gray-400">Updated 1 month ago</span>
            </div>
            <h3 class="text-xl font-bold mb-2 text-gray-900 dark:text-white group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors duration-200">Neurological Disorders Handbook</h3>
            <p class="text-gray-600 dark:text-gray-300 mb-4">Essential reference guide covering diagnosis and treatment of common neurological conditions with detailed case studies.</p>
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <img src="https://randomuser.me/api/portraits/men/67.jpg" alt="Author" class="w-8 h-8 rounded-full mr-2" />
                <span class="text-sm text-gray-600 dark:text-gray-300">Dr. Ahmed Al-Farsi</span>
              </div>
              <a href="{% url 'ebookjournals_download' 'pediatrics' %}" class="download-btn flex items-center text-white bg-purple-600 hover:bg-purple-700 px-4 py-2 rounded-lg transition-colors duration-200">
                <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
                Download
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- eBooks & Journals Section - Full Width -->
  <section class="py-16 w-full">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <h2 class="text-3xl font-bold text-center mb-12">Available Resources</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <!-- Resource Card 1 -->
        <div class="rounded-lg overflow-hidden shadow-2xl transform transition-all hover:scale-105 hover:rotate-1 hover:shadow-3xl bg-white dark:bg-gray-800">
          <img class="w-full h-48 object-cover transition-transform hover:scale-110" src="/img/card-top.jpg" alt="Pediatrics Resource" />
          <div class="px-6 py-4">
            <div class="font-bold text-xl mb-2 text-gray-800 dark:text-white">The Pediatrics Terms</div>
            <p class="text-gray-600 dark:text-gray-300 text-base">The Terms and Conditions for the Pediatric Department and also some other information</p>
            <a href="{% url 'ebookjournals_download' 'pediatrics' %}" class="inline-block bg-blue-600 hover:bg-blue-800 text-white font-bold mt-4 rounded-full px-6 py-3 transition-colors duration-300"><i class="fa-solid fa-file-pdf"></i> Download PDF</a>
          </div>
          <div class="px-6 pt-4 pb-6">
            <span class="inline-block bg-gray-100 dark:bg-gray-700 rounded-full px-3 py-1 text-sm font-semibold text-gray-700 dark:text-gray-300 mr-2 mb-2 shadow-sm hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">#pediatric</span>
            <span class="inline-block bg-gray-100 dark:bg-gray-700 rounded-full px-3 py-1 text-sm font-semibold text-gray-700 dark:text-gray-300 mr-2 mb-2 shadow-sm hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">#agu</span>
            <span class="inline-block bg-gray-100 dark:bg-gray-700 rounded-full px-3 py-1 text-sm font-semibold text-gray-700 dark:text-gray-300 mr-2 mb-2 shadow-sm hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">#bahrainagu</span>
          </div>
        </div>

        <!-- Resource Card 2 -->
        <div class="rounded-lg overflow-hidden shadow-2xl transform transition-all hover:scale-105 hover:rotate-1 hover:shadow-3xl bg-white dark:bg-gray-800">
          <img class="w-full h-48 object-cover transition-transform hover:scale-110" src="/img/card-top.jpg" alt="Surgery Resource" />
          <div class="px-6 py-4">
            <div class="font-bold text-xl mb-2 text-gray-800 dark:text-white">Surgery Guidelines</div>
            <p class="text-gray-600 dark:text-gray-300 text-base">Comprehensive guidelines for surgical procedures and protocols for medical students</p>
            <a href="{% url 'ebookjournals_download' 'pediatrics' %}" class="inline-block bg-blue-600 hover:bg-blue-800 text-white font-bold mt-4 rounded-full px-6 py-3 transition-colors duration-300"><i class="fa-solid fa-file-pdf"></i> Download PDF</a>
          </div>
          <div class="px-6 pt-4 pb-6">
            <span class="inline-block bg-gray-100 dark:bg-gray-700 rounded-full px-3 py-1 text-sm font-semibold text-gray-700 dark:text-gray-300 mr-2 mb-2 shadow-sm hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">#surgery</span>
            <span class="inline-block bg-gray-100 dark:bg-gray-700 rounded-full px-3 py-1 text-sm font-semibold text-gray-700 dark:text-gray-300 mr-2 mb-2 shadow-sm hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">#agu</span>
            <span class="inline-block bg-gray-100 dark:bg-gray-700 rounded-full px-3 py-1 text-sm font-semibold text-gray-700 dark:text-gray-300 mr-2 mb-2 shadow-sm hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">#medical</span>
          </div>
        </div>

        <!-- Resource Card 3 -->
        <div class="rounded-lg overflow-hidden shadow-2xl transform transition-all hover:scale-105 hover:rotate-1 hover:shadow-3xl bg-white dark:bg-gray-800">
          <img class="w-full h-48 object-cover transition-transform hover:scale-110" src="/img/card-top.jpg" alt="Cardiology Resource" />
          <div class="px-6 py-4">
            <div class="font-bold text-xl mb-2 text-gray-800 dark:text-white">Cardiology Handbook</div>
            <p class="text-gray-600 dark:text-gray-300 text-base">Essential reference guide for cardiology students with detailed diagrams and case studies</p>
            <a href="{% url 'ebookjournals_download' 'pediatrics' %}" class="inline-block bg-blue-600 hover:bg-blue-800 text-white font-bold mt-4 rounded-full px-6 py-3 transition-colors duration-300"><i class="fa-solid fa-file-pdf"></i> Download PDF</a>
          </div>
          <div class="px-6 pt-4 pb-6">
            <span class="inline-block bg-gray-100 dark:bg-gray-700 rounded-full px-3 py-1 text-sm font-semibold text-gray-700 dark:text-gray-300 mr-2 mb-2 shadow-sm hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">#cardiology</span>
            <span class="inline-block bg-gray-100 dark:bg-gray-700 rounded-full px-3 py-1 text-sm font-semibold text-gray-700 dark:text-gray-300 mr-2 mb-2 shadow-sm hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">#agu</span>
            <span class="inline-block bg-gray-100 dark:bg-gray-700 rounded-full px-3 py-1 text-sm font-semibold text-gray-700 dark:text-gray-300 mr-2 mb-2 shadow-sm hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">#heart</span>
          </div>
        </div>
      </div>

      <!-- Additional Resources Section -->
      <div class="mt-12 text-center">
        <p class="text-gray-600 dark:text-gray-300 mb-6">Looking for more resources? Check our complete library or request specific materials.</p>
        <a href="#" class="inline-block bg-blue-600 hover:bg-blue-800 text-white font-bold rounded-lg px-8 py-3 transition-colors duration-300 mr-4">Browse Library</a>
        <a href="#" class="inline-block bg-gray-200 dark:bg-gray-700 hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-800 dark:text-white font-bold rounded-lg px-8 py-3 transition-colors duration-300">Request Materials</a>
      </div>
    </div>
  </section>
  {% endblock %}
{% endblock %}
