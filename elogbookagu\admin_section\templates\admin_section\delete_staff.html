{% extends 'base.html' %}

{% block title %}
  Delete Staff
{% endblock %}

{% block navbar %}
  {% include 'components/admin_auth_navbar.html' %}
{% endblock %}

{% block content %}
  <div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-8">
      <h1 class="text-3xl font-bold text-gray-800 dark:text-white">Delete Staff</h1>
      <a href="{% url 'admin_section:add_staff' %}" class="px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg transition-colors duration-200 flex items-center">
        <i class="fas fa-arrow-left mr-2"></i> Back to Staff List
      </a>
    </div>

    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 max-w-2xl mx-auto">
      <div class="text-center mb-6">
        <div class="inline-flex items-center justify-center h-24 w-24 rounded-full bg-red-100 dark:bg-red-900 mb-4">
          <i class="fas fa-exclamation-triangle text-4xl text-red-600 dark:text-red-400"></i>
        </div>
        <h2 class="text-2xl font-bold text-gray-800 dark:text-white">Confirm Deletion</h2>
        <p class="text-gray-600 dark:text-gray-300 mt-2">Are you sure you want to delete this staff member? This action cannot be undone.</p>
      </div>

      <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-6 mb-6">
        <div class="flex items-center mb-4">
          <div class="flex-shrink-0 h-16 w-16 mr-4">
            {% if staff.user.profile_photo %}
              <img class="h-16 w-16 rounded-full object-cover" src="{{ staff.user.profile_photo.url }}" alt="{{ staff.user.get_full_name }}">
            {% else %}
              <div class="h-16 w-16 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                <span class="text-blue-600 dark:text-blue-300 text-xl font-medium">{{ staff.user.first_name|first }}{{ staff.user.last_name|first }}</span>
              </div>
            {% endif %}
          </div>
          <div>
            <h3 class="text-xl font-semibold text-gray-800 dark:text-white">{{ staff.user.get_full_name }}</h3>
            <p class="text-gray-500 dark:text-gray-400">{{ staff.user.email }}</p>
          </div>
        </div>

        <div class="space-y-2 text-sm">
          <div class="flex justify-between">
            <span class="text-gray-500 dark:text-gray-400">Username:</span>
            <span class="text-gray-800 dark:text-white font-medium">{{ staff.user.username }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-500 dark:text-gray-400">Departments:</span>
            <span class="text-gray-800 dark:text-white font-medium">
              {% for department in staff.departments.all %}
                {{ department.name }}{% if not forloop.last %}, {% endif %}
              {% empty %}
                None
              {% endfor %}
            </span>
          </div>
        </div>
      </div>

      <div class="flex justify-center space-x-4">
        <a href="{% url 'admin_section:add_staff' %}" class="px-6 py-3 bg-gray-500 hover:bg-gray-600 text-white rounded-lg transition-colors duration-200 flex items-center">
          <i class="fas fa-times mr-2"></i> Cancel
        </a>
        <form method="post" class="inline">
          {% csrf_token %}
          <button type="submit" class="px-6 py-3 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors duration-200 flex items-center">
            <i class="fas fa-trash-alt mr-2"></i> Delete Staff
          </button>
        </form>
      </div>
    </div>
  </div>
{% endblock %}
