{% comment %}Optimized public navbar component{% endcomment %}
<nav class="bg-white dark:bg-gray-800 shadow-md w-full sticky top-0 z-50 transition-colors duration-300">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex justify-between items-center h-16">
      <!-- Logo -->
      <div class="flex-shrink-0 flex items-center">
        <a href="{% url 'home_page' %}" class="text-xl font-bold text-gray-900 dark:text-white hover:scale-105 transition transform duration-300 flex items-center">
          <i class="fas fa-book-medical mr-2"></i>
          <span>AguELogBook</span>
        </a>
      </div>

      <!-- Desktop Menu -->
      <div class="hidden md:flex items-center space-x-6">
        <div class="flex space-x-6">
          {% with url_name=request.resolver_match.url_name %}
            <!-- Home Link -->
            <a href="{% url 'home_page' %}"
               class="nav-link px-4 py-2 rounded-lg transition-all duration-300 transform hover:scale-105 flex items-center
               {% if url_name == 'home_page' %}
                 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300
               {% else %}
                 text-gray-900 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700
               {% endif %}">
              <i class="fas fa-home mr-1.5"></i>
              <span>Home</span>
            </a>

            {# About and Resources links have been removed #}

            <!-- Updates Link -->
            <a href="{% url 'update_page' %}"
               class="nav-link px-4 py-2 rounded-lg transition-all duration-300 transform hover:scale-105 flex items-center
               {% if url_name == 'update_page' %}
                 bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300
               {% else %}
                 text-gray-900 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700
               {% endif %}">
              <i class="fas fa-sync-alt mr-1.5"></i>
              <span>Updates</span>
            </a>
          {% endwith %}
        </div>

        <!-- Login Button -->
        <a href="{% url 'login' %}" class="px-4 py-2 bg-blue-600 text-white font-medium rounded-xl shadow-md hover:bg-blue-700 hover:shadow-lg transition-all duration-300 transform hover:scale-105 hover:-translate-y-0.5 flex items-center">
          <i class="fas fa-sign-in-alt mr-1.5"></i>
          <span>Login</span>
        </a>

        <!-- Theme Toggle Button -->
        <button id="theme-toggle-btn" aria-label="Toggle dark mode" class="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-300">
          <svg id="theme-toggle-dark-icon" class="w-6 h-6 text-gray-900 dark:text-white hidden" fill="currentColor" viewBox="0 0 20 20">
            <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"></path>
          </svg>
          <svg id="theme-toggle-light-icon" class="w-6 h-6 text-gray-900 dark:text-white" fill="currentColor" viewBox="0 0 20 20">
            <path d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z"></path>
          </svg>
        </button>
      </div>

      <!-- Mobile Menu Button -->
      <div class="md:hidden">
        <button id="mobile-menu-button" aria-label="Open menu" class="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-300">
          <svg class="w-6 h-6 text-gray-900 dark:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path id="mobile-menu-icon" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
          </svg>
        </button>
      </div>
    </div>
  </div>

  <!-- Mobile Menu -->
  <div id="mobile-menu" class="md:hidden hidden bg-white dark:bg-gray-800 shadow-inner transition-all duration-300 ease-in-out">
    <div class="px-4 py-3 space-y-2">
      {% with url_name=request.resolver_match.url_name %}
        <!-- Home Link -->
        <a href="{% url 'home_page' %}"
           class="flex items-center justify-center p-3 rounded-lg transition-all duration-300 transform hover:scale-105
           {% if url_name == 'home_page' %}
             bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300
           {% else %}
             text-gray-900 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700
           {% endif %}">
          <i class="fas fa-home mr-1.5"></i>
          <span>Home</span>
        </a>

        {# About and Resources mobile links have been removed #}

        <!-- Updates Link -->
        <a href="{% url 'update_page' %}"
           class="flex items-center justify-center p-3 rounded-lg transition-all duration-300 transform hover:scale-105
           {% if url_name == 'update_page' %}
             bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300
           {% else %}
             text-gray-900 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700
           {% endif %}">
          <i class="fas fa-sync-alt mr-1.5"></i>
          <span>Updates</span>
        </a>
      {% endwith %}

      <div class="pt-2 flex justify-center">
        <a href="{% url 'login' %}" class="px-4 py-2 bg-blue-600 text-white font-medium rounded-xl shadow-md hover:bg-blue-700 hover:shadow-lg transition-all duration-300 transform hover:scale-105 hover:-translate-y-0.5 flex items-center justify-center">
          <i class="fas fa-sign-in-alt mr-1.5"></i>
          <span>Login</span>
        </a>
      </div>
    </div>
  </div>
</nav>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    // Mobile menu toggle
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');
    const mobileMenuIcon = document.getElementById('mobile-menu-icon');

    if (mobileMenuButton && mobileMenu && mobileMenuIcon) {
      mobileMenuButton.addEventListener('click', () => {
        mobileMenu.classList.toggle('hidden');
        // Change the icon
        if (mobileMenu.classList.contains('hidden')) {
          mobileMenuIcon.setAttribute('d', 'M4 6h16M4 12h16M4 18h16'); // Hamburger
          mobileMenuButton.setAttribute('aria-expanded', 'false');
        } else {
          mobileMenuIcon.setAttribute('d', 'M6 18L18 6M6 6l12 12'); // Close (X)
          mobileMenuButton.setAttribute('aria-expanded', 'true');
        }
      });
    }

    // Theme toggle functionality
    const themeToggleBtn = document.getElementById('theme-toggle-btn');
    if (themeToggleBtn) {
      themeToggleBtn.addEventListener('click', toggleTheme);
    }

    // Initialize theme icons based on current theme
    const isDark = document.documentElement.classList.contains('dark');
    const darkIcon = document.getElementById('theme-toggle-dark-icon');
    const lightIcon = document.getElementById('theme-toggle-light-icon');
    if (darkIcon && lightIcon) {
      darkIcon.classList.toggle('hidden', !isDark);
      lightIcon.classList.toggle('hidden', isDark);
    }
  });

  // Theme toggle functionality is now handled by the toggleTheme function in base.html
</script>

<!-- Styles -->
<style>
  /* Container adjustments */
  .container {
    padding-top: 0; /* Remove extra padding that causes misalignment */
    margin: 0 auto; /* Center content properly */
    width: 100%;
  }

  /* Form element styling */
  input,
  select {
    color: #000 !important;
    background-color: #fff !important;
    padding: 0.5rem;
    border: 1px solid #d1d5db !important;
    border-radius: 0.375rem !important;
  }

  .dark input,
  .dark select {
    color: #fff !important;
    background-color: #4a5568 !important;
    border: 1px solid #6b7280 !important;
  }

  /* Smooth transitions for mobile menu */
  #mobile-menu {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-in-out;
  }

  #mobile-menu:not(.hidden) {
    max-height: 500px;
  }
</style>
