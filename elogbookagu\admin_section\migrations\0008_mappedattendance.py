# Generated by Django 5.1.4 on 2025-06-02 08:45

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0008_alter_staff_departments'),
        ('admin_section', '0007_daterestrictionsettings_doctor_notification_days_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='MappedAttendance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Name for this attendance mapping', max_length=100)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('doctors', models.ManyToManyField(blank=True, related_name='mapped_attendances', to='accounts.doctor')),
                ('groups', models.ManyToManyField(blank=True, related_name='mapped_attendances', to='admin_section.group')),
                ('log_year', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='mapped_attendances', to='admin_section.logyear')),
                ('log_year_section', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='mapped_attendances', to='admin_section.logyearsection')),
                ('training_site', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='mapped_attendances', to='admin_section.trainingsite')),
            ],
            options={
                'verbose_name': 'Mapped Attendance',
                'verbose_name_plural': 'Mapped Attendances',
                'ordering': ['-created_at'],
                'unique_together': {('name', 'training_site', 'log_year')},
            },
        ),
    ]
