{% extends 'base.html' %}

{% block title %}
  Add Training Site
{% endblock %}

{% block navbar %}
{% include 'components/admin_auth_navbar.html' %}
{% endblock %}

{% block content %}
  <div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
      <div>
        <h2 class="text-3xl font-bold text-gray-800 dark:text-white">Training Sites</h2>
        <p class="text-gray-600 dark:text-gray-300 mt-1">Add and manage training sites for student logs</p>
      </div>
    </div>

    <!-- Messages section -->
    {% if messages %}
      <div class="mb-6">
        {% for message in messages %}
          <div class="p-4 mb-4 rounded-lg {% if message.tags == 'success' %}bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100{% elif message.tags == 'error' %}bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100{% else %}bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100{% endif %}">
            {{ message }}
          </div>
        {% endfor %}
      </div>
    {% endif %}

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Add Training Site Form -->
      <div class="lg:col-span-1">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
          <h3 class="text-xl font-semibold text-gray-800 dark:text-white mb-4">Add New Training Site</h3>
          <form method="post" action="{% url 'admin_section:add_training_site' %}">
            {% csrf_token %}
            
            <div class="mb-4">
              <label for="{{ form.name.id_for_label }}" class="block text-gray-700 dark:text-gray-300 mb-2">Training Site Name</label>
              {{ form.name }}
              {% if form.name.errors %}
                <p class="text-red-500 text-sm mt-1">{{ form.name.errors.0 }}</p>
              {% endif %}
            </div>
            
            <div class="mb-6">
              <label for="{{ form.log_year.id_for_label }}" class="block text-gray-700 dark:text-gray-300 mb-2">Academic Year</label>
              {{ form.log_year }}
              {% if form.log_year.errors %}
                <p class="text-red-500 text-sm mt-1">{{ form.log_year.errors.0 }}</p>
              {% endif %}
            </div>
            
            <button type="submit" class="w-full bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 px-4 rounded-lg transition duration-200">
              Add Training Site
            </button>
          </form>
        </div>
      </div>
      
      <!-- Training Sites List -->
      <div class="lg:col-span-2">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
          <h3 class="text-xl font-semibold text-gray-800 dark:text-white mb-4">Existing Training Sites</h3>
          
          {% if training_sites %}
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Name</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Academic Year</th>
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {% for site in training_sites %}
                    <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                      <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">{{ site.name }}</td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">{{ site.log_year.year_name }}</td>
                      <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <a href="{% url 'admin_section:edit_training_site' site.id %}" class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3">Edit</a>
                        <a href="{% url 'admin_section:delete_training_site' site.id %}" class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300" onclick="return confirm('Are you sure you want to delete this training site?');">Delete</a>
                      </td>
                    </tr>
                  {% endfor %}
                </tbody>
              </table>
            </div>
            
            <!-- Pagination -->
            {% if training_sites.has_other_pages %}
              <div class="flex justify-center mt-6">
                <nav class="inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                  {% if training_sites.has_previous %}
                    <a href="?page={{ training_sites.previous_page_number }}" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white dark:bg-gray-700 dark:border-gray-600 text-sm font-medium text-gray-500 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600">
                      <span class="sr-only">Previous</span>
                      <i class="fas fa-chevron-left"></i>
                    </a>
                  {% else %}
                    <span class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-gray-100 dark:bg-gray-800 dark:border-gray-600 text-sm font-medium text-gray-400 dark:text-gray-500">
                      <span class="sr-only">Previous</span>
                      <i class="fas fa-chevron-left"></i>
                    </span>
                  {% endif %}
                  
                  {% for i in training_sites.paginator.page_range %}
                    {% if training_sites.number == i %}
                      <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 dark:bg-blue-900 dark:border-gray-600 text-sm font-medium text-blue-600 dark:text-blue-200">
                        {{ i }}
                      </span>
                    {% else %}
                      <a href="?page={{ i }}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white dark:bg-gray-700 dark:border-gray-600 text-sm font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600">
                        {{ i }}
                      </a>
                    {% endif %}
                  {% endfor %}
                  
                  {% if training_sites.has_next %}
                    <a href="?page={{ training_sites.next_page_number }}" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white dark:bg-gray-700 dark:border-gray-600 text-sm font-medium text-gray-500 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600">
                      <span class="sr-only">Next</span>
                      <i class="fas fa-chevron-right"></i>
                    </a>
                  {% else %}
                    <span class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-gray-100 dark:bg-gray-800 dark:border-gray-600 text-sm font-medium text-gray-400 dark:text-gray-500">
                      <span class="sr-only">Next</span>
                      <i class="fas fa-chevron-right"></i>
                    </span>
                  {% endif %}
                </nav>
              </div>
            {% endif %}
          {% else %}
            <div class="text-center py-8">
              <p class="text-gray-500 dark:text-gray-400">No training sites found. Add your first one!</p>
            </div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>
{% endblock %}
