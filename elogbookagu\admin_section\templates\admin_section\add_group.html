{% extends 'base.html' %}

{% block title %}
  {% if is_edit %}Edit Group{% else %}Add Group{% endif %}
{% endblock %}

{% block navbar %}
{% include 'components/admin_auth_navbar.html' %}
{% endblock %}

{% block content %}
  <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-10 lg:py-12">
    <!-- Page Header -->
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
      <div>
        <h1 class="text-2xl sm:text-3xl font-bold text-gray-800 dark:text-gray-100">
          <i class="fas fa-users text-blue-500 mr-2"></i> {% if is_edit %}Edit Group: {{ group.group_name }}{% else %}Student Groups{% endif %}
        </h1>
        <p class="text-gray-600 dark:text-gray-400 mt-1">{% if is_edit %}Update group information{% else %}Create and manage student groups for academic activities{% endif %}</p>
      </div>
    </div>

    <!-- Messages section -->
    {% if messages %}
      <div class="fixed top-4 right-4 z-50 w-full max-w-sm">
        {% for message in messages %}
          <div class="p-4 mb-3 rounded-lg shadow-lg flex items-start {% if message.tags == 'success' %}bg-green-100 text-green-800 dark:bg-green-900/70 dark:text-green-200{% elif message.tags == 'error' %}bg-red-100 text-red-800 dark:bg-red-900/70 dark:text-red-200{% else %}bg-blue-100 text-blue-800 dark:bg-blue-900/70 dark:text-blue-200{% endif %} transform transition-all duration-300 animate-fade-in-down">
            <div class="flex-shrink-0">
              <i class="fas {% if message.tags == 'success' %}fa-check-circle text-green-500{% elif message.tags == 'error' %}fa-exclamation-circle text-red-500{% else %}fa-info-circle text-blue-500{% endif %} mt-0.5"></i>
            </div>
            <div class="ml-3 flex-grow">
              <p class="text-sm font-medium">{{ message }}</p>
            </div>
            <button type="button" class="ml-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 close-message">
              <i class="fas fa-times"></i>
            </button>
          </div>
        {% endfor %}
      </div>
    {% endif %}

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Group Form -->
      <div class="lg:col-span-1">
        <div class="bg-white dark:bg-gray-800 shadow-lg rounded-lg overflow-hidden border-t-4 {% if is_edit %}border-yellow-500{% else %}border-blue-500{% endif %}">
          <div class="p-6">
            <div class="flex items-center mb-6">
              <div class="flex-shrink-0 mr-4 {% if is_edit %}text-yellow-500{% else %}text-blue-500{% endif %}">
                <i class="fas {% if is_edit %}fa-edit text-2xl{% else %}fa-plus-circle text-2xl{% endif %}"></i>
              </div>
              <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-100">
                {{ is_edit|yesno:'Edit Existing,Add New' }} Group
              </h2>
            </div>

            <!-- Form Errors Summary -->
            {% if form.errors or form.non_field_errors %}
            <div class="mb-6 p-4 bg-red-50 dark:bg-red-900/20 rounded-lg border-l-4 border-red-500">
              <div class="flex items-start">
                <div class="flex-shrink-0">
                  <i class="fas fa-exclamation-circle text-red-600 dark:text-red-400"></i>
                </div>
                <div class="ml-3">
                  <h4 class="text-sm font-medium text-red-800 dark:text-red-300">Please correct the following errors:</h4>
                  {% if form.non_field_errors %}
                  <ul class="mt-1 text-sm text-red-700 dark:text-red-300 list-disc list-inside">
                    {% for error in form.non_field_errors %}
                    <li>{{ error }}</li>
                    {% endfor %}
                  </ul>
                  {% endif %}
                </div>
              </div>
            </div>
            {% endif %}

            <form method="post" action="{% if is_edit %}{% url 'admin_section:edit_group' group.id %}{% else %}{% url 'admin_section:add_group' %}{% endif %}" id="groupForm">
              {% csrf_token %}

              <!-- Group Name Field -->
              <div class="space-y-1 mb-6">
                <label for="id_group_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Group Name <span class="text-red-500">*</span>
                </label>
                <div class="w-full">
                  {{ form.group_name.as_widget|safe }}
                </div>
                <style>
                  #id_group_name {
                    width: 100%;
                    padding: 0.5rem 1rem;
                    border-radius: 0.5rem;
                    border: 1px solid #d1d5db;
                    background-color: white;
                    color: #111827;
                  }
                  .dark #id_group_name {
                    background-color: #374151;
                    border-color: #4b5563;
                    color: white;
                  }
                  #id_group_name:focus {
                    outline: none;
                    border-color: #3b82f6;
                    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.25);
                  }
                </style>
                {% if form.group_name.errors %}
                  <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.group_name.errors.0 }}</p>
                {% else %}
                  <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Enter a name for the group (e.g., A1, B2, etc.)</p>
                {% endif %}
              </div>

              <!-- Academic Year Field -->
              <div class="space-y-1 mb-6">
                <label for="id_log_year" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Academic Year <span class="text-red-500">*</span>
                </label>
                <div class="w-full">
                  {{ form.log_year.as_widget|safe }}
                </div>
                <style>
                  #id_log_year {
                    width: 100%;
                    padding: 0.5rem 1rem;
                    border-radius: 0.5rem;
                    border: 1px solid #d1d5db;
                    background-color: white;
                    color: #111827;
                  }
                  .dark #id_log_year {
                    background-color: #374151;
                    border-color: #4b5563;
                    color: white;
                  }
                  #id_log_year:focus {
                    outline: none;
                    border-color: #3b82f6;
                    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.25);
                  }
                </style>
                {% if form.log_year.errors %}
                  <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.log_year.errors.0 }}</p>
                {% else %}
                  <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Select the academic year for this group</p>
                {% endif %}
              </div>

              <!-- Year Section Field -->
              <div class="space-y-1 mb-6">
                <label for="id_log_year_section" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Year Section <span class="text-red-500">*</span>
                </label>
                <div class="relative">
                  <div class="w-full">
                    {{ form.log_year_section.as_widget|safe }}
                  </div>
                  <style>
                    #id_log_year_section {
                      width: 100%;
                      padding: 0.5rem 1rem;
                      border-radius: 0.5rem;
                      border: 1px solid #d1d5db;
                      background-color: white;
                      color: #111827;
                    }
                    .dark #id_log_year_section {
                      background-color: #374151;
                      border-color: #4b5563;
                      color: white;
                    }
                    #id_log_year_section:focus {
                      outline: none;
                      border-color: #3b82f6;
                      box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.25);
                    }
                  </style>
                  <div id="section-loading" class="hidden absolute inset-y-0 right-0 pr-3 pointer-events-none" style="display: none;">
                    <i class="fas fa-spinner fa-spin text-blue-500"></i>
                  </div>
                </div>
                {% if form.log_year_section.errors %}
                  <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.log_year_section.errors.0 }}</p>
                {% else %}
                  <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Select academic year first, then choose year section</p>
                {% endif %}
              </div>

              <!-- Form Actions -->
              <div class="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
                {% if is_edit %}
                  <a href="{% url 'admin_section:add_group' %}" class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-800 rounded-lg border border-gray-300 dark:border-gray-600 hover:bg-gray-200 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors duration-200">
                    <i class="fas fa-times mr-1"></i> Cancel
                  </a>
                  <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-yellow-500 rounded-lg hover:bg-yellow-600 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 transition-colors duration-200">
                    <i class="fas fa-save mr-1"></i> Update Group
                  </button>
                {% else %}
                  <button type="reset" class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-800 rounded-lg border border-gray-300 dark:border-gray-600 hover:bg-gray-200 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors duration-200">
                    <i class="fas fa-undo mr-1"></i> Reset
                  </button>
                  <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200">
                    <i class="fas fa-plus-circle mr-1"></i> Add Group
                  </button>
                {% endif %}
              </div>
            </form>
          </div>
        </div>

        <!-- Search Section -->
        <div class="bg-white dark:bg-gray-800 shadow-lg rounded-lg overflow-hidden border-t-4 border-purple-500 mt-6">
          <div class="p-6">
            <div class="flex items-center mb-4">
              <div class="flex-shrink-0 mr-3 text-purple-500">
                <i class="fas fa-search text-xl"></i>
              </div>
              <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-100">Search Groups</h3>
            </div>

            <form method="get" action="{% url 'admin_section:add_group' %}" class="space-y-4">
              <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <i class="fas fa-search text-gray-400"></i>
                </div>
                <input type="text" name="q" value="{{ search_query }}" class="w-full pl-10 pr-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white" placeholder="Search by group name...">
              </div>

              <div class="space-y-1">
                <label for="year_section" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Filter by Year Section</label>
                <select name="year_section" class="w-full px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-purple-500 focus:border-purple-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                  <option value="">All Year Sections</option>
                  {% for section in year_sections %}
                    <option value="{{ section.id }}" {% if selected_year_section == section.id|stringformat:"s" %}selected{% endif %}>
                      {{ section.year_name.year_name }} - {{ section.year_section_name }}
                    </option>
                  {% endfor %}
                </select>
              </div>

              <div class="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
                {% if search_query or selected_year_section %}
                  <a href="{% url 'admin_section:add_group' %}" class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-800 rounded-lg border border-gray-300 dark:border-gray-600 hover:bg-gray-200 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors duration-200">
                    <i class="fas fa-times mr-1"></i> Clear
                  </a>
                {% endif %}
                <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-purple-600 rounded-lg hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 transition-colors duration-200">
                  <i class="fas fa-search mr-1"></i> Apply Filters
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>

      <!-- Groups Table -->
      <div class="lg:col-span-2">
        <div class="bg-white dark:bg-gray-800 shadow-lg rounded-lg overflow-hidden border-t-4 border-green-500">
          <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div class="flex items-center">
              <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-100 flex items-center">
                <i class="fas fa-users text-green-500 mr-2"></i> Student Groups
                <span class="ml-2 px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">{{ groups.paginator.count }}</span>
              </h3>
              {% if selected_year_section or search_query %}
              <div class="ml-4 flex items-center text-sm text-gray-600 dark:text-gray-400">
                {% if selected_year_section %}
                <span class="ml-1 px-2 py-1 bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200 rounded-full text-xs font-medium">
                  {% for section in year_sections %}
                    {% if selected_year_section == section.id|stringformat:'s' %}
                      {{ section.year_name.year_name }} - {{ section.year_section_name }}
                    {% endif %}
                  {% endfor %}
                </span>
                {% endif %}
                {% if search_query %}
                <span class="ml-1 px-2 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 rounded-full text-xs font-medium">
                  "{{ search_query }}"
                </span>
                {% endif %}
              </div>
              {% endif %}
            </div>
            <div>
              <button id="batch-delete-btn" class="hidden px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 opacity-50 cursor-not-allowed items-center">
                <i class="fas fa-trash-alt mr-1"></i> Delete Selected
              </button>
            </div>
          </div>

          <div class="overflow-x-auto">
            <table id="groups-table" class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead class="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th scope="col" class="px-3 py-3 text-center">
                    <input type="checkbox" id="select-all-checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded cursor-pointer">
                  </th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    <div class="flex items-center">
                      <span>Group Name</span>
                      <i class="fas fa-sort ml-1 text-gray-400"></i>
                    </div>
                  </th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    <div class="flex items-center">
                      <span>Academic Year</span>
                      <i class="fas fa-sort ml-1 text-gray-400"></i>
                    </div>
                  </th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    <div class="flex items-center">
                      <span>Year Section</span>
                      <i class="fas fa-sort ml-1 text-gray-400"></i>
                    </div>
                  </th>
                  <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {% for group in groups %}
                  <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150">
                    <td class="px-3 py-4 whitespace-nowrap text-center">
                      <input type="checkbox" class="group-checkbox h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded cursor-pointer" data-id="{{ group.id }}">
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm font-medium text-gray-900 dark:text-white">{{ group.group_name }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span class="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                        {{ group.log_year.year_name }}
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span class="px-2 py-1 text-xs rounded-full bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                        {{ group.log_year_section.year_section_name }}
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div class="flex justify-end space-x-2">
                        <a href="{% url 'admin_section:edit_group' group.id %}"
                           class="p-1.5 bg-blue-50 text-blue-600 rounded hover:bg-blue-100 dark:bg-blue-900/30 dark:text-blue-400 dark:hover:bg-blue-900/50 transition-colors duration-200"
                           title="Edit Group">
                          <i class="fas fa-edit"></i>
                        </a>
                        <button type="button"
                                class="delete-btn p-1.5 bg-red-50 text-red-600 rounded hover:bg-red-100 dark:bg-red-900/30 dark:text-red-400 dark:hover:bg-red-900/50 transition-colors duration-200"
                                data-id="{{ group.id }}"
                                data-name="{{ group.group_name }}"
                                title="Delete Group">
                          <i class="fas fa-trash"></i>
                        </button>
                      </div>
                    </td>
                  </tr>
                {% empty %}
                  <tr>
                    <td colspan="5" class="px-6 py-8 text-center">
                      <div class="flex flex-col items-center justify-center text-gray-500 dark:text-gray-400">
                        <i class="fas fa-users text-4xl mb-3"></i>
                        <p class="text-lg font-medium">No groups found</p>
                        {% if search_query or selected_year_section %}
                          <p class="text-sm mt-1">Try clearing your search filters</p>
                          <a href="{% url 'admin_section:add_group' %}" class="mt-3 px-4 py-2 text-sm font-medium text-blue-600 bg-blue-50 rounded-lg hover:bg-blue-100 dark:bg-blue-900/20 dark:text-blue-400 dark:hover:bg-blue-900/30 transition-colors duration-200">
                            <i class="fas fa-times mr-1"></i> Clear All Filters
                          </a>
                        {% else %}
                          <p class="text-sm mt-1">Create your first group using the form</p>
                        {% endif %}
                      </div>
                    </td>
                  </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>

          <!-- Pagination -->
          {% if groups.has_other_pages %}
            <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
              <div class="flex flex-col sm:flex-row justify-between items-center">
                <div class="text-sm text-gray-700 dark:text-gray-300 mb-4 sm:mb-0">
                  Showing <span class="font-medium">{{ groups.start_index }}</span> to <span class="font-medium">{{ groups.end_index }}</span> of <span class="font-medium">{{ groups.paginator.count }}</span> groups
                </div>
                <nav class="flex justify-center">
                  <ul class="flex items-center space-x-1">
                    {% if groups.has_previous %}
                      <li>
                        <a href="?page=1{% if selected_year_section %}&year_section={{ selected_year_section }}{% endif %}{% if search_query %}&q={{ search_query }}{% endif %}" class="px-2 py-1 rounded-md bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors duration-200" title="First Page">
                          <i class="fas fa-angle-double-left"></i>
                        </a>
                      </li>
                      <li>
                        <a href="?page={{ groups.previous_page_number }}{% if selected_year_section %}&year_section={{ selected_year_section }}{% endif %}{% if search_query %}&q={{ search_query }}{% endif %}" class="px-3 py-1 rounded-md bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors duration-200">
                          <i class="fas fa-angle-left mr-1"></i> Prev
                        </a>
                      </li>
                    {% endif %}

                    {% for num in groups.paginator.page_range %}
                      {% if groups.number == num %}
                        <li>
                          <span class="px-3 py-1 rounded-md bg-blue-600 text-white font-medium">
                            {{ num }}
                          </span>
                        </li>
                      {% elif num > groups.number|add:-3 and num < groups.number|add:3 %}
                        <li>
                          <a href="?page={{ num }}{% if selected_year_section %}&year_section={{ selected_year_section }}{% endif %}{% if search_query %}&q={{ search_query }}{% endif %}" class="px-3 py-1 rounded-md bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors duration-200">
                            {{ num }}
                          </a>
                        </li>
                      {% endif %}
                    {% endfor %}

                    {% if groups.has_next %}
                      <li>
                        <a href="?page={{ groups.next_page_number }}{% if selected_year_section %}&year_section={{ selected_year_section }}{% endif %}{% if search_query %}&q={{ search_query }}{% endif %}" class="px-3 py-1 rounded-md bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors duration-200">
                          Next <i class="fas fa-angle-right ml-1"></i>
                        </a>
                      </li>
                      <li>
                        <a href="?page={{ groups.paginator.num_pages }}{% if selected_year_section %}&year_section={{ selected_year_section }}{% endif %}{% if search_query %}&q={{ search_query }}{% endif %}" class="px-2 py-1 rounded-md bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors duration-200" title="Last Page">
                          <i class="fas fa-angle-double-right"></i>
                        </a>
                      </li>
                    {% endif %}
                  </ul>
                </nav>
              </div>
            </div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>

  <!-- Delete Confirmation Modal -->
  <div id="delete-modal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 transition-opacity duration-300">
    <div class="flex items-center justify-center min-h-screen px-4">
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-md p-6 transform transition-all duration-300 scale-95 opacity-0" id="modalContent">
        <div class="flex items-center mb-4">
          <div class="flex-shrink-0 mr-3 text-red-500">
            <i class="fas fa-exclamation-triangle text-xl"></i>
          </div>
          <h3 class="text-lg font-bold text-gray-900 dark:text-white" id="modal-title">Delete Group</h3>
        </div>
        <p class="text-gray-700 dark:text-gray-300 mb-6 pl-8" id="modal-message">Are you sure you want to delete this group? This action cannot be undone.</p>
        <div class="flex justify-end space-x-3">
          <button id="cancel-delete" class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-800 rounded-lg border border-gray-300 dark:border-gray-600 hover:bg-gray-200 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors duration-200">
            <i class="fas fa-times mr-1"></i> Cancel
          </button>
          <form id="delete-form" method="post" action="">
            {% csrf_token %}
            <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors duration-200">
              <i class="fas fa-trash mr-1"></i> Delete
            </button>
          </form>
        </div>
      </div>
    </div>
  </div>

  <!-- Batch Delete Form (Hidden) -->
  <form id="batch-delete-form" method="post" action="{% url 'admin_section:add_group' %}" class="hidden">
    {% csrf_token %}
    <input type="hidden" name="delete_ids" id="delete-ids-input">
  </form>

  <!-- JavaScript for enhanced functionality -->
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Form elements
      const groupForm = document.getElementById('groupForm');
      const groupNameInput = document.querySelector('#id_group_name');
      const yearSelect = document.getElementById('id_log_year');
      const sectionSelect = document.getElementById('id_log_year_section');
      const sectionLoading = document.getElementById('section-loading');

      // Modal elements
      const deleteModal = document.getElementById('delete-modal');
      const modalContent = document.getElementById('modalContent');
      const deleteForm = document.getElementById('delete-form');
      const modalTitle = document.getElementById('modal-title');
      const modalMessage = document.getElementById('modal-message');
      const cancelDelete = document.getElementById('cancel-delete');
      const closeMessageBtns = document.querySelectorAll('.close-message');

      // Batch delete elements
      const selectAllCheckbox = document.getElementById('select-all-checkbox');
      const groupCheckboxes = document.querySelectorAll('.group-checkbox');
      const batchDeleteBtn = document.getElementById('batch-delete-btn');
      const batchDeleteForm = document.getElementById('batch-delete-form');
      const deleteIdsInput = document.getElementById('delete-ids-input');

      // Year section dynamic loading with improved UX
      if (yearSelect && sectionSelect) {
        yearSelect.addEventListener('change', function() {
          const yearId = this.value;

          // Clear current options
          sectionSelect.innerHTML = '<option value="">---------</option>';

          if (yearId) {
            // Show loading indicator
            if (sectionLoading) {
              sectionLoading.style.display = 'flex';
            }
            sectionSelect.disabled = true;

            // Fetch sections for selected year
            fetch(`/admin_section/api/group-year-sections/${yearId}/`)
              .then(response => {
                if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
                return response.json();
              })
              .then(data => {
                // Add new options
                data.forEach(section => {
                  const option = document.createElement('option');
                  option.value = section.id;
                  option.textContent = section.year_section_name;
                  sectionSelect.appendChild(option);
                });
                sectionSelect.disabled = false;
              })
              .catch(error => {
                console.error('Error fetching sections:', error);
                // Add error option
                const option = document.createElement('option');
                option.value = '';
                option.textContent = 'Error loading sections';
                sectionSelect.appendChild(option);
              })
              .finally(() => {
                // Hide loading indicator
                if (sectionLoading) {
                  sectionLoading.style.display = 'none';
                }
              });
          } else {
            sectionSelect.disabled = true;
          }
        });
      }

      // Show delete confirmation modal with animation
      document.querySelectorAll('.delete-btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
          e.preventDefault();
          const id = this.getAttribute('data-id');
          const name = this.getAttribute('data-name');

          modalTitle.textContent = 'Delete Group';
          modalMessage.textContent = `Are you sure you want to delete the group "${name}"? This action cannot be undone.`;
          deleteForm.action = `/admin_section/delete-group/${id}/`;

          // Show modal with animation
          deleteModal.classList.remove('hidden');
          setTimeout(() => {
            deleteModal.classList.add('opacity-100');
            modalContent.classList.remove('scale-95', 'opacity-0');
            modalContent.classList.add('scale-100', 'opacity-100');
          }, 10);
        });
      });

      // Hide modal with animation
      function hideModal() {
        modalContent.classList.remove('scale-100', 'opacity-100');
        modalContent.classList.add('scale-95', 'opacity-0');
        deleteModal.classList.remove('opacity-100');

        setTimeout(() => {
          deleteModal.classList.add('hidden');
        }, 300);
      }

      // Hide modal events
      if (cancelDelete) {
        cancelDelete.addEventListener('click', hideModal);
      }

      // Close modal when clicking outside
      if (deleteModal) {
        deleteModal.addEventListener('click', function(event) {
          if (event.target === deleteModal) {
            hideModal();
          }
        });
      }

      // Batch delete functionality
      // Toggle all checkboxes
      if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
          const isChecked = this.checked;
          groupCheckboxes.forEach(checkbox => {
            checkbox.checked = isChecked;
          });
          updateBatchDeleteButton();
        });
      }

      // Update batch delete button visibility and text
      function updateBatchDeleteButton() {
        const checkedBoxes = document.querySelectorAll('.group-checkbox:checked');
        const count = checkedBoxes.length;

        if (count > 0) {
          batchDeleteBtn.classList.remove('hidden');
          batchDeleteBtn.classList.remove('opacity-50', 'cursor-not-allowed');
          batchDeleteBtn.disabled = false;
          batchDeleteBtn.innerHTML = `<i class="fas fa-trash-alt mr-1"></i> Delete Selected (${count})`;
        } else {
          batchDeleteBtn.classList.add('hidden');
          batchDeleteBtn.classList.add('opacity-50', 'cursor-not-allowed');
          batchDeleteBtn.disabled = true;
          batchDeleteBtn.innerHTML = `<i class="fas fa-trash-alt mr-1"></i> Delete Selected`;
        }
      }

      // Update batch delete button when individual checkboxes change
      groupCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateBatchDeleteButton);
      });

      // Handle batch delete
      if (batchDeleteBtn) {
        batchDeleteBtn.addEventListener('click', function() {
          const checkedBoxes = document.querySelectorAll('.group-checkbox:checked');
          const ids = Array.from(checkedBoxes).map(checkbox => checkbox.getAttribute('data-id'));

          modalTitle.textContent = 'Delete Groups';
          modalMessage.textContent = `Are you sure you want to delete ${ids.length} selected groups? This action cannot be undone.`;

          deleteIdsInput.value = ids.join(',');

          // Show modal with animation
          deleteModal.classList.remove('hidden');
          setTimeout(() => {
            deleteModal.classList.add('opacity-100');
            modalContent.classList.remove('scale-95', 'opacity-0');
            modalContent.classList.add('scale-100', 'opacity-100');
          }, 10);

          // Set up the form submission
          deleteForm.action = '';
          deleteForm.addEventListener('submit', function(e) {
            e.preventDefault();
            batchDeleteForm.submit();
          });
        });
      }

      // Close message functionality
      if (closeMessageBtns.length > 0) {
        closeMessageBtns.forEach(btn => {
          btn.addEventListener('click', function() {
            const message = this.closest('.mb-3');
            if (message) {
              message.style.opacity = '0';
              setTimeout(() => {
                message.remove();
              }, 300);
            }
          });
        });

        // Auto-hide messages after 5 seconds
        setTimeout(function() {
          document.querySelectorAll('.fixed.top-4.right-4 .mb-3').forEach(function(message) {
            message.style.opacity = '0';
            setTimeout(function() {
              message.remove();
            }, 300);
          });
        }, 5000);
      }

      // Form validation
      if (groupForm && groupNameInput && yearSelect && sectionSelect) {
        groupForm.addEventListener('submit', function(e) {
          let isValid = true;

          // Validate group name
          if (!groupNameInput.value.trim()) {
            isValid = false;
            groupNameInput.classList.add('border-red-500');

            // Add error message if it doesn't exist
            let errorElement = groupNameInput.parentNode.querySelector('.error-message');
            if (!errorElement) {
              errorElement = document.createElement('p');
              errorElement.className = 'mt-1 text-sm text-red-600 dark:text-red-400 error-message';
              groupNameInput.parentNode.appendChild(errorElement);
            }
            errorElement.textContent = 'Please enter a group name';
          }

          // Validate academic year
          if (!yearSelect.value) {
            isValid = false;
            yearSelect.classList.add('border-red-500');

            // Add error message if it doesn't exist
            let errorElement = yearSelect.parentNode.querySelector('.error-message');
            if (!errorElement) {
              errorElement = document.createElement('p');
              errorElement.className = 'mt-1 text-sm text-red-600 dark:text-red-400 error-message';
              yearSelect.parentNode.appendChild(errorElement);
            }
            errorElement.textContent = 'Please select an academic year';
          }

          // Validate year section
          if (!sectionSelect.value) {
            isValid = false;
            sectionSelect.classList.add('border-red-500');

            // Add error message if it doesn't exist
            let errorElement = sectionSelect.parentNode.querySelector('.error-message');
            if (!errorElement) {
              errorElement = document.createElement('p');
              errorElement.className = 'mt-1 text-sm text-red-600 dark:text-red-400 error-message';
              sectionSelect.parentNode.appendChild(errorElement);
            }
            errorElement.textContent = 'Please select a year section';
          }

          if (!isValid) {
            e.preventDefault();
          }
        });

        // Remove error styling on input
        groupNameInput.addEventListener('input', function() {
          if (this.value.trim()) {
            this.classList.remove('border-red-500');
            const errorElement = this.parentNode.querySelector('.error-message');
            if (errorElement) {
              errorElement.remove();
            }
          }
        });

        yearSelect.addEventListener('change', function() {
          if (this.value) {
            this.classList.remove('border-red-500');
            const errorElement = this.parentNode.querySelector('.error-message');
            if (errorElement) {
              errorElement.remove();
            }
          }
        });

        sectionSelect.addEventListener('change', function() {
          if (this.value) {
            this.classList.remove('border-red-500');
            const errorElement = this.parentNode.querySelector('.error-message');
            if (errorElement) {
              errorElement.remove();
            }
          }
        });
      }
    });
  </script>
{% endblock %}
