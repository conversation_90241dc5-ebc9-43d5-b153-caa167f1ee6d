{% extends 'base.html' %}

{% block title %}
  Medical Resources - MedLogBook
{% endblock %}

{% block extra_head %}
  <!-- Enhanced styles for resources page -->
  <style>
    @keyframes float {
      0% { transform: translateY(0px); }
      50% { transform: translateY(-10px); }
      100% { transform: translateY(0px); }
    }

    @keyframes shimmer {
      0% { background-position: -1000px 0; }
      100% { background-position: 1000px 0; }
    }

    .animate-float {
      animation: float 6s ease-in-out infinite;
    }

    .resource-card {
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .resource-card:hover {
      transform: translateY(-8px);
      box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    .resource-card:hover .resource-icon {
      transform: scale(1.15) rotate(5deg);
    }

    .resource-icon {
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .category-card {
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .category-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    .category-card:hover .category-icon {
      transform: scale(1.15) rotate(10deg);
    }

    .category-icon {
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .search-input {
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .search-input:focus {
      transform: translateY(-2px);
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
    }

    .department-tab {
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .department-tab.active {
      border-color: #3b82f6;
      color: #3b82f6;
      background: linear-gradient(to right, rgba(59, 130, 246, 0.1), transparent);
    }

    .department-tab:not(.active):hover {
      background: linear-gradient(to right, rgba(59, 130, 246, 0.05), transparent);
      transform: translateY(-1px);
    }

    .loading-shimmer {
      background: linear-gradient(to right, #f6f7f8 8%, #edeef1 18%, #f6f7f8 33%);
      background-size: 2000px 100%;
      animation: shimmer 2s linear infinite;
    }
  </style>

  <!-- Preload critical images -->
  <link rel="preload" as="image" href="https://images.unsplash.com/photo-1585435557343-3b092031a831?q=80&w=2070" fetchpriority="high">
{% endblock %}

{% block navbar %}
  {% include './components/public_navbar.html' %}
{% endblock %}

{% block content_container %}
  <!-- Enhanced Hero Section with Visual Elements -->
  <section class="relative bg-gradient-to-r from-green-600 to-blue-600 dark:from-gray-800 dark:to-gray-900 py-24 md:py-32 w-full overflow-hidden">
    <!-- Decorative Elements -->
    <div class="absolute inset-0 overflow-hidden z-0">
      <div class="absolute top-0 right-0 -mt-16 -mr-16 w-64 h-64 bg-purple-400 bg-opacity-10 rounded-full"></div>
      <div class="absolute bottom-0 left-0 -mb-16 -ml-16 w-80 h-80 bg-yellow-200 bg-opacity-10 rounded-full"></div>
      <div class="absolute top-1/2 left-1/4 transform -translate-y-1/2 w-40 h-40 bg-blue-200 bg-opacity-5 rounded-full"></div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-20">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
        <div class="text-center md:text-left relative z-30">
          <span class="inline-block px-4 py-1 bg-white bg-opacity-20 rounded-full text-white text-sm font-medium mb-6 animate-fade-in">AGU Medical Library</span>
          <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold text-white leading-tight animate-fade-in">
            Medical <span class="text-yellow-300">Resources</span>
          </h1>
          <p class="mt-6 text-xl text-white opacity-90 animate-fade-in delay-100 leading-relaxed relative z-30">
            Access cutting-edge medical knowledge, research materials, and educational content to enhance your medical education and practice.
          </p>

          <!-- Search Bar -->
          <div class="mt-8 relative max-w-md mx-auto md:mx-0 animate-fade-in delay-200">
            <div class="relative">
              <input type="text" placeholder="Search for resources..." class="search-input w-full px-5 py-4 pr-12 rounded-lg border-2 border-white border-opacity-20 bg-white bg-opacity-10 text-white placeholder-white placeholder-opacity-75 focus:outline-none focus:border-white transition duration-200">
              <button class="absolute right-3 top-1/2 transform -translate-y-1/2 text-white hover:text-yellow-300 transition duration-200">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
              </button>
            </div>
            <div class="mt-3 flex flex-wrap gap-2 justify-center md:justify-start">
              <span class="text-sm text-white opacity-80">Popular:</span>
              <a href="#" class="text-sm text-white hover:text-yellow-300 transition duration-200">Cardiology</a>
              <a href="#" class="text-sm text-white hover:text-yellow-300 transition duration-200">Pediatrics</a>
              <a href="#" class="text-sm text-white hover:text-yellow-300 transition duration-200">Neurology</a>
            </div>
          </div>
        </div>

        <!-- Hero Image -->
        <div class="hidden md:block animate-float">
          <div class="relative">
            <img src="https://images.unsplash.com/photo-1585435557343-3b092031a831?q=80&w=2070" alt="Medical Resources" class="rounded-lg shadow-2xl w-full object-cover h-auto" loading="eager" />
            <div class="absolute -bottom-4 -right-4 bg-white dark:bg-gray-800 p-4 rounded-lg shadow-lg">
              <div class="flex items-center">
                <div class="bg-blue-500 p-2 rounded-full">
                  <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 4.804A7.968 7.968 0 005.5 4c-1.255 0-2.443.29-3.5.804v10A7.969 7.969 0 015.5 14c1.669 0 3.218.51 4.5 1.385A7.962 7.962 0 0114.5 14c1.255 0 2.443.29 3.5.804v-10A7.968 7.968 0 0014.5 4c-1.255 0-2.443.29-3.5.804V12a1 1 0 11-2 0V4.804z"></path>
                  </svg>
                </div>
                <div class="ml-3">
                  <p class="text-sm font-medium text-gray-900 dark:text-white">1000+ Resources</p>
                  <p class="text-xs text-gray-500 dark:text-gray-400">Updated Weekly</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  {% block content %}

  <!-- Enhanced Resource Categories Section -->
  <section class="py-20 w-full bg-white dark:bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-16">
        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white relative inline-block">
          Resource Categories
          <span class="absolute -bottom-2 left-0 w-full h-1 bg-blue-500 rounded-full"></span>
        </h2>
        <p class="mt-6 text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
          Explore our comprehensive collection of medical resources organized by category to enhance your learning experience.
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        <!-- Category Card 1 -->
        <a href="{% url 'ebookjournals_page' %}" class="category-card group bg-white dark:bg-gray-800 p-8 rounded-xl shadow-lg hover:shadow-xl border border-gray-100 dark:border-gray-700 transition-all duration-300 transform hover:-translate-y-2 text-center">
          <div class="category-icon w-20 h-20 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mb-6 mx-auto text-blue-600 dark:text-blue-400">
            <svg class="w-10 h-10" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9 4.804A7.968 7.968 0 005.5 4c-1.255 0-2.443.29-3.5.804v10A7.969 7.969 0 015.5 14c1.669 0 3.218.51 4.5 1.385A7.962 7.962 0 0114.5 14c1.255 0 2.443.29 3.5.804v-10A7.968 7.968 0 0014.5 4c-1.255 0-2.443.29-3.5.804V12a1 1 0 11-2 0V4.804z"></path>
            </svg>
          </div>
          <h3 class="text-xl font-bold mb-3 text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-200">eBooks & Journals</h3>
          <p class="text-gray-600 dark:text-gray-300 mb-4">Access our curated collection of medical literature and publications</p>
          <span class="inline-flex items-center text-blue-600 dark:text-blue-400 font-medium">
            Browse Collection
            <svg class="w-4 h-4 ml-1 transform group-hover:translate-x-1 transition-transform duration-200" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
            </svg>
          </span>
        </a>

        <!-- Category Card 2 -->
        <a href="#" class="category-card group bg-white dark:bg-gray-800 p-8 rounded-xl shadow-lg hover:shadow-xl border border-gray-100 dark:border-gray-700 transition-all duration-300 transform hover:-translate-y-2 text-center">
          <div class="category-icon w-20 h-20 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mb-6 mx-auto text-green-600 dark:text-green-400">
            <svg class="w-10 h-10" fill="currentColor" viewBox="0 0 20 20">
              <path d="M2 6a2 2 0 012-2h6a2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V6zM14.553 7.106A1 1 0 0014 8v4a1 1 0 00.553.894l2 1A1 1 0 0018 13V7a1 1 0 00-1.447-.894l-2 1z"></path>
            </svg>
          </div>
          <h3 class="text-xl font-bold mb-3 text-gray-900 dark:text-white group-hover:text-green-600 dark:group-hover:text-green-400 transition-colors duration-200">Video Lectures</h3>
          <p class="text-gray-600 dark:text-gray-300 mb-4">Learn from leading medical professionals through comprehensive video content</p>
          <span class="inline-flex items-center text-green-600 dark:text-green-400 font-medium">
            Watch Videos
            <svg class="w-4 h-4 ml-1 transform group-hover:translate-x-1 transition-transform duration-200" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
            </svg>
          </span>
        </a>

        <!-- Category Card 3 -->
        <a href="#" class="category-card group bg-white dark:bg-gray-800 p-8 rounded-xl shadow-lg hover:shadow-xl border border-gray-100 dark:border-gray-700 transition-all duration-300 transform hover:-translate-y-2 text-center">
          <div class="category-icon w-20 h-20 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center mb-6 mx-auto text-purple-600 dark:text-purple-400">
            <svg class="w-10 h-10" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 0l-2 2a1 1 0 101.414 1.414L8 10.414l1.293 1.293a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
            </svg>
          </div>
          <h3 class="text-xl font-bold mb-3 text-gray-900 dark:text-white group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors duration-200">Research Tools</h3>
          <p class="text-gray-600 dark:text-gray-300 mb-4">Advanced analytics and data visualization tools for medical research</p>
          <span class="inline-flex items-center text-purple-600 dark:text-purple-400 font-medium">
            Explore Tools
            <svg class="w-4 h-4 ml-1 transform group-hover:translate-x-1 transition-transform duration-200" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
            </svg>
          </span>
        </a>

        <!-- Category Card 4 -->
        <a href="#" class="category-card group bg-white dark:bg-gray-800 p-8 rounded-xl shadow-lg hover:shadow-xl border border-gray-100 dark:border-gray-700 transition-all duration-300 transform hover:-translate-y-2 text-center">
          <div class="category-icon w-20 h-20 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mb-6 mx-auto text-red-600 dark:text-red-400">
            <svg class="w-10 h-10" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7 2a1 1 0 00-.707 1.707L7 4.414v3.758a1 1 0 01-.293.707l-4 4C.817 14.769 2.156 18 4.828 18h10.343c2.673 0 4.012-3.231 2.122-5.121l-4-4A1 1 0 0113 8.172V4.414l.707-.707A1 1 0 0013 2H7zm2 6.172V4h2v4.172a3 3 0 00.879 2.12l1.027 1.028a4 4 0 00-2.171.102l-.47.156a4 4 0 01-2.53 0l-.563-.187a1.993 1.993 0 00-.114-.035l1.063-1.063A3 3 0 009 8.172z" clip-rule="evenodd"></path>
            </svg>
          </div>
          <h3 class="text-xl font-bold mb-3 text-gray-900 dark:text-white group-hover:text-red-600 dark:group-hover:text-red-400 transition-colors duration-200">Clinical Cases</h3>
          <p class="text-gray-600 dark:text-gray-300 mb-4">Study real-world clinical cases with detailed analysis and outcomes</p>
          <span class="inline-flex items-center text-red-600 dark:text-red-400 font-medium">
            View Cases
            <svg class="w-4 h-4 ml-1 transform group-hover:translate-x-1 transition-transform duration-200" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
            </svg>
          </span>
        </a>
      </div>

      <!-- Additional Categories Row -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
        <!-- Mini Category Card 1 -->
        <a href="#" class="group flex items-center p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors duration-200 border border-gray-100 dark:border-gray-700">
          <div class="flex-shrink-0 w-12 h-12 bg-yellow-100 dark:bg-yellow-900/30 rounded-full flex items-center justify-center text-yellow-600 dark:text-yellow-400 mr-4">
            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
              <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z"></path>
            </svg>
          </div>
          <div class="flex-grow">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-200">Educational Materials</h3>
            <p class="text-sm text-gray-600 dark:text-gray-300">Lecture notes, slides, and study guides</p>
          </div>
          <svg class="w-5 h-5 text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-400 transform group-hover:translate-x-1 transition-all duration-200" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
          </svg>
        </a>

        <!-- Mini Category Card 2 -->
        <a href="#" class="group flex items-center p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors duration-200 border border-gray-100 dark:border-gray-700">
          <div class="flex-shrink-0 w-12 h-12 bg-indigo-100 dark:bg-indigo-900/30 rounded-full flex items-center justify-center text-indigo-600 dark:text-indigo-400 mr-4">
            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path>
            </svg>
          </div>
          <div class="flex-grow">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-200">Practice Exams</h3>
            <p class="text-sm text-gray-600 dark:text-gray-300">Test your knowledge with sample questions</p>
          </div>
          <svg class="w-5 h-5 text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-400 transform group-hover:translate-x-1 transition-all duration-200" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
          </svg>
        </a>

        <!-- Mini Category Card 3 -->
        <a href="#" class="group flex items-center p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors duration-200 border border-gray-100 dark:border-gray-700">
          <div class="flex-shrink-0 w-12 h-12 bg-teal-100 dark:bg-teal-900/30 rounded-full flex items-center justify-center text-teal-600 dark:text-teal-400 mr-4">
            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"></path>
            </svg>
          </div>
          <div class="flex-grow">
            <h3 class="text-lg font-medium text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-200">Discussion Forums</h3>
            <p class="text-sm text-gray-600 dark:text-gray-300">Engage with peers and experts</p>
          </div>
          <svg class="w-5 h-5 text-gray-400 group-hover:text-blue-600 dark:group-hover:text-blue-400 transform group-hover:translate-x-1 transition-all duration-200" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
          </svg>
        </a>
      </div>
    </div>
  </section>

  <!-- Enhanced Featured Resources Section with Tabs -->
  <section class="py-20 bg-gray-50 dark:bg-gray-800 w-full">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-12">
        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white relative inline-block">
          Featured Resources
          <span class="absolute -bottom-2 left-0 w-full h-1 bg-blue-500 rounded-full"></span>
        </h2>
        <p class="mt-6 text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
          Discover our most popular and recently updated medical resources curated by our expert team.
        </p>
      </div>

      <!-- Department Tabs -->
      <div class="flex flex-wrap justify-center mb-10 border-b border-gray-200 dark:border-gray-700">
        <button class="department-tab active px-5 py-3 font-medium text-sm border-b-2 border-transparent focus:outline-none">
          All Departments
        </button>
        <button class="department-tab px-5 py-3 font-medium text-sm border-b-2 border-transparent focus:outline-none">
          Cardiology
        </button>
        <button class="department-tab px-5 py-3 font-medium text-sm border-b-2 border-transparent focus:outline-none">
          Pediatrics
        </button>
        <button class="department-tab px-5 py-3 font-medium text-sm border-b-2 border-transparent focus:outline-none">
          Neurology
        </button>
        <button class="department-tab px-5 py-3 font-medium text-sm border-b-2 border-transparent focus:outline-none">
          Surgery
        </button>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <!-- Resource Card 1 -->
        <div class="resource-card bg-white dark:bg-gray-700 rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 hover:-translate-y-2 group">
          <div class="relative">
            <img src="https://images.unsplash.com/photo-1578496479939-722d9dd1cc5b?q=80&w=2070" alt="Advanced ECG Analysis" class="w-full h-52 object-cover transition-transform duration-500 group-hover:scale-105" loading="lazy" />
            <div class="absolute top-4 left-4 bg-blue-600 text-white text-xs font-bold px-3 py-1 rounded-full">
              POPULAR
            </div>
            <div class="absolute top-4 right-4 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 text-xs font-medium px-3 py-1 rounded-full flex items-center">
              <svg class="w-3 h-3 mr-1 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
              </svg>
              4.9 (128)
            </div>
          </div>
          <div class="p-6">
            <div class="flex items-center mb-3">
              <span class="text-blue-600 dark:text-blue-400 text-sm font-medium bg-blue-100 dark:bg-blue-900/30 px-2.5 py-0.5 rounded-full">Cardiology</span>
              <span class="ml-2 text-xs text-gray-500 dark:text-gray-400">Updated 2 weeks ago</span>
            </div>
            <h3 class="text-xl font-bold mb-2 text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-200">Advanced ECG Analysis</h3>
            <p class="text-gray-600 dark:text-gray-300 mb-4">Comprehensive guide to electrocardiogram interpretation with case studies and practice examples.</p>
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="Author" class="w-8 h-8 rounded-full mr-2" />
                <span class="text-sm text-gray-600 dark:text-gray-300">Dr. Michael Chen</span>
              </div>
              <button class="flex items-center text-white bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded-lg transition-colors duration-200">
                <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
                Download
              </button>
            </div>
          </div>
        </div>

        <!-- Resource Card 2 -->
        <div class="resource-card bg-white dark:bg-gray-700 rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 hover:-translate-y-2 group">
          <div class="relative">
            <img src="https://images.unsplash.com/photo-1581595220892-b0739db3ba8c?q=80&w=2073" alt="Pediatric Assessment Guide" class="w-full h-52 object-cover transition-transform duration-500 group-hover:scale-105" loading="lazy" />
            <div class="absolute top-4 left-4 bg-green-600 text-white text-xs font-bold px-3 py-1 rounded-full">
              NEW
            </div>
            <div class="absolute top-4 right-4 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 text-xs font-medium px-3 py-1 rounded-full flex items-center">
              <svg class="w-3 h-3 mr-1 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
              </svg>
              4.7 (93)
            </div>
          </div>
          <div class="p-6">
            <div class="flex items-center mb-3">
              <span class="text-green-600 dark:text-green-400 text-sm font-medium bg-green-100 dark:bg-green-900/30 px-2.5 py-0.5 rounded-full">Pediatrics</span>
              <span class="ml-2 text-xs text-gray-500 dark:text-gray-400">Updated 3 days ago</span>
            </div>
            <h3 class="text-xl font-bold mb-2 text-gray-900 dark:text-white group-hover:text-green-600 dark:group-hover:text-green-400 transition-colors duration-200">Pediatric Assessment Guide</h3>
            <p class="text-gray-600 dark:text-gray-300 mb-4">Complete guide to pediatric physical examination and developmental assessment techniques.</p>
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="Author" class="w-8 h-8 rounded-full mr-2" />
                <span class="text-sm text-gray-600 dark:text-gray-300">Dr. Sarah Johnson</span>
              </div>
              <button class="flex items-center text-white bg-green-600 hover:bg-green-700 px-4 py-2 rounded-lg transition-colors duration-200">
                <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
                Download
              </button>
            </div>
          </div>
        </div>

        <!-- Resource Card 3 -->
        <div class="resource-card bg-white dark:bg-gray-700 rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 hover:-translate-y-2 group">
          <div class="relative">
            <img src="https://images.unsplash.com/photo-1559757175-7cb057fba93c?q=80&w=2073" alt="Neurological Disorders Handbook" class="w-full h-52 object-cover transition-transform duration-500 group-hover:scale-105" loading="lazy" />
            <div class="absolute top-4 right-4 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 text-xs font-medium px-3 py-1 rounded-full flex items-center">
              <svg class="w-3 h-3 mr-1 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
              </svg>
              4.8 (156)
            </div>
          </div>
          <div class="p-6">
            <div class="flex items-center mb-3">
              <span class="text-purple-600 dark:text-purple-400 text-sm font-medium bg-purple-100 dark:bg-purple-900/30 px-2.5 py-0.5 rounded-full">Neurology</span>
              <span class="ml-2 text-xs text-gray-500 dark:text-gray-400">Updated 1 month ago</span>
            </div>
            <h3 class="text-xl font-bold mb-2 text-gray-900 dark:text-white group-hover:text-purple-600 dark:group-hover:text-purple-400 transition-colors duration-200">Neurological Disorders Handbook</h3>
            <p class="text-gray-600 dark:text-gray-300 mb-4">Essential reference guide covering diagnosis and treatment of common neurological conditions.</p>
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <img src="https://randomuser.me/api/portraits/men/67.jpg" alt="Author" class="w-8 h-8 rounded-full mr-2" />
                <span class="text-sm text-gray-600 dark:text-gray-300">Dr. Ahmed Al-Farsi</span>
              </div>
              <button class="flex items-center text-white bg-purple-600 hover:bg-purple-700 px-4 py-2 rounded-lg transition-colors duration-200">
                <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
                Download
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- View More Button -->
      <div class="text-center mt-12">
        <a href="#" class="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg shadow-lg hover:bg-blue-700 transition duration-300 transform hover:scale-105">
          <i class="fas fa-book-medical mr-2"></i> View All Resources
        </a>
      </div>
    </div>
  </section>

  <!-- Resources by Department Section -->
  <section class="py-20 w-full bg-white dark:bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="text-center mb-12">
        <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white relative inline-block">
          Resources by Department
          <span class="absolute -bottom-2 left-0 w-full h-1 bg-blue-500 rounded-full"></span>
        </h2>
        <p class="mt-6 text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
          Browse our extensive collection of resources organized by medical specialty.
        </p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <!-- Department Card 1 -->
        <a href="#" class="group relative overflow-hidden rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
          <div class="absolute inset-0 bg-gradient-to-br from-blue-600 to-blue-800 opacity-90 group-hover:opacity-95 transition-opacity duration-300"></div>
          <div class="relative p-8 flex flex-col h-full min-h-[250px] justify-between z-10">
            <div>
              <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clip-rule="evenodd"></path>
                </svg>
              </div>
              <h3 class="text-2xl font-bold text-white mb-2">Cardiology</h3>
              <p class="text-white text-opacity-90 mb-6">Resources for cardiovascular diseases, diagnostics, and treatments.</p>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-white font-medium">42 Resources</span>
              <span class="text-white group-hover:translate-x-2 transition-transform duration-300 flex items-center">
                Explore
                <svg class="w-4 h-4 ml-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
              </span>
            </div>
          </div>
        </a>

        <!-- Department Card 2 -->
        <a href="#" class="group relative overflow-hidden rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
          <div class="absolute inset-0 bg-gradient-to-br from-green-600 to-green-800 opacity-90 group-hover:opacity-95 transition-opacity duration-300"></div>
          <div class="relative p-8 flex flex-col h-full min-h-[250px] justify-between z-10">
            <div>
              <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M11 3a1 1 0 10-2 0v1a1 1 0 102 0V3zM15.657 5.757a1 1 0 00-1.414-1.414l-.707.707a1 1 0 001.414 1.414l.707-.707zM18 10a1 1 0 01-1 1h-1a1 1 0 110-2h1a1 1 0 011 1zM5.05 6.464A1 1 0 106.464 5.05l-.707-.707a1 1 0 00-1.414 1.414l.707.707zM5 10a1 1 0 01-1 1H3a1 1 0 110-2h1a1 1 0 011 1zM8 16v-1h4v1a2 2 0 11-4 0zM12 14c.015-.34.208-.646.477-.859a4 4 0 10-4.954 0c.27.213.462.519.476.859h4.002z"></path>
                </svg>
              </div>
              <h3 class="text-2xl font-bold text-white mb-2">Pediatrics</h3>
              <p class="text-white text-opacity-90 mb-6">Resources for child health, development, and pediatric conditions.</p>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-white font-medium">38 Resources</span>
              <span class="text-white group-hover:translate-x-2 transition-transform duration-300 flex items-center">
                Explore
                <svg class="w-4 h-4 ml-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
              </span>
            </div>
          </div>
        </a>

        <!-- Department Card 3 -->
        <a href="#" class="group relative overflow-hidden rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
          <div class="absolute inset-0 bg-gradient-to-br from-purple-600 to-purple-800 opacity-90 group-hover:opacity-95 transition-opacity duration-300"></div>
          <div class="relative p-8 flex flex-col h-full min-h-[250px] justify-between z-10">
            <div>
              <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M3 5a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2h-2.22l.123.489.804.804A1 1 0 0113 18H7a1 1 0 01-.707-1.707l.804-.804L7.22 15H5a2 2 0 01-2-2V5zm5.771 7H5V5h10v7H8.771z" clip-rule="evenodd"></path>
                </svg>
              </div>
              <h3 class="text-2xl font-bold text-white mb-2">Neurology</h3>
              <p class="text-white text-opacity-90 mb-6">Resources for neurological disorders, brain function, and treatments.</p>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-white font-medium">35 Resources</span>
              <span class="text-white group-hover:translate-x-2 transition-transform duration-300 flex items-center">
                Explore
                <svg class="w-4 h-4 ml-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
              </span>
            </div>
          </div>
        </a>

        <!-- Department Card 4 -->
        <a href="#" class="group relative overflow-hidden rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
          <div class="absolute inset-0 bg-gradient-to-br from-red-600 to-red-800 opacity-90 group-hover:opacity-95 transition-opacity duration-300"></div>
          <div class="relative p-8 flex flex-col h-full min-h-[250px] justify-between z-10">
            <div>
              <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M7 2a1 1 0 00-.707 1.707L7 4.414v3.758a1 1 0 01-.293.707l-4 4C.817 14.769 2.156 18 4.828 18h10.343c2.673 0 4.012-3.231 2.122-5.121l-4-4A1 1 0 0113 8.172V4.414l.707-.707A1 1 0 0013 2H7zm2 6.172V4h2v4.172a3 3 0 00.879 2.12l1.027 1.028a4 4 0 00-2.171.102l-.47.156a4 4 0 01-2.53 0l-.563-.187a1.993 1.993 0 00-.114-.035l1.063-1.063A3 3 0 009 8.172z" clip-rule="evenodd"></path>
                </svg>
              </div>
              <h3 class="text-2xl font-bold text-white mb-2">Surgery</h3>
              <p class="text-white text-opacity-90 mb-6">Resources for surgical procedures, techniques, and post-operative care.</p>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-white font-medium">47 Resources</span>
              <span class="text-white group-hover:translate-x-2 transition-transform duration-300 flex items-center">
                Explore
                <svg class="w-4 h-4 ml-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
              </span>
            </div>
          </div>
        </a>

        <!-- Department Card 5 -->
        <a href="#" class="group relative overflow-hidden rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
          <div class="absolute inset-0 bg-gradient-to-br from-yellow-500 to-yellow-700 opacity-90 group-hover:opacity-95 transition-opacity duration-300"></div>
          <div class="relative p-8 flex flex-col h-full min-h-[250px] justify-between z-10">
            <div>
              <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clip-rule="evenodd"></path>
                </svg>
              </div>
              <h3 class="text-2xl font-bold text-white mb-2">Dermatology</h3>
              <p class="text-white text-opacity-90 mb-6">Resources for skin conditions, treatments, and diagnostic techniques.</p>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-white font-medium">29 Resources</span>
              <span class="text-white group-hover:translate-x-2 transition-transform duration-300 flex items-center">
                Explore
                <svg class="w-4 h-4 ml-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
              </span>
            </div>
          </div>
        </a>

        <!-- Department Card 6 -->
        <a href="#" class="group relative overflow-hidden rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
          <div class="absolute inset-0 bg-gradient-to-br from-indigo-600 to-indigo-800 opacity-90 group-hover:opacity-95 transition-opacity duration-300"></div>
          <div class="relative p-8 flex flex-col h-full min-h-[250px] justify-between z-10">
            <div>
              <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z"></path>
                </svg>
              </div>
              <h3 class="text-2xl font-bold text-white mb-2">Psychiatry</h3>
              <p class="text-white text-opacity-90 mb-6">Resources for mental health, psychological disorders, and therapies.</p>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-white font-medium">33 Resources</span>
              <span class="text-white group-hover:translate-x-2 transition-transform duration-300 flex items-center">
                Explore
                <svg class="w-4 h-4 ml-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
              </span>
            </div>
          </div>
        </a>
      </div>

      <!-- View All Departments Button -->
      <div class="text-center mt-12">
        <a href="#" class="inline-flex items-center px-6 py-3 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white font-medium rounded-lg shadow-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition duration-300 transform hover:scale-105">
          <i class="fas fa-th-large mr-2"></i> View All Departments
        </a>
      </div>
    </div>
  </section>

  <!-- Call-to-Action Section -->
  <section class="py-20 w-full relative overflow-hidden">
    <div class="absolute inset-0 bg-gradient-to-r from-blue-600 to-green-600 dark:from-blue-900 dark:to-green-900"></div>

    <!-- Decorative Elements -->
    <div class="absolute top-0 left-0 w-full h-full overflow-hidden">
      <svg class="absolute top-0 left-0 transform translate-y-[-30%] opacity-10" width="800" height="800" viewBox="0 0 800 800" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="400" cy="400" r="400" fill="white"/>
      </svg>
      <svg class="absolute bottom-0 right-0 transform translate-x-[30%] translate-y-[30%] opacity-10" width="800" height="800" viewBox="0 0 800 800" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="400" cy="400" r="400" fill="white"/>
      </svg>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 relative z-10">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
        <div class="text-center md:text-left">
          <h2 class="text-3xl md:text-4xl font-bold text-white mb-6 animate-fade-in">Can't Find What You're Looking For?</h2>
          <p class="text-white text-opacity-90 mb-8 text-lg max-w-xl animate-fade-in delay-100">
            Our team is constantly updating our resource library. If you need specific materials or have suggestions, please let us know.
          </p>
          <div class="flex flex-col sm:flex-row md:justify-start justify-center gap-4 animate-fade-in delay-200">
            <a href="#" class="px-8 py-4 bg-white text-blue-600 font-medium rounded-lg shadow-lg hover:shadow-xl transition duration-300 transform hover:scale-105">
              <i class="fas fa-paper-plane mr-2"></i> Request Resources
            </a>
            <a href="#" class="px-8 py-4 bg-transparent text-white border-2 border-white font-medium rounded-lg hover:bg-white hover:bg-opacity-10 transition duration-300">
              <i class="fas fa-headset mr-2"></i> Contact Support
            </a>
          </div>
        </div>

        <!-- CTA Image -->
        <div class="hidden md:block animate-float">
          <div class="relative">
            <img src="https://images.unsplash.com/photo-1576091160550-2173dba999ef?q=80&w=2070" alt="Medical Education" class="rounded-lg shadow-2xl w-full object-cover h-auto transform -rotate-2" loading="lazy" />
            <div class="absolute -bottom-4 -left-4 bg-white dark:bg-gray-800 p-4 rounded-lg shadow-lg">
              <div class="flex items-center">
                <div class="bg-green-500 p-2 rounded-full">
                  <i class="fas fa-book-medical text-white"></i>
                </div>
                <div class="ml-3">
                  <p class="text-sm font-medium text-gray-900 dark:text-white">Weekly Updates</p>
                  <p class="text-xs text-gray-500 dark:text-gray-400">New Resources Every Week</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
  {% endblock %}
{% endblock %}
