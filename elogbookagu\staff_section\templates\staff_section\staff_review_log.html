{% extends 'base.html' %}

{% block title %}
  Review Student Log
{% endblock %}

{% block navbar %}
  {% include 'components/staff_auth_navbar.html' %}
{% endblock %}

{% block content %}
  <div class="container mx-auto px-4 py-8">
    <div class="mb-6">
      <a href="{% url 'staff_section:staff_reviews' %}" class="inline-flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
        </svg>
        Back to Reviews
      </a>
    </div>

    <div class="bg-white dark:bg-gray-800 shadow overflow-hidden sm:rounded-lg">
      <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">
          Student Log Details
        </h3>
        <p class="mt-1 max-w-2xl text-sm text-gray-500 dark:text-gray-400">
          Review and provide feedback on this log entry.
        </p>
      </div>

      <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:p-6">
        <dl class="grid grid-cols-1 gap-x-4 gap-y-6 sm:grid-cols-2">
          <div class="sm:col-span-1">
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Student</dt>
            <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ log.student.user.get_full_name }}</dd>
          </div>
          <div class="sm:col-span-1">
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Student ID</dt>
            <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ log.student.student_id }}</dd>
          </div>
          <div class="sm:col-span-1">
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Date</dt>
            <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ log.date }}</dd>
          </div>
          <div class="sm:col-span-1">
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Department</dt>
            <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ log.department.name }}</dd>
          </div>
          <div class="sm:col-span-1">
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Activity Type</dt>
            <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ log.activity_type.name }}</dd>
          </div>
          <div class="sm:col-span-1">
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Participation</dt>
            <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ log.participation_type }}</dd>
          </div>
          <div class="sm:col-span-2">
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Description</dt>
            <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ log.description }}</dd>
          </div>
          {% if log.patient_id %}
          <div class="sm:col-span-1">
            <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Patient ID</dt>
            <dd class="mt-1 text-sm text-gray-900 dark:text-white">{{ log.patient_id }}</dd>
          </div>
          {% endif %}
        </dl>
      </div>

      <!-- Review Form -->
      <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:p-6">
        <h4 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Review Decision</h4>
        
        <form method="post" action="{% url 'staff_section:review_log' log.id %}">
          {% csrf_token %}
          
          <div class="mb-4">
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Decision</label>
            <div class="space-y-2">
              {% for value, text in form.is_approved.field.choices %}
              <div class="flex items-center">
                <input type="radio" name="{{ form.is_approved.name }}" value="{{ value }}" id="id_is_approved_{{ forloop.counter0 }}" 
                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                       {% if form.is_approved.value == value|stringformat:"s" %}checked{% endif %}>
                <label for="id_is_approved_{{ forloop.counter0 }}" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                  {{ text }}
                </label>
              </div>
              {% endfor %}
            </div>
            {% if form.is_approved.errors %}
            <p class="mt-2 text-sm text-red-600">{{ form.is_approved.errors.0 }}</p>
            {% endif %}
          </div>
          
          <div class="mb-4">
            <label for="{{ form.reviewer_comments.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Comments
            </label>
            {{ form.reviewer_comments }}
            {% if form.reviewer_comments.errors %}
            <p class="mt-2 text-sm text-red-600">{{ form.reviewer_comments.errors.0 }}</p>
            {% endif %}
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
              Add any comments or feedback for the student. This will be visible to them.
            </p>
          </div>
          
          <div class="flex justify-end">
            <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
              Submit Review
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
{% endblock %}
