{% extends 'components/auth_navbar_base.html' %}
{% load static %}

{% block home_url %}{% url 'admin_section:admin_dash' %}{% endblock %}
{% block header_icon %}fas fa-user-shield{% endblock %}
{% block portal_name %}Admin Portal{% endblock %}
{% block user_role %}Administrator{% endblock %}
{% block user_role_header %}Administrator{% endblock %}
{% block user_title %}Admin{% endblock %}
{% block logout_url %}{% url 'admin_section:logout' %}{% endblock %}
{% block logout_url_bottom %}{% url 'admin_section:logout' %}{% endblock %}
{% block profile_url %}{% url 'admin_section:admin_profile' %}{% endblock %}
{% block notifications_url %}{% url 'admin_section:notifications' %}{% endblock %}
{% block sidebar_menu %}
  <!-- Dashboard -->
  <a href="{% url 'admin_section:admin_dash' %}" class="nav-item flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 group transition-colors duration-200">
    <i class="fas fa-chart-line w-5 h-5 text-blue-600 dark:text-blue-400"></i>
    <span class="ml-3 font-medium">Dashboard</span>
  </a>

  <!-- Profile -->
  <a href="{% url 'admin_section:admin_profile' %}" class="nav-item flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 group transition-colors duration-200">
    <i class="fas fa-user-md w-5 h-5 text-blue-600 dark:text-blue-400"></i>
    <span class="ml-3 font-medium">Profile</span>
  </a>

  <!-- Posts -->
  <a href="{% url 'admin_section:admin_blogs' %}" class="nav-item flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 group transition-colors duration-200">
    <i class="far fa-edit w-5 h-5 text-blue-600 dark:text-blue-400"></i>
    <span class="ml-3 font-medium">Posts</span>
  </a>

  <!-- Add Data Dropdown -->
  <div class="relative" x-data="{ open: false }">
    <button
      @click="open = !open"
      type="button"
      class="flex items-center justify-between w-full px-4 py-3 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 group transition-colors duration-200"
    >
      <div class="flex items-center">
        <i class="fas fa-plus-circle w-5 h-5 text-blue-600 dark:text-blue-400"></i>
        <span class="ml-3 font-medium">Add Data</span>
      </div>
      <i class="fas fa-chevron-down w-4 h-4 transition-transform duration-200" :class="{'transform rotate-180': open}"></i>
    </button>

    <div
      x-show="open"
      @click.outside="open = false"
      x-transition:enter="transition ease-out duration-200"
      x-transition:enter-start="opacity-0 transform scale-95"
      x-transition:enter-end="opacity-100 transform scale-100"
      x-transition:leave="transition ease-in duration-150"
      x-transition:leave-start="opacity-100 transform scale-100"
      x-transition:leave-end="opacity-0 transform scale-95"
      class="mt-1 w-full bg-gray-50 dark:bg-gray-700 rounded-lg shadow-lg overflow-hidden"
      style="display: none;"
    >

      <div class="py-1 max-h-60 overflow-y-auto shadow-inner">
        <a href="{% url 'admin_section:add_year' %}" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-blue-100 dark:hover:bg-blue-800 hover:text-blue-800 dark:hover:text-white transition-colors duration-150">
          Add Year
        </a>
        <a href="{% url 'admin_section:add_elogyear' %}" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-blue-100 dark:hover:bg-blue-800 hover:text-blue-800 dark:hover:text-white transition-colors duration-150">
          Add ELog Year
        </a>
        <a href="{% url 'admin_section:add_user' %}" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-blue-100 dark:hover:bg-blue-800 hover:text-blue-800 dark:hover:text-white transition-colors duration-150">
          Add Users
        </a>
        <a href="{% url 'admin_section:bulk_import_users' %}" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-blue-100 dark:hover:bg-blue-800 hover:text-blue-800 dark:hover:text-white transition-colors duration-150">
          Bulk Import Users
        </a>


        <a href="{% url 'admin_section:add_department' %}" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-blue-100 dark:hover:bg-blue-800 hover:text-blue-800 dark:hover:text-white transition-colors duration-150">
          Add Department
        </a>
        <a href="{% url 'admin_section:add_activity_type' %}" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-blue-100 dark:hover:bg-blue-800 hover:text-blue-800 dark:hover:text-white transition-colors duration-150">
          Add Activity Type
        </a>
        <a href="{% url 'admin_section:core_dia_pro_session_list' %}" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-blue-100 dark:hover:bg-blue-800 hover:text-blue-800 dark:hover:text-white transition-colors duration-150">
          Add Based Session Activity
        </a>
        <a href="{% url 'admin_section:add_group' %}" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-blue-100 dark:hover:bg-blue-800 hover:text-blue-800 dark:hover:text-white transition-colors duration-150">
          Add Group
        </a>
        <a href="{% url 'admin_section:add_student' %}" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-blue-100 dark:hover:bg-blue-800 hover:text-blue-800 dark:hover:text-white transition-colors duration-150">
          Add Student
        </a>
        <a href="{% url 'admin_section:add_doctor' %}" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-blue-100 dark:hover:bg-blue-800 hover:text-blue-800 dark:hover:text-white transition-colors duration-150">
          Add Doctor
        </a>
        <a href="{% url 'admin_section:add_staff' %}" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-blue-100 dark:hover:bg-blue-800 hover:text-blue-800 dark:hover:text-white transition-colors duration-150">
          Add Staff
        </a>
        <a href="{% url 'admin_section:add_training_site' %}" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-blue-100 dark:hover:bg-blue-800 hover:text-blue-800 dark:hover:text-white transition-colors duration-150">
          Add Training Site
        </a>
        <a href="{% url 'admin_section:mapped_attendance_list' %}" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-blue-100 dark:hover:bg-blue-800 hover:text-blue-800 dark:hover:text-white transition-colors duration-150">
          Mapped Attendance
        </a>
      </div>
    </div>
  </div>

  <!-- Reports -->
  <div class="relative" x-data="{ open: false }">
    <button @click="open = !open" class="nav-item w-full flex items-center justify-between px-4 py-3 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 group transition-colors duration-200">
      <div class="flex items-center">
        <i class="fas fa-chart-bar w-5 h-5 text-blue-600 dark:text-blue-400"></i>
        <span class="ml-3 font-medium">Reports</span>
      </div>
      <i class="fas fa-chevron-down w-4 h-4 transition-transform duration-200" :class="{'transform rotate-180': open}"></i>
    </button>

    <div
      x-show="open"
      @click.outside="open = false"
      x-transition:enter="transition ease-out duration-200"
      x-transition:enter-start="opacity-0 transform scale-95"
      x-transition:enter-end="opacity-100 transform scale-100"
      x-transition:leave="transition ease-in duration-150"
      x-transition:leave-start="opacity-100 transform scale-100"
      x-transition:leave-end="opacity-0 transform scale-95"
      class="mt-1 w-full bg-gray-50 dark:bg-gray-700 rounded-lg shadow-lg overflow-hidden"
      style="display: none;"
    >

      <div class="py-1 max-h-60 overflow-y-auto shadow-inner">
        <a href="{% url 'admin_section:department_report' %}" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-blue-100 dark:hover:bg-blue-800 hover:text-blue-800 dark:hover:text-white transition-colors duration-150">
          <i class="fas fa-building mr-2"></i>Department Report
        </a>
        <a href="{% url 'admin_section:student_report' %}" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-blue-100 dark:hover:bg-blue-800 hover:text-blue-800 dark:hover:text-white transition-colors duration-150">
          <i class="fas fa-user-graduate mr-2"></i>Student Report
        </a>
        <a href="{% url 'admin_section:tutor_report' %}" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-blue-100 dark:hover:bg-blue-800 hover:text-blue-800 dark:hover:text-white transition-colors duration-150">
          <i class="fas fa-user-md mr-2"></i>Tutor Report
        </a>
      </div>
    </div>
  </div>

  <!-- Date Restrictions -->
  <a href="{% url 'admin_section:date_restrictions' %}" class="nav-item flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 group transition-colors duration-200">
    <i class="fas fa-calendar-alt w-5 h-5 text-blue-600 dark:text-blue-400"></i>
    <span class="ml-3 font-medium">Date Restrictions</span>
  </a>

  <!-- Support -->
  <a href="{% url 'admin_section:admin_support' %}" class="nav-item flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 group transition-colors duration-200">
    <i class="fab fa-hire-a-helper w-5 h-5 text-blue-600 dark:text-blue-400"></i>
    <span class="ml-3 font-medium">Support</span>
  </a>

  <!-- Review -->
  <a href="{% url 'admin_section:admin_reviews' %}" class="nav-item flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 group transition-colors duration-200">
    <i class="fas fa-book-reader w-5 h-5 text-blue-600 dark:text-blue-400"></i>
    <span class="ml-3 font-medium">Review</span>
  </a>

  <!-- Notifications -->
  <a href="{% url 'admin_section:notifications' %}" class="nav-item flex items-center px-4 py-3 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 group transition-colors duration-200">
    <i class="fas fa-bell w-5 h-5 text-blue-600 dark:text-blue-400"></i>
    <span class="ml-3 font-medium">Notifications</span>
    {% if admin_unread_notifications_count > 0 %}
      <span class="ml-auto bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
        {{ admin_unread_notifications_count }}
      </span>
    {% endif %}
  </a>
{% endblock %}

{% block extra_scripts %}
<!-- No additional scripts needed as Alpine.js is loaded in base.html -->
{% endblock %}
