"""
PDF utility functions for adding common headers, logos, and formatting
"""
import os
from django.conf import settings
from reportlab.platypus import Paragraph, Spacer, Image
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib.enums import TA_CENTER
from reportlab.lib import colors


def add_agu_header(elements, title="Report"):
    """
    Add AGU logo and university name to PDF elements
    
    Args:
        elements: List of PDF elements to add header to
        title: Title of the report
    """
    # Define styles
    styles = getSampleStyleSheet()
    
    # Custom style for university name
    university_style = ParagraphStyle(
        'UniversityName',
        parent=styles['Heading1'],
        fontSize=16,
        textColor=colors.HexColor('#1f2937'),  # Dark gray
        alignment=TA_CENTER,
        spaceAfter=0.1*inch,
        fontName='Helvetica-Bold'
    )
    
    # Custom style for title
    title_style = ParagraphStyle(
        'ReportTitle',
        parent=styles['Heading1'],
        fontSize=14,
        textColor=colors.HexColor('#2563eb'),  # Blue
        alignment=TA_CENTER,
        spaceAfter=0.3*inch,
        fontName='Helvetica-Bold'
    )
    
    try:
        # Add AGU logo
        logo_path = os.path.join(settings.MEDIA_ROOT, 'agulogo.png')
        if os.path.exists(logo_path):
            # Create image with appropriate size
            logo = Image(logo_path, width=1.5*inch, height=1.5*inch)
            logo.hAlign = 'CENTER'
            elements.append(logo)
            elements.append(Spacer(1, 0.1*inch))
        else:
            print(f"Logo not found at: {logo_path}")
    except Exception as e:
        print(f"Error adding logo: {str(e)}")
    
    # Add university name
    elements.append(Paragraph("Arabian Gulf University", university_style))
    elements.append(Spacer(1, 0.1*inch))
    
    # Add title
    elements.append(Paragraph(title, title_style))
    elements.append(Spacer(1, 0.2*inch))
    
    return elements


def get_common_styles():
    """
    Get common styles for PDF documents
    """
    styles = getSampleStyleSheet()
    
    # Custom styles
    custom_styles = {
        'subtitle': ParagraphStyle(
            'CustomSubtitle',
            parent=styles['Heading2'],
            fontSize=12,
            textColor=colors.HexColor('#374151'),
            spaceAfter=0.15*inch,
            fontName='Helvetica-Bold'
        ),
        'normal': ParagraphStyle(
            'CustomNormal',
            parent=styles['Normal'],
            fontSize=10,
            textColor=colors.HexColor('#374151'),
            spaceAfter=0.1*inch,
            fontName='Helvetica'
        ),
        'small': ParagraphStyle(
            'CustomSmall',
            parent=styles['Normal'],
            fontSize=8,
            textColor=colors.HexColor('#6b7280'),
            spaceAfter=0.05*inch,
            fontName='Helvetica'
        )
    }
    
    return custom_styles


def add_footer_info(elements, generated_by=None, export_date=None):
    """
    Add footer information to PDF
    
    Args:
        elements: List of PDF elements
        generated_by: Name of the person who generated the report
        export_date: Date when the report was generated
    """
    styles = get_common_styles()
    
    elements.append(Spacer(1, 0.3*inch))
    
    if generated_by:
        elements.append(Paragraph(f"Generated by: {generated_by}", styles['small']))
    
    if export_date:
        elements.append(Paragraph(f"Generated on: {export_date}", styles['small']))
    
    return elements
