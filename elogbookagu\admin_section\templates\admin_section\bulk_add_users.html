{% extends 'base.html' %}

{% block title %}Bulk Add Users{% endblock %}

{% block content %}
{% include 'components/admin_auth_navbar.html' %}
<div class="container mx-auto px-4 py-8 mt-16">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <h2 class="text-2xl font-bold text-gray-800 dark:text-white mb-6">Bulk Add Users</h2>

        <!-- Messages -->
        {% if messages %}
        <div class="mb-6">
            {% for message in messages %}
            <div class="p-4 mb-4 rounded-lg {% if message.tags == 'success' %}bg-green-100 text-green-800{% elif message.tags == 'error' %}bg-red-100 text-red-800{% elif message.tags == 'warning' %}bg-yellow-100 text-yellow-800{% else %}bg-blue-100 text-blue-800{% endif %}">
                {{ message }}
            </div>
            {% endfor %}
        </div>
        {% endif %}

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Upload Form -->
            <div>
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                    <h3 class="text-xl font-semibold mb-4 text-gray-800 dark:text-white">Upload CSV File</h3>
                    <form method="post" enctype="multipart/form-data" class="space-y-4">
                        {% csrf_token %}
                        {{ form }}
                        <div class="pt-4">
                            <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Upload and Import
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Download Templates -->
                <div class="mt-6 space-y-3">
                    <h4 class="font-semibold text-gray-700 dark:text-gray-300">Download Templates:</h4>
                    <a href="{% url 'admin_section:download_user_template' %}?type=general" class="block w-full bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded text-center">
                        Download General Template
                    </a>
                    <a href="{% url 'admin_section:download_user_template' %}?type=student" class="block w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded text-center">
                        Download Student Template
                    </a>
                    <a href="{% url 'admin_section:download_user_template' %}?type=doctor" class="block w-full bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded text-center">
                        Download Doctor Template
                    </a>
                    <a href="{% url 'admin_section:download_user_template' %}?type=staff" class="block w-full bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded text-center">
                        Download Staff Template
                    </a>
                </div>
            </div>

            <!-- Instructions and Results -->
            <div>
                {% if results %}
                <!-- Import Results -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
                    <h3 class="text-xl font-semibold mb-4 text-gray-800 dark:text-white">Import Results</h3>
                    <div class="grid grid-cols-2 gap-4 mb-4">
                        <div class="bg-green-100 p-4 rounded-lg text-center">
                            <p class="text-sm text-gray-600">Successfully Imported</p>
                            <p class="text-2xl font-bold text-green-600">{{ results.success_count }}</p>
                        </div>
                        <div class="bg-red-100 p-4 rounded-lg text-center">
                            <p class="text-sm text-gray-600">Failed to Import</p>
                            <p class="text-2xl font-bold text-red-600">{{ results.error_count }}</p>
                        </div>
                    </div>
                    {% if results.error_messages %}
                    <div class="mt-4">
                        <h4 class="font-semibold text-gray-700 mb-2">Error Details:</h4>
                        <div class="bg-red-50 p-3 rounded-lg max-h-60 overflow-y-auto">
                            <ul class="list-disc pl-5 space-y-1">
                                {% for error in results.error_messages %}
                                <li class="text-sm text-red-600">{{ error }}</li>
                                {% endfor %}
                            </ul>
                        </div>
                    </div>
                    {% endif %}
                </div>
                {% endif %}

                <!-- Instructions -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                    <h3 class="text-xl font-semibold mb-4 text-gray-800 dark:text-white">Instructions</h3>
                    <div class="space-y-4">
                        <div>
                            <h4 class="font-semibold text-gray-700 dark:text-gray-300 mb-2">General Required Fields</h4>
                            <ul class="list-disc pl-5 space-y-1 text-gray-600 dark:text-gray-400">
                                <li>username (required)</li>
                                <li>email (required)</li>
                                <li>password (required)</li>
                                <li>first_name (required)</li>
                                <li>last_name (required)</li>
                                <li>role (required: admin/student/doctor/staff)</li>
                                <li>city (required)</li>
                                <li>country (required)</li>
                                <li>phone_no (required)</li>
                            </ul>
                        </div>

                        <div>
                            <h4 class="font-semibold text-gray-700 dark:text-gray-300 mb-2">Role-Specific Required Fields</h4>
                            <ul class="list-disc pl-5 space-y-1 text-gray-600 dark:text-gray-400">
                                <li><strong>For Students:</strong> student_id (required), group (optional - can be group ID or group name like 'B1', 'A2', etc.)</li>
                                <li><strong>For Doctors:</strong> speciality (optional)</li>
                            </ul>
                        </div>

                        <div>
                            <h4 class="font-semibold text-gray-700 dark:text-gray-300 mb-2">Optional Fields</h4>
                            <ul class="list-disc pl-5 space-y-1 text-gray-600 dark:text-gray-400">
                                <li>bio</li>
                                <li>speciality (for non-doctor roles)</li>
                            </ul>
                        </div>

                        <div>
                            <h4 class="font-semibold text-gray-700 dark:text-gray-300 mb-2">Example Formats</h4>

                            <h5 class="font-medium text-gray-700 dark:text-gray-300 mt-3 mb-1">Student Example:</h5>
                            <div class="bg-gray-100 dark:bg-gray-700 p-3 rounded-lg overflow-x-auto mb-3">
                                <code class="text-sm">
                                    username,email,password,first_name,last_name,role,student_id,group,city,country,phone_no<br>
                                    student1,<EMAIL>,Pass123!,John,Student,student,STU12345,B1,New York,USA,1234567890<br>
                                    student2,<EMAIL>,Pass456!,Jane,Student,student,STU67890,A2,London,UK,9876543210
                                </code>
                            </div>

                            <h5 class="font-medium text-gray-700 dark:text-gray-300 mt-3 mb-1">Doctor Example:</h5>
                            <div class="bg-gray-100 dark:bg-gray-700 p-3 rounded-lg overflow-x-auto">
                                <code class="text-sm">
                                    username,email,password,first_name,last_name,role,speciality,city,country,phone_no,bio<br>
                                    doctor1,<EMAIL>,Pass123!,John,Doctor,doctor,Cardiology,New York,USA,1234567890,Experienced cardiologist
                                </code>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}