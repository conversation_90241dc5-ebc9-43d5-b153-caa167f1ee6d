# Generated by Django 5.1.4 on 2025-05-12 08:15

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('accounts', '0008_alter_staff_departments'),
        ('student_section', '0010_studentlogformmodel_review_deadline'),
    ]

    operations = [
        migrations.CreateModel(
            name='StaffNotification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=100)),
                ('message', models.TextField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('is_read', models.BooleanField(default=False)),
                ('log_entry', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='student_section.studentlogformmodel')),
                ('recipient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to='accounts.staff')),
            ],
            options={
                'verbose_name': 'Staff Notification',
                'verbose_name_plural': 'Staff Notifications',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='StaffSupportTicket',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('subject', models.CharField(max_length=100)),
                ('description', models.TextField()),
                ('date_created', models.DateTimeField(auto_now_add=True)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('solved', 'Solved')], default='pending', max_length=20)),
                ('admin_comments', models.TextField(blank=True)),
                ('resolved_date', models.DateTimeField(blank=True, null=True)),
                ('staff', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='support_tickets', to='accounts.staff')),
            ],
            options={
                'verbose_name': 'Staff Support Ticket',
                'verbose_name_plural': 'Staff Support Tickets',
                'ordering': ['-date_created'],
            },
        ),
    ]
