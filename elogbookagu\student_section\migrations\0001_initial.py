# Generated by Django 5.1.4 on 2025-03-05 07:57

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('publicpage', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='ActivityType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('department', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='activity_types', to='publicpage.department')),
            ],
            options={
                'unique_together': {('name', 'department')},
            },
        ),
        migrations.CreateModel(
            name='CoreDiaProSession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON>ield(max_length=200)),
                ('activity_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='core_dia_pro_sessions', to='student_section.activitytype')),
                ('department', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='core_dia_pro_sessions', to='publicpage.department')),
            ],
            options={
                'verbose_name': 'Core Diagnosis Procedure Session',
                'verbose_name_plural': 'Core Diagnosis Procedure Sessions',
            },
        ),
    ]
