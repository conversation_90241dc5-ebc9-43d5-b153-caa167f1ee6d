{% extends 'base.html' %}

{% block title %}
  Student Profile
{% endblock %}

{% block navbar %}
  {% include './components/student_auth_navbar.html' %}
{% endblock %}

{% block content %}
  <div class="container mx-auto px-4 py-8">
    <!-- Profile Header with Background -->
    <div class="relative mb-8">
      <div class="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 h-48 rounded-t-xl"></div>
      <div class="relative pt-6 px-8">
        <div class="flex flex-col md:flex-row items-center">
          <!-- Profile Picture with Enhanced Styling -->
          <div class="relative z-10 mb-4 md:mb-0">
            <div class="relative group">
              <div class="w-32 h-32 md:w-40 md:h-40 rounded-full border-4 border-white dark:border-gray-800 overflow-hidden shadow-xl">
                <img src="{{ profile_photo }}" alt="Profile Photo" onerror="this.src='/media/profiles/default.jpg';"
                  class="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110" />
              </div>

              <!-- Upload Button with Improved Styling -->
              <div class="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300">
                <label for="profile-photo-upload" class="cursor-pointer bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-full shadow-lg transform transition-transform duration-300 hover:scale-105">
                  <i class="fas fa-camera mr-1"></i> Change Photo
                </label>
              </div>

              <!-- Hidden File Input -->
              <form id="profile-photo-form" action="{% url 'student_section:update_profile_photo' %}" method="post" enctype="multipart/form-data" class="hidden">
                {% csrf_token %}
                <input type="file" id="profile-photo-upload" name="profile_photo" accept="image/*" class="hidden" />
              </form>
            </div>
          </div>

          <!-- Profile Title and Name -->
          <div class="md:ml-6 text-center md:text-left">
            <h1 class="text-3xl font-bold text-white">{{ full_name }}</h1>
            <p class="text-blue-100 text-lg">Student</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content with Cards -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-xl p-6 md:p-8 transition-all duration-300 hover:shadow-2xl -mt-10">
      <!-- Profile Content -->
      <div class="grid md:grid-cols-2 gap-8">
        <!-- Left Column -->
        <div class="space-y-6">
          <!-- Personal Information Card -->
          <div class="bg-gray-50 dark:bg-gray-700 rounded-xl shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg">
            <div class="bg-blue-600 dark:bg-blue-800 px-6 py-4">
              <h2 class="text-xl font-semibold text-white flex items-center">
                <i class="fas fa-user-circle mr-2"></i> Personal Information
              </h2>
            </div>
            <div class="p-6">
              <div class="space-y-4">
                <div class="flex items-center border-b border-gray-200 dark:border-gray-600 pb-3">
                  <span class="font-medium w-32 text-gray-700 dark:text-gray-300 flex items-center">
                    <i class="fas fa-envelope text-blue-500 mr-2"></i> Email:
                  </span>
                  <span class="dark:text-white">{{ user_email }}</span>
                </div>
                <div class="flex items-center border-b border-gray-200 dark:border-gray-600 pb-3">
                  <span class="font-medium w-32 text-gray-700 dark:text-gray-300 flex items-center">
                    <i class="fas fa-users text-blue-500 mr-2"></i> Group:
                  </span>
                  <span class="dark:text-white">{{ group_name }}</span>
                </div>
                <div class="flex items-center border-b border-gray-200 dark:border-gray-600 pb-3">
                  <span class="font-medium w-32 text-gray-700 dark:text-gray-300 flex items-center">
                    <i class="fas fa-graduation-cap text-blue-500 mr-2"></i> Year:
                  </span>
                  <span class="dark:text-white">{{ log_year }}</span>
                </div>
                <div class="flex items-center">
                  <span class="font-medium w-32 text-gray-700 dark:text-gray-300 flex items-center">
                    <i class="fas fa-calendar-alt text-blue-500 mr-2"></i> Period:
                  </span>
                  <span class="dark:text-white">{{ log_year_section }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Biography Card -->
          <div class="bg-gray-50 dark:bg-gray-700 rounded-xl shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg">
            <div class="bg-purple-600 dark:bg-purple-800 px-6 py-4 flex justify-between items-center">
              <h2 class="text-xl font-semibold text-white flex items-center">
                <i class="fas fa-book-open mr-2"></i> Biography
              </h2>
              <button id="openModalBio" class="px-3 py-1 bg-white text-purple-600 rounded-lg hover:bg-gray-100 transition-colors duration-200 flex items-center text-sm font-medium">
                <i class="fas fa-edit mr-1"></i> Edit
              </button>
            </div>
            <div class="p-6">
              <div class="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-inner min-h-[100px]">
                <p class="italic text-gray-700 dark:text-gray-300 biography-text">
                  {{ user_bio|default:"No biography provided yet. Click Edit to add your bio." }}
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- Right Column -->
        <div class="space-y-6">
          <!-- Contact Information Card -->
          <div class="bg-gray-50 dark:bg-gray-700 rounded-xl shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg">
            <div class="bg-green-600 dark:bg-green-800 px-6 py-4 flex justify-between items-center">
              <h2 class="text-xl font-semibold text-white flex items-center">
                <i class="fas fa-address-card mr-2"></i> Contact Information
              </h2>
              <button id="openModal" class="px-3 py-1 bg-white text-green-600 rounded-lg hover:bg-gray-100 transition-colors duration-200 flex items-center text-sm font-medium">
                <i class="fas fa-edit mr-1"></i> Edit
              </button>
            </div>
            <div class="p-6">
              <div class="space-y-4">
                <div class="flex items-center border-b border-gray-200 dark:border-gray-600 pb-3">
                  <span class="font-medium w-32 text-gray-700 dark:text-gray-300 flex items-center">
                    <i class="fas fa-phone text-green-500 mr-2"></i> Phone:
                  </span>
                  <span id="phone-text" class="dark:text-white">{{ user_phone|default:"Not provided" }}</span>
                </div>
                <div class="flex items-center border-b border-gray-200 dark:border-gray-600 pb-3">
                  <span class="font-medium w-32 text-gray-700 dark:text-gray-300 flex items-center">
                    <i class="fas fa-city text-green-500 mr-2"></i> City:
                  </span>
                  <span id="city-text" class="dark:text-white">{{ user_city|default:"Not provided" }}</span>
                </div>
                <div class="flex items-center">
                  <span class="font-medium w-32 text-gray-700 dark:text-gray-300 flex items-center">
                    <i class="fas fa-globe text-green-500 mr-2"></i> Country:
                  </span>
                  <span id="country-text" class="dark:text-white">{{ user_country|default:"Not provided" }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Quick Stats Card -->
          <div class="bg-gray-50 dark:bg-gray-700 rounded-xl shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg">
            <div class="bg-amber-600 dark:bg-amber-800 px-6 py-4">
              <h2 class="text-xl font-semibold text-white flex items-center">
                <i class="fas fa-chart-line mr-2"></i> Activity Stats
              </h2>
            </div>
            <div class="p-6">
              <div class="grid grid-cols-2 gap-4">
                <div class="bg-white dark:bg-gray-800 rounded-lg p-4 text-center shadow-sm">
                  <div class="text-3xl font-bold text-blue-600 dark:text-blue-400">
                    <i class="fas fa-clipboard-list"></i>
                  </div>
                  <p class="text-gray-500 dark:text-gray-400 text-sm mt-1">Logs Submitted</p>
                  <p class="text-2xl font-bold text-gray-800 dark:text-white mt-1">{{ logs_count|default:"0" }}</p>
                </div>
                <div class="bg-white dark:bg-gray-800 rounded-lg p-4 text-center shadow-sm">
                  <div class="text-3xl font-bold text-green-600 dark:text-green-400">
                    <i class="fas fa-check-circle"></i>
                  </div>
                  <p class="text-gray-500 dark:text-gray-400 text-sm mt-1">Approved</p>
                  <p class="text-2xl font-bold text-gray-800 dark:text-white mt-1">{{ approved_count|default:"0" }}</p>
                </div>
                <div class="bg-white dark:bg-gray-800 rounded-lg p-4 text-center shadow-sm">
                  <div class="text-3xl font-bold text-amber-600 dark:text-amber-400">
                    <i class="fas fa-clock"></i>
                  </div>
                  <p class="text-gray-500 dark:text-gray-400 text-sm mt-1">Pending</p>
                  <p class="text-2xl font-bold text-gray-800 dark:text-white mt-1">{{ pending_count|default:"0" }}</p>
                </div>
                <div class="bg-white dark:bg-gray-800 rounded-lg p-4 text-center shadow-sm">
                  <div class="text-3xl font-bold text-purple-600 dark:text-purple-400">
                    <i class="fas fa-hospital"></i>
                  </div>
                  <p class="text-gray-500 dark:text-gray-400 text-sm mt-1">Departments</p>
                  <p class="text-2xl font-bold text-gray-800 dark:text-white mt-1">{{ departments_count|default:"0" }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Contact Information Modal -->
  <div id="editModal" class="hidden fixed inset-0 bg-black bg-opacity-70 z-50 backdrop-blur-sm transition-all duration-300">
    <div class="flex items-center justify-center min-h-screen px-4">
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-2xl w-full max-w-2xl transform transition-all duration-300 scale-95 opacity-0" id="contactModalContent">
        <!-- Modal Header with Green Gradient -->
        <div class="bg-gradient-to-r from-green-500 to-green-700 rounded-t-xl p-6">
          <div class="flex justify-between items-center">
            <h2 class="text-2xl font-bold text-white flex items-center">
              <i class="fas fa-address-card mr-3"></i> Edit Contact Information
            </h2>
            <button class="text-white hover:text-gray-200 transition-colors duration-200" id="closeModal">
              <i class="fas fa-times text-xl"></i>
            </button>
          </div>
        </div>

        <!-- Modal Body -->
        <div class="p-8">
          <!-- Edit Form -->
          <form id="contactForm" action="{% url 'student_section:update_contact_info' %}" method="post">
            {% csrf_token %}
            <div class="mb-6">
              <label for="phone" class="block text-gray-700 dark:text-gray-300 text-sm font-medium mb-2">
                <i class="fas fa-phone text-green-500 mr-2"></i> Phone Number
              </label>
              <input type="text" id="phone" name="phone" value="{{ user_phone }}"
                class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200 dark:bg-gray-700 dark:text-white"
                placeholder="Enter your phone number" />
            </div>

            <div class="mb-6">
              <label for="city" class="block text-gray-700 dark:text-gray-300 text-sm font-medium mb-2">
                <i class="fas fa-city text-green-500 mr-2"></i> City
              </label>
              <input type="text" id="city" name="city" value="{{ user_city }}"
                class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200 dark:bg-gray-700 dark:text-white"
                placeholder="Enter your city" />
            </div>

            <div class="mb-6">
              <label for="country" class="block text-gray-700 dark:text-gray-300 text-sm font-medium mb-2">
                <i class="fas fa-globe text-green-500 mr-2"></i> Country
              </label>
              <input type="text" id="country" name="country" value="{{ user_country }}"
                class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-all duration-200 dark:bg-gray-700 dark:text-white"
                placeholder="Enter your country" />
            </div>

            <!-- Status Message Area -->
            <div id="contactFormStatus" class="mb-6 hidden rounded-lg p-4"></div>

            <!-- Submit Button -->
            <button type="submit" id="contactSubmitBtn"
              class="w-full py-3 px-4 bg-gradient-to-r from-green-500 to-green-700 text-white font-semibold rounded-lg shadow-md hover:from-green-600 hover:to-green-800 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-75 transition-all duration-200 flex justify-center items-center">
              <span id="contactBtnText">Save Changes</span>
              <span id="contactLoadingSpinner" class="hidden ml-2 inline-block h-5 w-5 animate-spin rounded-full border-3 border-solid border-white border-r-transparent"></span>
            </button>
          </form>
        </div>
      </div>
    </div>
  </div>

  <!-- Biography Modal -->
  <div id="editModalbio" class="hidden fixed inset-0 bg-black bg-opacity-70 z-50 backdrop-blur-sm transition-all duration-300">
    <div class="flex items-center justify-center min-h-screen px-4">
      <div class="bg-white dark:bg-gray-800 rounded-xl shadow-2xl w-full max-w-2xl transform transition-all duration-300 scale-95 opacity-0" id="bioModalContent">
        <!-- Modal Header with Purple Gradient -->
        <div class="bg-gradient-to-r from-purple-500 to-purple-700 rounded-t-xl p-6">
          <div class="flex justify-between items-center">
            <h2 class="text-2xl font-bold text-white flex items-center">
              <i class="fas fa-book-open mr-3"></i> Edit Your Biography
            </h2>
            <button class="text-white hover:text-gray-200 transition-colors duration-200" id="closeModalBio">
              <i class="fas fa-times text-xl"></i>
            </button>
          </div>
        </div>

        <!-- Modal Body -->
        <div class="p-8">
          <!-- Edit Form -->
          <form id="bioForm" action="{% url 'student_section:update_biography' %}" method="post">
            {% csrf_token %}
            <div class="mb-6">
              <label for="biography" class="block text-gray-700 dark:text-gray-300 text-sm font-medium mb-2">
                <i class="fas fa-pen text-purple-500 mr-2"></i> Tell us about yourself
              </label>
              <textarea id="biography" name="biography"
                class="w-full p-4 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 dark:bg-gray-700 dark:text-white min-h-[150px]"
                placeholder="Share your background, interests, and aspirations...">{{ user_bio }}</textarea>
              <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">
                <i class="fas fa-info-circle mr-1"></i> Your biography will be visible on your profile
              </p>
            </div>

            <!-- Status Message Area -->
            <div id="bioFormStatus" class="mb-6 hidden rounded-lg p-4"></div>

            <!-- Submit Button -->
            <button type="submit" id="bioSubmitBtn"
              class="w-full py-3 px-4 bg-gradient-to-r from-purple-500 to-purple-700 text-white font-semibold rounded-lg shadow-md hover:from-purple-600 hover:to-purple-800 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-opacity-75 transition-all duration-200 flex justify-center items-center">
              <span id="bioBtnText">Save Biography</span>
              <span id="bioLoadingSpinner" class="hidden ml-2 inline-block h-5 w-5 animate-spin rounded-full border-3 border-solid border-white border-r-transparent"></span>
            </button>
          </form>
        </div>
      </div>
    </div>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Contact information Modal with animations
      const contactModal = document.getElementById('editModal');
      const contactModalContent = document.getElementById('contactModalContent');

      document.getElementById('openModal').addEventListener('click', function() {
        // Show the modal background
        contactModal.classList.remove('hidden');

        // Animate the modal content after a tiny delay
        setTimeout(() => {
          contactModalContent.classList.remove('scale-95', 'opacity-0');
          contactModalContent.classList.add('scale-100', 'opacity-100');
        }, 50);
      });

      function closeContactModal() {
        // Animate out
        contactModalContent.classList.remove('scale-100', 'opacity-100');
        contactModalContent.classList.add('scale-95', 'opacity-0');

        // Hide the modal after animation completes
        setTimeout(() => {
          contactModal.classList.add('hidden');
        }, 300);
      }

      document.getElementById('closeModal').addEventListener('click', closeContactModal);

      contactModal.addEventListener('click', function(event) {
        if (event.target === this) {
          closeContactModal();
        }
      });

      // Biography Modal with animations
      const bioModal = document.getElementById('editModalbio');
      const bioModalContent = document.getElementById('bioModalContent');

      document.getElementById('openModalBio').addEventListener('click', function() {
        // Show the modal background
        bioModal.classList.remove('hidden');

        // Animate the modal content after a tiny delay
        setTimeout(() => {
          bioModalContent.classList.remove('scale-95', 'opacity-0');
          bioModalContent.classList.add('scale-100', 'opacity-100');
        }, 50);
      });

      function closeBioModal() {
        // Animate out
        bioModalContent.classList.remove('scale-100', 'opacity-100');
        bioModalContent.classList.add('scale-95', 'opacity-0');

        // Hide the modal after animation completes
        setTimeout(() => {
          bioModal.classList.add('hidden');
        }, 300);
      }

      document.getElementById('closeModalBio').addEventListener('click', closeBioModal);

      bioModal.addEventListener('click', function(event) {
        if (event.target === this) {
          closeBioModal();
        }
      });

      // Add keyboard support for closing modals with Escape key
      document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
          if (!contactModal.classList.contains('hidden')) {
            closeContactModal();
          }
          if (!bioModal.classList.contains('hidden')) {
            closeBioModal();
          }
        }
      });

      // Profile photo upload handler
      document.getElementById('profile-photo-upload').addEventListener('change', function () {
        if (this.files && this.files[0]) {
          const form = document.getElementById('profile-photo-form');
          const formData = new FormData(form);

          // Show a loading indicator
          const profileImg = document.querySelector('[alt="Profile Photo"]');
          profileImg.style.opacity = '0.5';

          fetch(form.action, {
            method: 'POST',
            body: formData,
            headers: {
              'X-Requested-With': 'XMLHttpRequest'
            }
          })
          .then(response => response.json())
          .then(data => {
            if (data.success) {
              // Update the profile image
              profileImg.src = data.profile_photo;

              // Show success message
              alert('Profile photo updated successfully!');
            } else {
              alert(data.error || 'Failed to update profile photo');
            }
          })
          .catch(error => {
            alert('Network error. Please try again.');
          })
          .finally(() => {
            // Reset opacity
            profileImg.style.opacity = '1';
          });
        }
      })

      // Contact form submission with enhanced feedback
      const contactForm = document.getElementById('contactForm');
      if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
          e.preventDefault();

          // Show loading state
          const submitBtn = document.getElementById('contactSubmitBtn');
          const btnText = document.getElementById('contactBtnText');
          const loadingSpinner = document.getElementById('contactLoadingSpinner');
          const formStatus = document.getElementById('contactFormStatus');

          submitBtn.disabled = true;
          btnText.textContent = 'Saving...';
          loadingSpinner.classList.remove('hidden');

          // Get form data
          const formData = new FormData(contactForm);

          // Send AJAX request
          fetch(contactForm.action, {
            method: 'POST',
            body: formData,
            headers: {
              'X-Requested-With': 'XMLHttpRequest'
            }
          })
          .then(response => response.json())
          .then(data => {
            // Reset button state
            submitBtn.disabled = false;
            btnText.textContent = 'Save Changes';
            loadingSpinner.classList.add('hidden');

            if (data.success) {
              // Show success message with icon
              formStatus.classList.remove('hidden', 'bg-red-100', 'text-red-700');
              formStatus.classList.add('bg-green-100', 'text-green-700');
              formStatus.innerHTML = `
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <i class="fas fa-check-circle text-green-500 text-xl"></i>
                  </div>
                  <div class="ml-3">
                    <p class="text-sm font-medium text-green-800">Contact information updated successfully!</p>
                  </div>
                </div>
              `;

              // Update the displayed contact info with animation
              const phoneText = document.getElementById('phone-text');
              const cityText = document.getElementById('city-text');
              const countryText = document.getElementById('country-text');

              // Add highlight animation
              [phoneText, cityText, countryText].forEach(el => {
                el.classList.add('transition-all', 'duration-500');
                el.style.backgroundColor = 'rgba(16, 185, 129, 0.2)';  // Light green background

                // Set the new values
                if (el === phoneText) el.textContent = data.user_phone || 'Not provided';
                if (el === cityText) el.textContent = data.user_city || 'Not provided';
                if (el === countryText) el.textContent = data.user_country || 'Not provided';

                // Remove highlight after animation
                setTimeout(() => {
                  el.style.backgroundColor = 'transparent';
                }, 2000);
              });

              // Close modal after a delay
              setTimeout(() => {
                closeContactModal();
              }, 1500);
            } else {
              // Show error message with icon
              formStatus.classList.remove('hidden', 'bg-green-100', 'text-green-700');
              formStatus.classList.add('bg-red-100', 'text-red-700');
              formStatus.innerHTML = `
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-circle text-red-500 text-xl"></i>
                  </div>
                  <div class="ml-3">
                    <p class="text-sm font-medium text-red-800">${data.error || 'An error occurred. Please try again.'}</p>
                  </div>
                </div>
              `;
            }
          })
          .catch(error => {
            // Handle error with icon
            submitBtn.disabled = false;
            btnText.textContent = 'Save Changes';
            loadingSpinner.classList.add('hidden');

            formStatus.classList.remove('hidden', 'bg-green-100', 'text-green-700');
            formStatus.classList.add('bg-red-100', 'text-red-700');
            formStatus.innerHTML = `
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <i class="fas fa-wifi-slash text-red-500 text-xl"></i>
                </div>
                <div class="ml-3">
                  <p class="text-sm font-medium text-red-800">Network error. Please check your connection and try again.</p>
                </div>
              </div>
            `;
          });
        });
      }

      // Biography form submission with enhanced feedback
      const bioForm = document.getElementById('bioForm');
      if (bioForm) {
        bioForm.addEventListener('submit', function(e) {
          e.preventDefault();

          // Show loading state
          const submitBtn = document.getElementById('bioSubmitBtn');
          const btnText = document.getElementById('bioBtnText');
          const loadingSpinner = document.getElementById('bioLoadingSpinner');
          const formStatus = document.getElementById('bioFormStatus');

          submitBtn.disabled = true;
          btnText.textContent = 'Saving...';
          loadingSpinner.classList.remove('hidden');

          // Get form data
          const formData = new FormData(bioForm);

          // Send AJAX request
          fetch(bioForm.action, {
            method: 'POST',
            body: formData,
            headers: {
              'X-Requested-With': 'XMLHttpRequest'
            }
          })
          .then(response => response.json())
          .then(data => {
            // Reset button state
            submitBtn.disabled = false;
            btnText.textContent = 'Save Biography';
            loadingSpinner.classList.add('hidden');

            if (data.success) {
              // Show success message with icon
              formStatus.classList.remove('hidden', 'bg-red-100', 'text-red-700');
              formStatus.classList.add('bg-green-100', 'text-green-700');
              formStatus.innerHTML = `
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <i class="fas fa-check-circle text-green-500 text-xl"></i>
                  </div>
                  <div class="ml-3">
                    <p class="text-sm font-medium text-green-800">Biography updated successfully!</p>
                  </div>
                </div>
              `;

              // Update the displayed bio on the page with animation
              const bioElement = document.querySelector('.biography-text');
              if (bioElement) {
                bioElement.classList.add('transition-all', 'duration-500');
                bioElement.style.backgroundColor = 'rgba(139, 92, 246, 0.2)';  // Light purple background

                // Set the new value
                bioElement.textContent = data.user_bio || 'No biography provided yet. Click Edit to add your bio.';

                // Remove highlight after animation
                setTimeout(() => {
                  bioElement.style.backgroundColor = 'transparent';
                }, 2000);
              }

              // Close modal after a delay
              setTimeout(() => {
                closeBioModal();
              }, 1500);
            } else {
              // Show error message with icon
              formStatus.classList.remove('hidden', 'bg-green-100', 'text-green-700');
              formStatus.classList.add('bg-red-100', 'text-red-700');
              formStatus.innerHTML = `
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-circle text-red-500 text-xl"></i>
                  </div>
                  <div class="ml-3">
                    <p class="text-sm font-medium text-red-800">${data.error || 'An error occurred. Please try again.'}</p>
                  </div>
                </div>
              `;
            }
          })
          .catch(error => {
            // Handle error with icon
            submitBtn.disabled = false;
            btnText.textContent = 'Save Biography';
            loadingSpinner.classList.add('hidden');

            formStatus.classList.remove('hidden', 'bg-green-100', 'text-green-700');
            formStatus.classList.add('bg-red-100', 'text-red-700');
            formStatus.innerHTML = `
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <i class="fas fa-wifi-slash text-red-500 text-xl"></i>
                </div>
                <div class="ml-3">
                  <p class="text-sm font-medium text-red-800">Network error. Please check your connection and try again.</p>
                </div>
              </div>
            `;
          });
        });
      }
    });
  </script>
{% endblock %}
