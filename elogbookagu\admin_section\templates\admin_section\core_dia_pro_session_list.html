{% extends 'base.html' %}
{% load widget_tweaks %}

{% block title %}
  Core Diagnosis Procedure Sessions
{% endblock %}

{% block navbar %}
  {% include 'components/admin_auth_navbar.html' %}
{% endblock %}

{% block content %}
  <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-10 lg:py-12">
    <!-- Page Header -->
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
      <div>
        <h1 class="text-2xl sm:text-3xl font-bold text-gray-800 dark:text-gray-100">
          <i class="fas fa-calendar-plus text-blue-500 mr-2"></i> Core Diagnosis Procedure Sessions
        </h1>
        <p class="text-gray-600 dark:text-gray-400 mt-1">Create and manage diagnostic procedure sessions for departments</p>
      </div>

      <!-- Search Form -->
      <div class="w-full md:w-auto">
        <form method="get" action="{% url 'admin_section:core_dia_pro_session_list' %}" class="relative flex items-center">
          <div class="relative flex-grow">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <i class="fas fa-search text-gray-400"></i>
            </div>
            <input
              type="text"
              id="search-input"
              name="q"
              value="{{ search_query }}"
              class="w-full pl-10 pr-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:outline-none"
              placeholder="Search sessions..."
            />
            {% if search_query %}
            <button
              type="button"
              id="clear-search"
              class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              title="Clear search"
            >
              <i class="fas fa-times"></i>
            </button>
            {% endif %}
          </div>
          <button
            type="submit"
            class="ml-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 transition-colors duration-200 flex items-center"
          >
            <i class="fas fa-search mr-1"></i> Search
          </button>
        </form>
      </div>
    </div>

    <!-- Messages -->
    {% if messages %}
      <div class="fixed top-4 right-4 z-50 w-full max-w-sm">
        {% for message in messages %}
          <div class="p-4 mb-3 rounded-lg shadow-lg flex items-start {% if message.tags == 'success' %}bg-green-100 text-green-800 dark:bg-green-900/70 dark:text-green-200{% else %}bg-red-100 text-red-800 dark:bg-red-900/70 dark:text-red-200{% endif %} transform transition-all duration-300 animate-fade-in-down">
            <div class="flex-shrink-0">
              <i class="fas {% if message.tags == 'success' %}fa-check-circle text-green-500{% else %}fa-exclamation-circle text-red-500{% endif %} mt-0.5"></i>
            </div>
            <div class="ml-3 flex-grow">
              <p class="text-sm font-medium">{{ message }}</p>
            </div>
            <button type="button" class="ml-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 close-message">
              <i class="fas fa-times"></i>
            </button>
          </div>
        {% endfor %}
      </div>
    {% endif %}

    <!-- Create/Edit Form -->
    <div class="max-w-2xl mx-auto bg-white dark:bg-gray-800 shadow-lg rounded-lg overflow-hidden mb-8 border-t-4 {% if editing %}border-yellow-500{% else %}border-blue-500{% endif %}">
      <div class="p-6 sm:p-8">
        <div class="flex items-center mb-6">
          <div class="flex-shrink-0 mr-4 {% if editing %}text-yellow-500{% else %}text-blue-500{% endif %}">
            <i class="fas {% if editing %}fa-edit text-2xl{% else %}fa-plus-circle text-2xl{% endif %}"></i>
          </div>
          <h2 class="text-xl font-semibold text-gray-800 dark:text-gray-100">
            {{ editing|yesno:'Edit Existing,Add New' }} Session
          </h2>
        </div>

        <!-- Form Errors Summary -->
        {% if form.errors or form.non_field_errors %}
        <div class="mb-6 p-4 bg-red-50 dark:bg-red-900/20 rounded-lg border-l-4 border-red-500">
          <div class="flex items-start">
            <div class="flex-shrink-0">
              <i class="fas fa-exclamation-circle text-red-600 dark:text-red-400"></i>
            </div>
            <div class="ml-3">
              <h4 class="text-sm font-medium text-red-800 dark:text-red-300">Please correct the following errors:</h4>
              {% if form.non_field_errors %}
              <ul class="mt-1 text-sm text-red-700 dark:text-red-300 list-disc list-inside">
                {% for error in form.non_field_errors %}
                <li>{{ error }}</li>
                {% endfor %}
              </ul>
              {% endif %}
            </div>
          </div>
        </div>
        {% endif %}

        <form method="post"
          action="{% if editing %}
            {% url 'admin_section:core_dia_pro_session_update' editing_session.pk %}
          {% else %}
            {% url 'admin_section:core_dia_pro_session_create' %}
          {% endif %}"
          class="space-y-6" id="sessionForm">
          {% csrf_token %}

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Department Field -->
            <div class="space-y-1">
              <label for="id_department" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Department <span class="text-red-500">*</span>
              </label>
              {{ form.department|add_class:'w-full px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-blue-500'|attr:'id:id_department' }}
              {% if form.department.errors %}
                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.department.errors|join:', ' }}</p>
              {% else %}
                <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Select the department for this session</p>
              {% endif %}
            </div>

            <!-- Activity Type Field -->
            <div class="space-y-1">
              <label for="id_activity_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Activity Type <span class="text-red-500">*</span>
              </label>
              <div class="relative">
                {{ form.activity_type|add_class:'w-full px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-blue-500'|attr:'id:id_activity_type' }}
                <div id="activity-type-loading" class="hidden absolute inset-y-0 right-0 pr-3 pointer-events-none" style="display: none;">
                  <i class="fas fa-spinner fa-spin text-blue-500"></i>
                </div>
              </div>
              {% if form.activity_type.errors %}
                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.activity_type.errors|join:', ' }}</p>
              {% else %}
                <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Select department first, then choose activity type</p>
              {% endif %}
            </div>
          </div>

          <!-- Session Name Field -->
          <div class="space-y-1">
            <label for="id_name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Session Name <span class="text-red-500">*</span>
            </label>
            {{ form.name|add_class:'w-full px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-blue-500'|attr:'id:id_name' }}
            {% if form.name.errors %}
              <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.name.errors|join:', ' }}</p>
            {% else %}
              <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Enter a descriptive name for this session</p>
            {% endif %}
          </div>

          <!-- Form Actions -->
          <div class="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
            {% if editing %}
              <a href="{% url 'admin_section:core_dia_pro_session_list' %}" class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-800 rounded-lg border border-gray-300 dark:border-gray-600 hover:bg-gray-200 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors duration-200">
                <i class="fas fa-times mr-1"></i> Cancel
              </a>
              <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-yellow-500 rounded-lg hover:bg-yellow-600 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 transition-colors duration-200">
                <i class="fas fa-save mr-1"></i> Update Session
              </button>
            {% else %}
              <button type="reset" class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-800 rounded-lg border border-gray-300 dark:border-gray-600 hover:bg-gray-200 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors duration-200">
                <i class="fas fa-undo mr-1"></i> Reset
              </button>
              <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200">
                <i class="fas fa-plus-circle mr-1"></i> Create Session
              </button>
            {% endif %}
          </div>
        </form>
      </div>
    </div>

    <!-- Sessions Table with CRUD -->
    <div class="bg-white dark:bg-gray-800 shadow-lg rounded-lg overflow-hidden border-t-4 border-green-500">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-100 flex items-center">
          <i class="fas fa-list-ul text-green-500 mr-2"></i> Sessions
          <span class="ml-2 px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">{{ core_sessions.paginator.count }}</span>
        </h3>
        {% if search_query %}
        <div class="flex items-center text-sm text-gray-600 dark:text-gray-400">
          <span>Filtered by: </span>
          <span class="ml-1 px-2 py-1 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 rounded-full text-xs font-medium">
            "{{ search_query }}"</span>
          <a href="{% url 'admin_section:core_dia_pro_session_list' %}" class="ml-2 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 flex items-center">
            <i class="fas fa-times-circle mr-1"></i> Clear
          </a>
        </div>
        {% endif %}
      </div>

      <div class="overflow-x-auto">
        <table id="sessions-table" class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead class="bg-gray-50 dark:bg-gray-700">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                <div class="flex items-center">
                  <span>Name</span>
                  <i class="fas fa-sort ml-1 text-gray-400"></i>
                </div>
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                <div class="flex items-center">
                  <span>Department</span>
                  <i class="fas fa-sort ml-1 text-gray-400"></i>
                </div>
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                <div class="flex items-center">
                  <span>Activity Type</span>
                  <i class="fas fa-sort ml-1 text-gray-400"></i>
                </div>
              </th>
              <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            {% for session in core_sessions %}
              <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ session.name }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="px-2 py-1 text-xs rounded-full bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                    {{ session.department.name }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                    {{ session.activity_type.name }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div class="flex justify-end space-x-2">
                    <a href="{% url 'admin_section:core_dia_pro_session_update' session.pk %}"
                       class="p-1.5 bg-blue-50 text-blue-600 rounded hover:bg-blue-100 dark:bg-blue-900/30 dark:text-blue-400 dark:hover:bg-blue-900/50 transition-colors duration-200"
                       title="Edit Session">
                      <i class="fas fa-edit"></i>
                    </a>
                    <form method="post" action="{% url 'admin_section:core_dia_pro_session_delete' session.pk %}" class="inline" onsubmit="return confirm('Are you sure you want to delete this session?');">
                      {% csrf_token %}
                      <button type="submit"
                              class="p-1.5 bg-red-50 text-red-600 rounded hover:bg-red-100 dark:bg-red-900/30 dark:text-red-400 dark:hover:bg-red-900/50 transition-colors duration-200"
                              title="Delete Session">
                        <i class="fas fa-trash"></i>
                      </button>
                    </form>
                  </div>
                </td>
              </tr>
            {% empty %}
              <tr>
                <td colspan="4" class="px-6 py-8 text-center">
                  <div class="flex flex-col items-center justify-center text-gray-500 dark:text-gray-400">
                    {% if search_query %}
                      <i class="fas fa-search text-4xl mb-3"></i>
                      <p class="text-lg font-medium">No matching sessions found</p>
                      <p class="text-sm mt-1">Try adjusting your search criteria</p>
                    {% else %}
                      <i class="fas fa-calendar-times text-4xl mb-3"></i>
                      <p class="text-lg font-medium">No sessions found</p>
                      <p class="text-sm mt-1">Create your first session using the form above</p>
                    {% endif %}
                  </div>
                </td>
              </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      {% if core_sessions.has_other_pages %}
        <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
          <div class="flex flex-col sm:flex-row justify-between items-center">
            <div class="text-sm text-gray-700 dark:text-gray-300 mb-4 sm:mb-0">
              Showing <span class="font-medium">{{ core_sessions.start_index }}</span> to <span class="font-medium">{{ core_sessions.end_index }}</span> of <span class="font-medium">{{ core_sessions.paginator.count }}</span> sessions
            </div>
            <nav class="flex justify-center">
              <ul class="flex items-center space-x-1">
                {% if core_sessions.has_previous %}
                  <li>
                    <a href="?page=1{% if search_query %}&q={{ search_query }}{% endif %}" class="px-2 py-1 rounded-md bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors duration-200" title="First Page">
                      <i class="fas fa-angle-double-left"></i>
                    </a>
                  </li>
                  <li>
                    <a href="?page={{ core_sessions.previous_page_number }}{% if search_query %}&q={{ search_query }}{% endif %}" class="px-3 py-1 rounded-md bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors duration-200">
                      <i class="fas fa-angle-left mr-1"></i> Prev
                    </a>
                  </li>
                {% endif %}

                {% for num in core_sessions.paginator.page_range %}
                  {% if core_sessions.number == num %}
                    <li>
                      <span class="px-3 py-1 rounded-md bg-blue-600 text-white font-medium">
                        {{ num }}
                      </span>
                    </li>
                  {% elif num > core_sessions.number|add:-3 and num < core_sessions.number|add:3 %}
                    <li>
                      <a href="?page={{ num }}{% if search_query %}&q={{ search_query }}{% endif %}" class="px-3 py-1 rounded-md bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors duration-200">
                        {{ num }}
                      </a>
                    </li>
                  {% endif %}
                {% endfor %}

                {% if core_sessions.has_next %}
                  <li>
                    <a href="?page={{ core_sessions.next_page_number }}{% if search_query %}&q={{ search_query }}{% endif %}" class="px-3 py-1 rounded-md bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors duration-200">
                      Next <i class="fas fa-angle-right ml-1"></i>
                    </a>
                  </li>
                  <li>
                    <a href="?page={{ core_sessions.paginator.num_pages }}{% if search_query %}&q={{ search_query }}{% endif %}" class="px-2 py-1 rounded-md bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors duration-200" title="Last Page">
                      <i class="fas fa-angle-double-right"></i>
                    </a>
                  </li>
                {% endif %}
              </ul>
            </nav>
          </div>
        </div>
      {% endif %}
    </div>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function () {
      // Form elements
      const departmentSelect = document.getElementById('id_department');
      const activityTypeSelect = document.getElementById('id_activity_type');
      const sessionNameInput = document.getElementById('id_name');
      const activityTypeLoading = document.getElementById('activity-type-loading');
      const sessionForm = document.getElementById('sessionForm');
      const clearSearchBtn = document.getElementById('clear-search');
      const searchInput = document.getElementById('search-input');
      const closeMessageBtns = document.querySelectorAll('.close-message');

      // Form validation and dynamic loading
      if (departmentSelect && activityTypeSelect && sessionNameInput) {
        // Only disable activity type select in create mode
        if (!document.querySelector('[name="editing"]')) {
          activityTypeSelect.disabled = true;
        }

        // Handle department change
        departmentSelect.addEventListener('change', function () {
          const departmentId = this.value;
          if (!departmentId) {
            activityTypeSelect.innerHTML = '<option value="">Select Activity Type</option>';
            activityTypeSelect.disabled = true;
            return;
          }

          // Show loading indicator
          if (activityTypeLoading) {
            activityTypeLoading.style.display = 'flex';
          }

          // Fetch activity types for selected department
          fetch(`/admin_section/api/activity-types/${departmentId}/`, {
            headers: {
              'X-Requested-With': 'XMLHttpRequest'
            }
          })
          .then(response => {
            if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
            return response.json();
          })
          .then(data => {
            activityTypeSelect.innerHTML = '<option value="">Select Activity Type</option>';
            if (data && Array.isArray(data)) {
              data.forEach(activity => {
                const option = document.createElement('option');
                option.value = activity.id;
                option.text = activity.name;
                activityTypeSelect.appendChild(option);
              });
              activityTypeSelect.disabled = false;
            }
          })
          .catch(error => {
            console.error('Error fetching activity types:', error);
            activityTypeSelect.innerHTML = '<option value="">Error loading activity types</option>';
            activityTypeSelect.disabled = true;
          })
          .finally(() => {
            // Hide loading indicator
            if (activityTypeLoading) {
              activityTypeLoading.style.display = 'none';
            }
          });
        });

        // Form submission validation
        if (sessionForm) {
          sessionForm.addEventListener('submit', function(e) {
            let isValid = true;

            // Validate department
            if (!departmentSelect.value) {
              isValid = false;
              highlightError(departmentSelect, 'Please select a department');
            } else {
              removeHighlight(departmentSelect);
            }

            // Validate activity type
            if (!activityTypeSelect.value) {
              isValid = false;
              highlightError(activityTypeSelect, 'Please select an activity type');
            } else {
              removeHighlight(activityTypeSelect);
            }

            // Validate session name
            if (!sessionNameInput.value.trim()) {
              isValid = false;
              highlightError(sessionNameInput, 'Please enter a session name');
            } else {
              removeHighlight(sessionNameInput);
            }

            if (!isValid) {
              e.preventDefault();
            }
          });
        }

        // Helper functions for form validation
        function highlightError(element, message) {
          element.classList.add('border-red-500', 'focus:ring-red-500', 'focus:border-red-500');

          // Add error message if it doesn't exist
          let errorElement = element.parentNode.querySelector('.error-message');
          if (!errorElement) {
            errorElement = document.createElement('p');
            errorElement.className = 'mt-1 text-sm text-red-600 dark:text-red-400 error-message';
            if (element.parentNode.classList.contains('relative')) {
              element.parentNode.parentNode.appendChild(errorElement);
            } else {
              element.parentNode.appendChild(errorElement);
            }
          }
          errorElement.textContent = message;
        }

        function removeHighlight(element) {
          element.classList.remove('border-red-500', 'focus:ring-red-500', 'focus:border-red-500');

          // Remove error message if it exists
          const errorElement = element.parentNode.querySelector('.error-message') ||
                              (element.parentNode.parentNode && element.parentNode.parentNode.querySelector('.error-message'));
          if (errorElement) {
            errorElement.remove();
          }
        }
      }

      // Clear search functionality
      if (clearSearchBtn && searchInput) {
        clearSearchBtn.addEventListener('click', function() {
          window.location.href = '{% url "admin_section:core_dia_pro_session_list" %}';
        });
      }

      // Close message functionality
      if (closeMessageBtns.length > 0) {
        closeMessageBtns.forEach(btn => {
          btn.addEventListener('click', function() {
            const message = this.closest('.mb-3');
            if (message) {
              message.style.opacity = '0';
              setTimeout(() => {
                message.remove();
              }, 300);
            }
          });
        });

        // Auto-hide messages after 5 seconds
        setTimeout(function() {
          document.querySelectorAll('.fixed.top-4.right-4 .mb-3').forEach(function(message) {
            message.style.opacity = '0';
            setTimeout(function() {
              message.remove();
            }, 300);
          });
        }, 5000);
      }
    });
  </script>
{% endblock %}
