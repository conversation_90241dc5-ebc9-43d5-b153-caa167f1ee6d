{% extends 'base.html' %}

{% block title %}Updates - ElogBook{% endblock %}
{% block navbar %}
  {% include './components/public_navbar.html' %}
{% endblock %}

{% block extra_head %}
  <!-- Preload critical images -->
  <link rel="preload" as="image" href="https://images.unsplash.com/photo-1576091160399-112ba8d25d1d?q=80&w=2070" fetchpriority="high">

  <style>
    @keyframes float {
      0% { transform: translateY(0px); }
      50% { transform: translateY(-10px); }
      100% { transform: translateY(0px); }
    }

    .animate-float {
      animation: float 4s ease-in-out infinite;
    }

    .update-card {
      transition: all 0.3s ease;
    }

    .update-card:hover {
      transform: translateY(-8px);
    }

    .update-card img {
      transition: transform 0.5s ease;
    }

    .update-card:hover img {
      transform: scale(1.05);
    }

    .update-card .action-btn {
      transition: all 0.3s ease;
    }

    .update-card:hover .action-btn {
      transform: translateY(-5px);
    }

    .filter-pill {
      transition: all 0.3s ease;
    }

    .filter-pill:hover {
      transform: translateY(-2px);
    }

    .filter-pill.active {
      background-color: #3b82f6;
      color: white;
    }
  </style>
{% endblock %}

{% block content_container %}
<!-- Hero Section with Enhanced Design -->
<section class="bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-700 dark:from-gray-800 dark:via-gray-700 dark:to-gray-900 py-24 w-full relative overflow-hidden">
    <div class="absolute inset-0 bg-pattern opacity-10"></div>
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
        <h1 class="text-5xl md:text-6xl font-bold text-white animate-fade-in bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-100">Latest Updates</h1>
        <p class="mt-6 text-xl md:text-2xl text-gray-200 animate-fade-in delay-100 max-w-3xl mx-auto">Stay informed with the latest news and announcements from our medical community</p>
    </div>
</section>

{% block content %}
<!-- Updates Section with Enhanced Cards -->
<section class="py-20 w-full bg-gray-50 dark:bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Filter and Search Section -->
        <div class="mb-12 flex flex-col md:flex-row justify-between items-center gap-4">
            <div class="flex flex-wrap items-center gap-4">
                <a href="{% url 'update_page' %}" class="filter-pill {% if not selected_category %}active{% endif %} px-4 py-2 text-sm font-medium {% if not selected_category %}text-blue-600 dark:text-blue-400{% else %}text-gray-500 dark:text-gray-400{% endif %} bg-white dark:bg-gray-800 rounded-lg shadow-sm transition duration-300 ease-in-out transform hover:shadow-md">
                    <span class="flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                        All
                    </span>
                </a>
                {% for cat_value, cat_name in categories %}
                <a href="{% url 'update_page' %}?category={{ cat_value }}" class="filter-pill {% if selected_category == cat_value %}active{% endif %} px-4 py-2 text-sm font-medium {% if selected_category == cat_value %}text-blue-600 dark:text-blue-400{% else %}text-gray-500 dark:text-gray-400{% endif %} bg-white dark:bg-gray-800 rounded-lg shadow-sm transition duration-300 ease-in-out transform hover:shadow-md">
                    <span class="flex items-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            {% if cat_value == 'news' %}
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9.5a2 2 0 00-.586-1.414l-4.5-4.5A2 2 0 0015.5 3H15m4 13a2 2 0 01-2-2V9.5a2 2 0 00-.586-1.414l-4.5-4.5A2 2 0 0015.5 3H15"></path>
                            {% elif cat_value == 'announcement' %}
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z"></path>
                            {% elif cat_value == 'feature' %}
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                            {% else %}
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                            {% endif %}
                        </svg>
                        {{ cat_name }}
                    </span>
                </a>
                {% endfor %}
            </div>
            <div class="relative w-full md:w-64">
                <form method="get" action="{% url 'update_page' %}">
                    {% if selected_category %}<input type="hidden" name="category" value="{{ selected_category }}">{% endif %}
                    <input type="text" name="q" value="{{ search_query }}" placeholder="Search updates..." class="w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 transition duration-150 ease-in-out">
                    <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                        <button type="submit" class="focus:outline-none">
                            <svg class="w-5 h-5 text-gray-400 hover:text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Updates Grid with Enhanced Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {% if blogs %}
                {% for blog in blogs %}
                    <div class="update-card bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden animate-fade-in-up {% if forloop.counter > 1 %}delay-{{ forloop.counter|add:"-1" }}00{% endif %}">
                        <div class="relative group">
                            {% if blog.featured_image %}
                                <img src="{{ blog.featured_image.url }}" alt="{{ blog.title }}" class="w-full h-48 object-cover">
                            {% else %}
                                <div class="w-full h-48 bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                                    <svg class="h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                    </svg>
                                </div>
                            {% endif %}
                            <div class="absolute top-4 left-4 px-3 py-1
                                {% if blog.category == 'news' %}bg-blue-500
                                {% elif blog.category == 'announcement' %}bg-green-500
                                {% elif blog.category == 'feature' %}bg-purple-500
                                {% else %}bg-gray-500{% endif %}
                                text-white text-sm font-medium rounded-full animate-float">
                                {% for cat_value, cat_name in categories %}
                                    {% if cat_value == blog.category %}{{ cat_name }}{% endif %}
                                {% endfor %}
                            </div>
                            <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                        </div>
                        <div class="p-6 relative">
                            <div class="flex items-center mb-4">
                                <span class="text-sm text-gray-500 dark:text-gray-400">{{ blog.created_at|date:"F d, Y" }}</span>
                                <span class="mx-2 text-gray-300 dark:text-gray-600">•</span>
                                <span class="text-sm text-gray-500 dark:text-gray-400">By {{ blog.author.get_full_name|default:blog.author.username }}</span>
                            </div>
                            <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-3 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition duration-300">{{ blog.title }}</h3>
                            <p class="text-gray-600 dark:text-gray-300 mb-4">{{ blog.summary }}</p>
                            <div class="flex items-center justify-between">
                                <a href="{% url 'blog_detail' blog.id %}" class="action-btn inline-flex items-center text-blue-500 dark:text-blue-400 hover:text-blue-600 dark:hover:text-blue-300 transition duration-300 group">
                                    <span>Read More</span>
                                    <svg class="w-4 h-4 ml-2 transform group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                                    </svg>
                                </a>
                                {% if blog.attachment %}
                                <div class="flex items-center">
                                    <a href="{{ blog.attachment.url }}" target="_blank" class="action-btn text-gray-400 hover:text-blue-500 dark:hover:text-blue-400 transition duration-300" title="Download Attachment">
                                        <svg class="w-5 h-5 transform hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
                                        </svg>
                                    </a>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                {% endfor %}
            {% else %}
                <div class="col-span-1 md:col-span-3 py-12 text-center">
                    <svg class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <h3 class="mt-2 text-lg font-medium text-gray-900 dark:text-white">No updates found</h3>
                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                        {% if search_query %}
                            No results found for "{{ search_query }}". Try a different search term or clear filters.
                        {% elif selected_category %}
                            No updates found in this category. Try a different category or view all updates.
                        {% else %}
                            Check back later for updates and announcements.
                        {% endif %}
                    </p>
                    {% if search_query or selected_category %}
                        <div class="mt-6">
                            <a href="{% url 'update_page' %}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                View All Updates
                            </a>
                        </div>
                    {% endif %}
                </div>
            {% endif %}
        </div>

        <!-- Pagination -->
        {% if blogs.has_other_pages %}
        <div class="mt-12 flex justify-center items-center space-x-2">
            {% if blogs.has_previous %}
                <a href="?page={{ blogs.previous_page_number }}{% if search_query %}&q={{ search_query }}{% endif %}{% if selected_category %}&category={{ selected_category }}{% endif %}" class="px-4 py-2 text-sm font-medium text-gray-500 bg-white dark:bg-gray-800 rounded-lg shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 transition duration-150 ease-in-out">
                    Previous
                </a>
            {% else %}
                <span class="px-4 py-2 text-sm font-medium text-gray-400 bg-gray-100 dark:bg-gray-700 dark:text-gray-500 rounded-lg shadow-sm cursor-not-allowed">
                    Previous
                </span>
            {% endif %}

            {% for i in blogs.paginator.page_range %}
                {% if blogs.number == i %}
                    <span class="px-4 py-2 text-sm font-medium text-white bg-blue-500 rounded-lg shadow-sm">
                        {{ i }}
                    </span>
                {% elif i > blogs.number|add:'-3' and i < blogs.number|add:'3' %}
                    <a href="?page={{ i }}{% if search_query %}&q={{ search_query }}{% endif %}{% if selected_category %}&category={{ selected_category }}{% endif %}" class="px-4 py-2 text-sm font-medium text-gray-500 bg-white dark:bg-gray-800 rounded-lg shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 transition duration-150 ease-in-out">
                        {{ i }}
                    </a>
                {% endif %}
            {% endfor %}

            {% if blogs.has_next %}
                <a href="?page={{ blogs.next_page_number }}{% if search_query %}&q={{ search_query }}{% endif %}{% if selected_category %}&category={{ selected_category }}{% endif %}" class="px-4 py-2 text-sm font-medium text-gray-500 bg-white dark:bg-gray-800 rounded-lg shadow-sm hover:bg-gray-50 dark:hover:bg-gray-700 transition duration-150 ease-in-out">
                    Next
                </a>
            {% else %}
                <span class="px-4 py-2 text-sm font-medium text-gray-400 bg-gray-100 dark:bg-gray-700 dark:text-gray-500 rounded-lg shadow-sm cursor-not-allowed">
                    Next
                </span>
            {% endif %}
        </div>
        {% endif %}
    </div>
</section>
{% endblock %}
{% endblock %}