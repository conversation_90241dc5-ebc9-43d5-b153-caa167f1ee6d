{% extends 'base.html' %}

{% block title %}
  {% if is_create %}Create New Blog Post{% else %}Edit Blog Post{% endif %}
{% endblock %}

{% block navbar %}
{% include 'components/admin_auth_navbar.html' %}
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
  <!-- Header -->
  <div class="mb-8">
    <h1 class="text-3xl font-bold text-gray-800 dark:text-white">
      {% if is_create %}Create New Blog Post{% else %}Edit Blog Post{% endif %}
    </h1>
    <p class="mt-2 text-gray-600 dark:text-gray-400">
      {% if is_create %}
        Create a new blog post to share with your users.
      {% else %}
        Update the details of your existing blog post.
      {% endif %}
    </p>
  </div>

  <!-- Form Card -->
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
    <form method="post" enctype="multipart/form-data" class="space-y-6">
      {% csrf_token %}
      
      <!-- Title Field -->
      <div>
        <label for="{{ form.title.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Title</label>
        {{ form.title }}
        {% if form.title.errors %}
          <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.title.errors.0 }}</p>
        {% endif %}
      </div>
      
      <!-- Summary Field -->
      <div>
        <label for="{{ form.summary.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Summary</label>
        {{ form.summary }}
        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">A brief summary that will be displayed in blog listings (max 300 characters).</p>
        {% if form.summary.errors %}
          <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.summary.errors.0 }}</p>
        {% endif %}
      </div>
      
      <!-- Category Field -->
      <div>
        <label for="{{ form.category.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Category</label>
        {{ form.category }}
        {% if form.category.errors %}
          <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.category.errors.0 }}</p>
        {% endif %}
      </div>
      
      <!-- Content Field -->
      <div>
        <label for="{{ form.content.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Content</label>
        {{ form.content }}
        {% if form.content.errors %}
          <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.content.errors.0 }}</p>
        {% endif %}
      </div>
      
      <!-- Featured Image Field -->
      <div>
        <label for="{{ form.featured_image.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Featured Image</label>
        {% if blog.featured_image %}
          <div class="mb-2 flex items-center">
            <img src="{{ blog.featured_image.url }}" alt="Current featured image" class="h-20 w-20 object-cover rounded-md">
            <span class="ml-2 text-sm text-gray-500 dark:text-gray-400">Current image</span>
          </div>
        {% endif %}
        {{ form.featured_image }}
        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Upload an image to be displayed with your blog post.</p>
        {% if form.featured_image.errors %}
          <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.featured_image.errors.0 }}</p>
        {% endif %}
      </div>
      
      <!-- Attachment Field -->
      <div>
        <label for="{{ form.attachment.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Attachment</label>
        {% if blog.attachment %}
          <div class="mb-2 flex items-center">
            <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
            </svg>
            <a href="{{ blog.attachment.url }}" target="_blank" class="ml-2 text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
              {{ blog.get_attachment_name }}
            </a>
          </div>
        {% endif %}
        {{ form.attachment }}
        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Upload a file to attach to your blog post.</p>
        {% if form.attachment.errors %}
          <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.attachment.errors.0 }}</p>
        {% endif %}
      </div>
      
      <!-- Attachment Name Field -->
      <div>
        <label for="{{ form.attachment_name.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Attachment Name</label>
        {{ form.attachment_name }}
        <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Provide a friendly name for the attachment (optional).</p>
        {% if form.attachment_name.errors %}
          <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.attachment_name.errors.0 }}</p>
        {% endif %}
      </div>
      
      <!-- Published Status Field -->
      <div class="flex items-center">
        {{ form.is_published }}
        <label for="{{ form.is_published.id_for_label }}" class="ml-2 block text-sm font-medium text-gray-700 dark:text-gray-300">Publish immediately</label>
        {% if form.is_published.errors %}
          <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.is_published.errors.0 }}</p>
        {% endif %}
      </div>
      
      <!-- Form Actions -->
      <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
        <a href="{% url 'admin_section:admin_blogs' %}" class="px-4 py-2 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition duration-300">
          Cancel
        </a>
        <button type="submit" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition duration-300">
          {% if is_create %}Create Blog Post{% else %}Update Blog Post{% endif %}
        </button>
      </div>
    </form>
  </div>
</div>
{% endblock %}
