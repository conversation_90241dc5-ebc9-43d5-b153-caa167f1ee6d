# Generated by Django 5.1.4 on 2025-03-24 08:51

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('accounts', '0006_alter_doctor_departments_alter_student_group'),
        ('admin_section', '0002_department_logyear_alter_activitytype_department_and_more'),
        ('student_section', '0002_remove_corediaprosession_activity_type_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='StudentLogFormModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('patient_id', models.CharField(blank=True, max_length=4)),
                ('description', models.TextField(blank=True)),
                ('participation_type', models.CharField(choices=[('Observed', 'Observed'), ('Assisted', 'Assisted')], max_length=50)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_reviewed', models.BooleanField(default=False)),
                ('review_date', models.DateTimeField(blank=True, null=True)),
                ('reviewer_comments', models.TextField(blank=True)),
                ('activity_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='admin_section.activitytype')),
                ('core_diagnosis', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='log_forms', to='admin_section.corediaprosession')),
                ('department', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='admin_section.department')),
                ('group', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='admin_section.group')),
                ('log_year', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='admin_section.logyear')),
                ('log_year_section', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='admin_section.logyearsection')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='log_forms', to='accounts.student')),
                ('training_site', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='admin_section.trainingsite')),
                ('tutor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='supervised_logs', to='accounts.doctor')),
            ],
            options={
                'verbose_name': 'Student Log Form',
                'verbose_name_plural': 'Student Log Forms',
                'ordering': ['-date', '-created_at'],
            },
        ),
    ]
