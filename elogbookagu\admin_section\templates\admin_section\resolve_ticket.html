{% extends 'base.html' %}

{% block title %}
  Resolve Support Ticket
{% endblock %}

{% block navbar %}
{% include 'components/admin_auth_navbar.html' %}
{% endblock %}

<!-- Main Content -->

{% block content %}
  <div class="container mx-auto px-4 py-8">
    <div class="mb-6">
      <a href="{% url 'admin_section:admin_support' %}?type={{ ticket_type }}" class="inline-flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
        </svg>
        Back to Support Tickets
      </a>
    </div>

    <!-- Messages section -->
    {% if messages %}
      <div class="mb-6">
        {% for message in messages %}
          <div class="p-4 mb-4 rounded-lg {% if message.tags == 'success' %}bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100{% elif message.tags == 'error' %}bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100{% else %}bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100{% endif %}">
            {{ message }}
          </div>
        {% endfor %}
      </div>
    {% endif %}

    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
      <h2 class="text-2xl font-bold mb-6 text-gray-800 dark:text-white">Resolve Support Ticket</h2>

      <div class="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
        <div class="mb-2">
          <span class="font-semibold text-gray-700 dark:text-gray-300">{% if ticket_type == 'student' %}Student{% else %}Doctor{% endif %}:</span>
          <span class="text-gray-800 dark:text-white">
            {% if ticket_type == 'student' %}
              {{ ticket.student.user.get_full_name }}
            {% else %}
              Dr. {{ ticket.doctor.user.get_full_name }}
            {% endif %}
          </span>
        </div>
        {% if ticket_type == 'student' %}
        <div class="mb-2">
          <span class="font-semibold text-gray-700 dark:text-gray-300">Student ID:</span>
          <span class="text-gray-800 dark:text-white">{{ ticket.student.student_id }}</span>
        </div>
        {% endif %}
        <div class="mb-2">
          <span class="font-semibold text-gray-700 dark:text-gray-300">Subject:</span>
          <span class="text-gray-800 dark:text-white">{{ ticket.subject }}</span>
        </div>
        <div class="mb-2">
          <span class="font-semibold text-gray-700 dark:text-gray-300">Date Submitted:</span>
          <span class="text-gray-800 dark:text-white">{{ ticket.date_created|date:"F d, Y H:i" }}</span>
        </div>
        <div class="mb-2">
          <span class="font-semibold text-gray-700 dark:text-gray-300">Current Status:</span>
          <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {% if ticket.status == 'solved' %}bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100{% else %}bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100{% endif %}">
            {{ ticket.status|title }}
          </span>
        </div>
        <div>
          <span class="font-semibold text-gray-700 dark:text-gray-300">Description:</span>
          <p class="mt-1 text-gray-800 dark:text-white">{{ ticket.description }}</p>
        </div>
      </div>

      <form method="post" class="space-y-4">
        {% csrf_token %}
        <div>
          <label for="id_status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Status</label>
          {{ form.status }}
        </div>
        <div>
          <label for="id_admin_comments" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Your Response</label>
          {{ form.admin_comments }}
        </div>
        <div class="pt-2">
          <button type="submit" class="w-full py-2 px-4 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg shadow-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-75 transition-colors duration-200">
            Submit Response
          </button>
        </div>
      </form>
    </div>
  </div>
{% endblock %}
