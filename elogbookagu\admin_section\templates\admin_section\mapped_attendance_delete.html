{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_head %}
<style>
  .delete-card {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  }
  
  .warning-icon {
    animation: pulse 2s infinite;
  }
  
  @keyframes pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: .5;
    }
  }
</style>
{% endblock %}

{% block navbar %}
  {% include 'components/admin_auth_navbar.html' %}
{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
  <div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
    
    <!-- Header Section -->
    <div class="delete-card rounded-lg p-6 mb-8 text-white">
      <div class="flex items-center">
        <div class="warning-icon">
          <i class="fas fa-exclamation-triangle text-4xl mr-4"></i>
        </div>
        <div>
          <h1 class="text-3xl font-bold">Delete Mapped Attendance</h1>
          <p class="mt-2 text-red-100">
            This action cannot be undone. Please confirm your decision.
          </p>
        </div>
      </div>
    </div>

    <!-- Confirmation Section -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
          <i class="fas fa-trash-alt mr-2 text-red-600"></i>
          Confirm Deletion
        </h3>
      </div>

      <div class="p-6">
        <!-- Warning Message -->
        <div class="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
          <div class="flex">
            <div class="flex-shrink-0">
              <i class="fas fa-exclamation-triangle text-red-400"></i>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-red-800">
                Warning: This action is irreversible
              </h3>
              <div class="mt-2 text-sm text-red-700">
                <p>You are about to delete the following mapped attendance record:</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Mapping Details -->
        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6">
          <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Mapping Details
          </h4>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">
                Mapping Name
              </label>
              <p class="text-lg font-semibold text-gray-900 dark:text-white">
                {{ mapping.name }}
              </p>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">
                Training Site
              </label>
              <p class="text-lg font-semibold text-gray-900 dark:text-white">
                {{ mapping.training_site.name }}
              </p>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">
                Log Year
              </label>
              <p class="text-lg font-semibold text-gray-900 dark:text-white">
                {{ mapping.log_year.year_name }}
              </p>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-500 dark:text-gray-400 mb-1">
                Status
              </label>
              {% if mapping.is_active %}
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  <i class="fas fa-check-circle mr-1"></i>Active
                </span>
              {% else %}
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                  <i class="fas fa-times-circle mr-1"></i>Inactive
                </span>
              {% endif %}
            </div>
          </div>

          <!-- Mapped Items Summary -->
          <div class="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
              <div class="flex items-center">
                <div class="p-2 rounded-full bg-blue-100 dark:bg-blue-900">
                  <i class="fas fa-user-md text-blue-600 dark:text-blue-400"></i>
                </div>
                <div class="ml-3">
                  <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Mapped Doctors</p>
                  <p class="text-xl font-bold text-gray-900 dark:text-white">{{ mapping.doctors.count }}</p>
                </div>
              </div>
            </div>
            
            <div class="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-600">
              <div class="flex items-center">
                <div class="p-2 rounded-full bg-green-100 dark:bg-green-900">
                  <i class="fas fa-users text-green-600 dark:text-green-400"></i>
                </div>
                <div class="ml-3">
                  <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Mapped Groups</p>
                  <p class="text-xl font-bold text-gray-900 dark:text-white">{{ mapping.groups.count }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Consequences -->
        <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-6">
          <div class="flex">
            <div class="flex-shrink-0">
              <i class="fas fa-info-circle text-yellow-400"></i>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-yellow-800">
                What will happen when you delete this mapping:
              </h3>
              <div class="mt-2 text-sm text-yellow-700">
                <ul class="list-disc list-inside space-y-1">
                  <li>The mapping between doctors and this training site will be removed</li>
                  <li>The mapping between student groups and this training site will be removed</li>
                  <li>This action will not affect the doctors, groups, or training site themselves</li>
                  <li>Any attendance records based on this mapping may need to be reviewed</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <!-- Confirmation Form -->
        <form method="post" class="space-y-6">
          {% csrf_token %}
          
          <!-- Confirmation Checkbox -->
          <div class="flex items-start">
            <div class="flex items-center h-5">
              <input id="confirm-delete" name="confirm_delete" type="checkbox" required
                     class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded">
            </div>
            <div class="ml-3 text-sm">
              <label for="confirm-delete" class="font-medium text-gray-700 dark:text-gray-300">
                I understand that this action cannot be undone and I want to delete this mapped attendance record.
              </label>
            </div>
          </div>

          <!-- Form Actions -->
          <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200 dark:border-gray-700">
            <a href="{% url 'admin_section:mapped_attendance_list' %}" 
               class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-600 transition-colors duration-200">
              <i class="fas fa-times mr-2"></i>
              Cancel
            </a>
            <a href="{% url 'admin_section:mapped_attendance_detail' mapping.pk %}" 
               class="px-4 py-2 text-sm font-medium text-blue-700 bg-blue-100 border border-blue-300 rounded-md hover:bg-blue-200 dark:bg-blue-900 dark:text-blue-300 dark:border-blue-600 dark:hover:bg-blue-800 transition-colors duration-200">
              <i class="fas fa-eye mr-2"></i>
              View Details
            </a>
            <button type="submit" 
                    class="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200">
              <i class="fas fa-trash-alt mr-2"></i>
              Delete Mapping
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const deleteButton = form.querySelector('button[type="submit"]');
    const confirmCheckbox = document.getElementById('confirm-delete');
    
    // Initially disable the delete button
    deleteButton.disabled = true;
    deleteButton.classList.add('opacity-50', 'cursor-not-allowed');
    
    // Enable/disable delete button based on checkbox
    confirmCheckbox.addEventListener('change', function() {
        if (this.checked) {
            deleteButton.disabled = false;
            deleteButton.classList.remove('opacity-50', 'cursor-not-allowed');
        } else {
            deleteButton.disabled = true;
            deleteButton.classList.add('opacity-50', 'cursor-not-allowed');
        }
    });
    
    // Add confirmation dialog
    form.addEventListener('submit', function(e) {
        if (!confirm('Are you absolutely sure you want to delete this mapped attendance record? This action cannot be undone.')) {
            e.preventDefault();
        }
    });
});
</script>
{% endblock %}
