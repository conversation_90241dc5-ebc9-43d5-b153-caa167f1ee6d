# Generated by Django 5.1.4 on 2025-03-15 17:57

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('admin_section', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Department',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50)),
            ],
        ),
        migrations.CreateModel(
            name='LogYear',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('year_name', models.Char<PERSON>ield(max_length=20, unique=True)),
            ],
        ),
        migrations.AlterField(
            model_name='activitytype',
            name='department',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='activity_types', to='admin_section.department'),
        ),
        migrations.Alter<PERSON>ield(
            model_name='corediaprosession',
            name='department',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='core_dia_pro_sessions', to='admin_section.department'),
        ),
        migrations.AddField(
            model_name='department',
            name='log_year',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='department_log_year', to='admin_section.logyear'),
        ),
        migrations.CreateModel(
            name='LogYearSection',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('year_section_name', models.CharField(max_length=20)),
                ('is_deleted', models.BooleanField(default=False)),
                ('year_name', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='log_year_sections', to='admin_section.logyear')),
            ],
        ),
        migrations.CreateModel(
            name='Group',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('group_name', models.CharField(max_length=50)),
                ('log_year', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='groups_log_year', to='admin_section.logyear')),
                ('log_year_section', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='groups_log_year_section', to='admin_section.logyearsection')),
            ],
        ),
        migrations.AddField(
            model_name='department',
            name='log_year_section',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='department_log_year_section', to='admin_section.logyearsection'),
        ),
        migrations.CreateModel(
            name='TrainingSite',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('log_year', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='training_sites', to='admin_section.logyear')),
            ],
        ),
    ]
