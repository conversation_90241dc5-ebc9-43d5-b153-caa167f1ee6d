{% extends 'base.html' %}

{% block title %}
  {{ blog.title }}
{% endblock %}

{% block navbar %}
{% include 'components/admin_auth_navbar.html' %}
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
  <!-- Back Button and Actions -->
  <div class="flex flex-wrap justify-between items-center mb-6">
    <a href="{% url 'admin_section:admin_blogs' %}" class="inline-flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 mb-4 md:mb-0">
      <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
      </svg>
      Back to Blogs
    </a>
    
    <div class="flex space-x-3">
      <a href="{% url 'admin_section:blog_edit' blog.id %}" class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition duration-300">
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
        </svg>
        Edit
      </a>
      <a href="{% url 'admin_section:blog_delete' blog.id %}" class="inline-flex items-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition duration-300">
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
        </svg>
        Delete
      </a>
    </div>
  </div>

  <!-- Blog Content Card -->
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
    <!-- Featured Image -->
    {% if blog.featured_image %}
      <div class="w-full h-64 md:h-80 bg-gray-200 dark:bg-gray-700">
        <img src="{{ blog.featured_image.url }}" alt="{{ blog.title }}" class="w-full h-full object-cover">
      </div>
    {% endif %}
    
    <!-- Content -->
    <div class="p-6">
      <!-- Header -->
      <div class="mb-6">
        <div class="flex flex-wrap items-center mb-2">
          <span class="px-2 py-1 text-xs font-semibold rounded-full 
            {% if blog.category == 'news' %}bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-300
            {% elif blog.category == 'announcement' %}bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-300
            {% elif blog.category == 'feature' %}bg-purple-100 text-purple-800 dark:bg-purple-900/50 dark:text-purple-300
            {% else %}bg-gray-100 text-gray-800 dark:bg-gray-900/50 dark:text-gray-300{% endif %}">
            {% for cat_value, cat_name in blog.CATEGORY_CHOICES %}
              {% if cat_value == blog.category %}{{ cat_name }}{% endif %}
            {% endfor %}
          </span>
          
          <span class="mx-2 text-gray-400">•</span>
          
          <span class="text-sm text-gray-500 dark:text-gray-400">
            {{ blog.created_at|date:"F d, Y" }}
          </span>
          
          <span class="mx-2 text-gray-400">•</span>
          
          <span class="text-sm text-gray-500 dark:text-gray-400">
            By {{ blog.author.get_full_name|default:blog.author.username }}
          </span>
          
          <span class="ml-auto">
            {% if blog.is_published %}
              <span class="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-300">
                Published
              </span>
            {% else %}
              <span class="px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800 dark:bg-gray-900/50 dark:text-gray-300">
                Draft
              </span>
            {% endif %}
          </span>
        </div>
        
        <h1 class="text-3xl font-bold text-gray-800 dark:text-white">{{ blog.title }}</h1>
        <p class="mt-2 text-gray-600 dark:text-gray-400">{{ blog.summary }}</p>
      </div>
      
      <!-- Main Content -->
      <div class="prose prose-blue max-w-none dark:prose-invert dark:text-gray-300">
        {{ blog.content|linebreaks }}
      </div>
      
      <!-- Attachment -->
      {% if blog.attachment %}
        <div class="mt-8 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
          <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-2">Attachment</h3>
          <div class="flex items-center">
            <svg class="h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"></path>
            </svg>
            <div class="ml-3">
              <a href="{{ blog.attachment.url }}" target="_blank" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 font-medium">
                {{ blog.get_attachment_name }}
              </a>
              <p class="text-sm text-gray-500 dark:text-gray-400">Click to download</p>
            </div>
          </div>
        </div>
      {% endif %}
      
      <!-- Metadata -->
      <div class="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
        <div class="flex flex-wrap justify-between text-sm text-gray-500 dark:text-gray-400">
          <div>
            <span>Created: {{ blog.created_at|date:"F d, Y H:i" }}</span>
          </div>
          <div>
            <span>Last updated: {{ blog.updated_at|date:"F d, Y H:i" }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
