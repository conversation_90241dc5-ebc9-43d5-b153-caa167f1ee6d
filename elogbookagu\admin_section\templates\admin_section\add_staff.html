{% extends 'base.html' %}

{% block title %}
  Add Staff
{% endblock %}

{% block navbar %}
  {% include 'components/admin_auth_navbar.html' %}
{% endblock %}

{% block content %}
  <div class="container mx-auto px-4 py-8">
    <div class="flex flex-col md:flex-row justify-between items-center mb-8">
      <h1 class="text-3xl font-bold text-gray-800 dark:text-white mb-4 md:mb-0">Staff Management</h1>
      <div class="flex space-x-2">
        <a href="#add-staff-form" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200 flex items-center">
          <i class="fas fa-user-plus mr-2"></i> Add Staff
        </a>
        <a href="#bulk-upload-form" class="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors duration-200 flex items-center">
          <i class="fas fa-file-upload mr-2"></i> Bulk Upload
        </a>
        <a href="#assign-staff-form" class="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors duration-200 flex items-center">
          <i class="fas fa-user-tag mr-2"></i> Assign Staff
        </a>
      </div>
    </div>

    <!-- Messages -->
    {% if messages %}
      <div class="mb-6">
        {% for message in messages %}
          <div class="p-4 mb-4 rounded-lg flex items-start {% if message.tags == 'success' %}bg-green-100 text-green-800 dark:bg-green-800/50 dark:text-green-100{% elif message.tags == 'error' %}bg-red-100 text-red-800 dark:bg-red-800/50 dark:text-red-100{% else %}bg-blue-100 text-blue-800 dark:bg-blue-800/50 dark:text-blue-100{% endif %}">
            <div class="mr-3 mt-0.5">
              {% if message.tags == 'success' %}
                <i class="fas fa-check-circle"></i>
              {% elif message.tags == 'error' %}
                <i class="fas fa-exclamation-circle"></i>
              {% else %}
                <i class="fas fa-info-circle"></i>
              {% endif %}
            </div>
            <div>
              {{ message }}
            </div>
            <button type="button" class="ml-auto -mx-1.5 -my-1.5 rounded-lg focus:ring-2 p-1.5 inline-flex h-8 w-8 hover:bg-gray-200 dark:hover:bg-gray-700" onclick="this.parentElement.remove()">
              <span class="sr-only">Close</span>
              <i class="fas fa-times"></i>
            </button>
          </div>
        {% endfor %}
      </div>
    {% endif %}

    <!-- Bulk Upload Results -->
    {% if bulk_results %}
      <div class="mb-6 p-4 bg-white dark:bg-gray-800 rounded-lg shadow-md">
        <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-2">Bulk Upload Results</h2>
        <div class="grid grid-cols-3 gap-4 mb-4">
          <div class="bg-blue-100 dark:bg-blue-900/50 p-3 rounded-lg text-center">
            <p class="text-sm text-blue-800 dark:text-blue-200">Total Processed</p>
            <p class="text-2xl font-bold text-blue-800 dark:text-blue-200">{{ bulk_results.total }}</p>
          </div>
          <div class="bg-green-100 dark:bg-green-900/50 p-3 rounded-lg text-center">
            <p class="text-sm text-green-800 dark:text-green-200">Successfully Added</p>
            <p class="text-2xl font-bold text-green-800 dark:text-green-200">{{ bulk_results.success }}</p>
          </div>
          <div class="bg-red-100 dark:bg-red-900/50 p-3 rounded-lg text-center">
            <p class="text-sm text-red-800 dark:text-red-200">Failed</p>
            <p class="text-2xl font-bold text-red-800 dark:text-red-200">{{ bulk_results.failed }}</p>
          </div>
        </div>
        {% if bulk_results.errors %}
          <div class="mt-4">
            <h3 class="text-lg font-medium text-gray-800 dark:text-white mb-2">Errors</h3>
            <div class="bg-red-50 dark:bg-red-900/30 p-3 rounded-lg max-h-40 overflow-y-auto">
              <ul class="list-disc list-inside text-sm text-red-800 dark:text-red-200">
                {% for error in bulk_results.errors %}
                  <li>{{ error }}</li>
                {% endfor %}
              </ul>
            </div>
          </div>
        {% endif %}
      </div>
    {% endif %}

    <!-- Staff List -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-8">
      <div class="flex flex-col md:flex-row justify-between items-center mb-6">
        <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-4 md:mb-0">Staff List</h2>
        
        <!-- Filters -->
        <div class="w-full md:w-auto">
          <form method="get" class="flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-2">
            <div class="flex-grow">
              <input type="text" name="q" value="{{ search_query }}" placeholder="Search staff..." class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
            </div>
            <div>
              <select name="department" class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                <option value="">All Departments</option>
                {% for department in departments %}
                  <option value="{{ department.id }}" {% if selected_department == department.id|stringformat:"i" %}selected{% endif %}>{{ department.name }}</option>
                {% endfor %}
              </select>
            </div>
            <div class="flex space-x-2">
              <button type="submit" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200">
                <i class="fas fa-search mr-2"></i> Filter
              </button>
              <a href="{% url 'admin_section:add_staff' %}" class="px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg transition-colors duration-200">
                <i class="fas fa-times mr-2"></i> Clear
              </a>
            </div>
          </form>
        </div>
      </div>

      <!-- Staff Table -->
      {% if staff_members %}
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Name</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Email</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Departments</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {% for staff in staff_members %}
                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="flex-shrink-0 h-10 w-10">
                        {% if staff.user.profile_photo %}
                          <img class="h-10 w-10 rounded-full object-cover" src="{{ staff.user.profile_photo.url }}" alt="{{ staff.user.get_full_name }}">
                        {% else %}
                          <div class="h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                            <span class="text-blue-600 dark:text-blue-300 font-medium">{{ staff.user.first_name|first }}{{ staff.user.last_name|first }}</span>
                          </div>
                        {% endif %}
                      </div>
                      <div class="ml-4">
                        <div class="text-sm font-medium text-gray-900 dark:text-white">{{ staff.user.get_full_name }}</div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">@{{ staff.user.username }}</div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900 dark:text-white">{{ staff.user.email }}</div>
                  </td>
                  <td class="px-6 py-4">
                    <div class="flex flex-wrap gap-2">
                      {% for department in staff.departments.all %}
                        <div class="group relative">
                          <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                            {{ department.name }}
                            <a href="{% url 'admin_section:remove_staff_from_department' staff.id department.id %}" class="ml-1 text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300" onclick="return confirm('Are you sure you want to remove this staff from {{ department.name }}?')">
                              <i class="fas fa-times-circle"></i>
                            </a>
                          </span>
                        </div>
                      {% empty %}
                        <span class="text-sm text-gray-500 dark:text-gray-400">No departments assigned</span>
                      {% endfor %}
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div class="flex space-x-2">
                      <a href="{% url 'admin_section:edit_staff' staff.id %}" class="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300">
                        <i class="fas fa-edit"></i> Edit
                      </a>
                      <a href="{% url 'admin_section:delete_staff' staff.id %}" class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300">
                        <i class="fas fa-trash-alt"></i> Delete
                      </a>
                    </div>
                  </td>
                </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        {% if staff_members.has_other_pages %}
          <div class="flex justify-center mt-6">
            <nav class="inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
              {% if staff_members.has_previous %}
                <a href="?page={{ staff_members.previous_page_number }}{% if search_query %}&q={{ search_query }}{% endif %}{% if selected_department %}&department={{ selected_department }}{% endif %}" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white dark:bg-gray-800 dark:border-gray-600 text-sm font-medium text-gray-500 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">
                  <span class="sr-only">Previous</span>
                  <i class="fas fa-chevron-left"></i>
                </a>
              {% else %}
                <span class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-gray-100 dark:bg-gray-700 dark:border-gray-600 text-sm font-medium text-gray-400 dark:text-gray-500">
                  <span class="sr-only">Previous</span>
                  <i class="fas fa-chevron-left"></i>
                </span>
              {% endif %}

              {% for i in staff_members.paginator.page_range %}
                {% if staff_members.number == i %}
                  <span class="relative inline-flex items-center px-4 py-2 border border-blue-500 bg-blue-50 dark:bg-blue-900/50 dark:border-blue-600 text-sm font-medium text-blue-600 dark:text-blue-300">{{ i }}</span>
                {% else %}
                  <a href="?page={{ i }}{% if search_query %}&q={{ search_query }}{% endif %}{% if selected_department %}&department={{ selected_department }}{% endif %}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white dark:bg-gray-800 dark:border-gray-600 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">{{ i }}</a>
                {% endif %}
              {% endfor %}

              {% if staff_members.has_next %}
                <a href="?page={{ staff_members.next_page_number }}{% if search_query %}&q={{ search_query }}{% endif %}{% if selected_department %}&department={{ selected_department }}{% endif %}" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white dark:bg-gray-800 dark:border-gray-600 text-sm font-medium text-gray-500 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">
                  <span class="sr-only">Next</span>
                  <i class="fas fa-chevron-right"></i>
                </a>
              {% else %}
                <span class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-gray-100 dark:bg-gray-700 dark:border-gray-600 text-sm font-medium text-gray-400 dark:text-gray-500">
                  <span class="sr-only">Next</span>
                  <i class="fas fa-chevron-right"></i>
                </span>
              {% endif %}
            </nav>
          </div>
        {% endif %}
      {% else %}
        <div class="text-center py-12 border-2 border-dashed border-gray-300 dark:border-gray-700 rounded-lg">
          <div class="text-gray-400 dark:text-gray-500 mb-3">
            <i class="fas fa-users text-5xl"></i>
          </div>
          <p class="text-gray-500 dark:text-gray-400 mb-2">No staff members found.</p>
          <p class="text-sm text-gray-400 dark:text-gray-500">Add staff members using the form below or clear your filters.</p>
        </div>
      {% endif %}
    </div>

    <!-- Forms Section -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
      <!-- Add Staff Form -->
      <div id="add-staff-form" class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-6 flex items-center">
          <i class="fas fa-user-plus text-blue-500 mr-2"></i> Add New Staff
        </h2>
        <form method="post" class="space-y-4">
          {% csrf_token %}
          <div>
            <label for="{{ user_form.username.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Username <span class="text-red-500">*</span></label>
            {{ user_form.username }}
            {% if user_form.username.errors %}
              <p class="text-red-500 text-xs mt-1">{{ user_form.username.errors.0 }}</p>
            {% endif %}
          </div>
          <div>
            <label for="{{ user_form.email.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Email <span class="text-red-500">*</span></label>
            {{ user_form.email }}
            {% if user_form.email.errors %}
              <p class="text-red-500 text-xs mt-1">{{ user_form.email.errors.0 }}</p>
            {% endif %}
          </div>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label for="{{ user_form.first_name.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">First Name <span class="text-red-500">*</span></label>
              {{ user_form.first_name }}
              {% if user_form.first_name.errors %}
                <p class="text-red-500 text-xs mt-1">{{ user_form.first_name.errors.0 }}</p>
              {% endif %}
            </div>
            <div>
              <label for="{{ user_form.last_name.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Last Name <span class="text-red-500">*</span></label>
              {{ user_form.last_name }}
              {% if user_form.last_name.errors %}
                <p class="text-red-500 text-xs mt-1">{{ user_form.last_name.errors.0 }}</p>
              {% endif %}
            </div>
          </div>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label for="{{ user_form.password1.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Password <span class="text-red-500">*</span></label>
              {{ user_form.password1 }}
              {% if user_form.password1.errors %}
                <p class="text-red-500 text-xs mt-1">{{ user_form.password1.errors.0 }}</p>
              {% endif %}
            </div>
            <div>
              <label for="{{ user_form.password2.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Confirm Password <span class="text-red-500">*</span></label>
              {{ user_form.password2 }}
              {% if user_form.password2.errors %}
                <p class="text-red-500 text-xs mt-1">{{ user_form.password2.errors.0 }}</p>
              {% endif %}
            </div>
          </div>
          <div>
            <label for="{{ user_form.phone_no.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Phone Number</label>
            {{ user_form.phone_no }}
            {% if user_form.phone_no.errors %}
              <p class="text-red-500 text-xs mt-1">{{ user_form.phone_no.errors.0 }}</p>
            {% endif %}
          </div>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label for="{{ user_form.city.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">City</label>
              {{ user_form.city }}
              {% if user_form.city.errors %}
                <p class="text-red-500 text-xs mt-1">{{ user_form.city.errors.0 }}</p>
              {% endif %}
            </div>
            <div>
              <label for="{{ user_form.country.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Country</label>
              {{ user_form.country }}
              {% if user_form.country.errors %}
                <p class="text-red-500 text-xs mt-1">{{ user_form.country.errors.0 }}</p>
              {% endif %}
            </div>
          </div>
          <div>
            <label for="{{ user_form.bio.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Bio</label>
            {{ user_form.bio }}
            {% if user_form.bio.errors %}
              <p class="text-red-500 text-xs mt-1">{{ user_form.bio.errors.0 }}</p>
            {% endif %}
          </div>
          <div>
            <label for="{{ staff_form.departments.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Departments</label>
            {{ staff_form.departments }}
            {% if staff_form.departments.errors %}
              <p class="text-red-500 text-xs mt-1">{{ staff_form.departments.errors.0 }}</p>
            {% endif %}
            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Hold Ctrl/Cmd to select multiple departments</p>
          </div>
          <div class="pt-4">
            <button type="submit" name="add_staff" class="w-full py-2 px-4 bg-blue-600 hover:bg-blue-700 text-white font-semibold rounded-lg shadow-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-75 transition-all duration-200">
              <i class="fas fa-user-plus mr-2"></i> Add Staff
            </button>
          </div>
        </form>
      </div>

      <!-- Bulk Upload Form -->
      <div id="bulk-upload-form" class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-6 flex items-center">
          <i class="fas fa-file-upload text-green-500 mr-2"></i> Bulk Upload Staff
        </h2>
        <form method="post" enctype="multipart/form-data" class="space-y-4">
          {% csrf_token %}
          <div>
            <label for="{{ bulk_form.csv_file.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">CSV File <span class="text-red-500">*</span></label>
            {{ bulk_form.csv_file }}
            {% if bulk_form.csv_file.errors %}
              <p class="text-red-500 text-xs mt-1">{{ bulk_form.csv_file.errors.0 }}</p>
            {% endif %}
            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">{{ bulk_form.csv_file.help_text }}</p>
          </div>
          <div>
            <label for="{{ bulk_form.department.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Department <span class="text-red-500">*</span></label>
            {{ bulk_form.department }}
            {% if bulk_form.department.errors %}
              <p class="text-red-500 text-xs mt-1">{{ bulk_form.department.errors.0 }}</p>
            {% endif %}
            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">{{ bulk_form.department.help_text }}</p>
          </div>
          <div class="pt-4">
            <button type="submit" name="bulk_upload" class="w-full py-2 px-4 bg-green-600 hover:bg-green-700 text-white font-semibold rounded-lg shadow-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-75 transition-all duration-200">
              <i class="fas fa-file-upload mr-2"></i> Upload Staff
            </button>
          </div>
          <div class="text-center mt-4">
            <a href="{% url 'admin_section:download_staff_sample_csv' %}" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm">
              <i class="fas fa-download mr-1"></i> Download Sample CSV
            </a>
          </div>
        </form>
      </div>

      <!-- Assign Staff Form -->
      <div id="assign-staff-form" class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
        <h2 class="text-xl font-semibold text-gray-800 dark:text-white mb-6 flex items-center">
          <i class="fas fa-user-tag text-purple-500 mr-2"></i> Assign Staff to Department
        </h2>
        <form method="post" class="space-y-4">
          {% csrf_token %}
          <div>
            <label for="{{ assign_form.staff.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Staff <span class="text-red-500">*</span></label>
            {{ assign_form.staff }}
            {% if assign_form.staff.errors %}
              <p class="text-red-500 text-xs mt-1">{{ assign_form.staff.errors.0 }}</p>
            {% endif %}
          </div>
          <div>
            <label for="{{ assign_form.department.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Department <span class="text-red-500">*</span></label>
            {{ assign_form.department }}
            {% if assign_form.department.errors %}
              <p class="text-red-500 text-xs mt-1">{{ assign_form.department.errors.0 }}</p>
            {% endif %}
          </div>
          <div class="pt-4">
            <button type="submit" name="assign_staff" class="w-full py-2 px-4 bg-purple-600 hover:bg-purple-700 text-white font-semibold rounded-lg shadow-md focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-opacity-75 transition-all duration-200">
              <i class="fas fa-user-tag mr-2"></i> Assign Staff
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
{% endblock %}
