{% extends 'base.html' %}

{% block title %}
  Notifications
{% endblock %}

{% block navbar %}
  {% include './components/doc_auth_navbar.html' %}
{% endblock %}

{% block content %}
  <div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
      <h2 class="text-2xl font-bold text-gray-800 dark:text-white">Notifications</h2>
      
      {% if unread_count > 0 %}
        <a href="?mark_all_read=true" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
          Mark all as read
        </a>
      {% endif %}
    </div>
    
    {% if notifications %}
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
        <ul class="divide-y divide-gray-200 dark:divide-gray-700">
          {% for notification in notifications %}
            <li class="p-4 {% if not notification.is_read %}bg-blue-50 dark:bg-blue-900/20{% endif %} transition-colors duration-200">
              <div class="flex justify-between items-start">
                <div>
                  <h3 class="text-lg font-semibold text-gray-800 dark:text-white">
                    {{ notification.title }}
                    {% if not notification.is_read %}
                      <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100">
                        New
                      </span>
                    {% endif %}
                  </h3>
                  <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">{{ notification.message }}</p>
                  <p class="text-xs text-gray-500 dark:text-gray-400 mt-2">{{ notification.created_at|date:"F j, Y, g:i a" }}</p>
                  
                  {% if notification.log_entry %}
                    <a href="{% url 'doctor_section:review_log' notification.log_entry.id %}" class="mt-2 inline-flex items-center text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
                      <span>View Log</span>
                      <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                      </svg>
                    </a>
                  {% endif %}
                </div>
                
                {% if not notification.is_read %}
                  <a href="?mark_read={{ notification.id }}" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
                    <span class="sr-only">Mark as read</span>
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                  </a>
                {% endif %}
              </div>
            </li>
          {% endfor %}
        </ul>
      </div>
      
      <!-- Pagination -->
      {% if notifications.has_other_pages %}
        <div class="flex justify-center mt-6">
          <nav class="inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
            {% if notifications.has_previous %}
              <a href="?page={{ notifications.previous_page_number }}" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-sm font-medium text-gray-500 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">
                <span class="sr-only">Previous</span>
                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
              </a>
            {% endif %}
            
            {% for i in notifications.paginator.page_range %}
              {% if notifications.number == i %}
                <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 bg-blue-50 dark:bg-blue-900 text-sm font-medium text-blue-600 dark:text-blue-200">
                  {{ i }}
                </span>
              {% elif i > notifications.number|add:'-3' and i < notifications.number|add:'3' %}
                <a href="?page={{ i }}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">
                  {{ i }}
                </a>
              {% endif %}
            {% endfor %}
            
            {% if notifications.has_next %}
              <a href="?page={{ notifications.next_page_number }}" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-sm font-medium text-gray-500 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700">
                <span class="sr-only">Next</span>
                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                </svg>
              </a>
            {% endif %}
          </nav>
        </div>
      {% endif %}
    {% else %}
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 text-center">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"></path>
        </svg>
        <h3 class="mt-2 text-lg font-medium text-gray-900 dark:text-white">No notifications</h3>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">You don't have any notifications at the moment.</p>
      </div>
    {% endif %}
  </div>
{% endblock %}
