{% extends 'base.html' %}

{% load widget_tweaks %}

{% block title %}
    {% if editing %}Edit{% else %}Add{% endif %} Activity Type
{% endblock %}

{% block navbar %}
{% include 'components/admin_auth_navbar.html' %}
{% endblock %}

{% block content %}
    <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-10 lg:py-12">
        <div class="max-w-6xl mx-auto">
            <!-- Page Header -->
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
                <div>
                    <h2 class="text-2xl sm:text-3xl font-bold text-gray-800 dark:text-gray-100">
                        {% if editing %}Edit{% else %}Manage{% endif %} Activity Types
                    </h2>
                    <p class="text-gray-600 dark:text-gray-400 mt-1">{% if editing %}Update existing{% else %}Create and manage{% endif %} activity types for departments</p>
                </div>
                {% if editing %}
                <a href="{% url 'admin_section:add_activity_type' %}" class="flex items-center px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600 transition-colors duration-200">
                    <i class="fas fa-arrow-left mr-2"></i> Back to Activity Types
                </a>
                {% endif %}
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Form Section -->
                <div class="lg:col-span-1">
                    <div class="bg-white dark:bg-gray-800 shadow-lg rounded-lg p-6 border-t-4 {% if editing %}border-yellow-500{% else %}border-blue-500{% endif %}">
                        <h3 class="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-100">
                            {% if editing %}Edit{% else %}Add New{% endif %} Activity Type
                        </h3>

                        <!-- Form Errors Summary -->
                        {% if form.errors or form.non_field_errors %}
                        <div class="mb-6 p-4 bg-red-50 dark:bg-red-900/20 rounded-lg border-l-4 border-red-500">
                            <div class="flex items-start">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-exclamation-circle text-red-600 dark:text-red-400"></i>
                                </div>
                                <div class="ml-3">
                                    <h4 class="text-sm font-medium text-red-800 dark:text-red-300">Please correct the following errors:</h4>
                                    {% if form.non_field_errors %}
                                    <ul class="mt-1 text-sm text-red-700 dark:text-red-300 list-disc list-inside">
                                        {% for error in form.non_field_errors %}
                                        <li>{{ error }}</li>
                                        {% endfor %}
                                    </ul>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        {% endif %}

                        <form method="post" class="space-y-6">
                            {% csrf_token %}

                            <!-- Activity Type Name Field -->
                            <div class="space-y-1">
                                <label for="{{ form.name.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Activity Type Name <span class="text-red-500">*</span></label>
                                {{ form.name|add_class:"w-full px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white" }}
                                {% if form.name.errors %}
                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.name.errors|join:', ' }}</p>
                                {% else %}
                                <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Enter a descriptive name for this activity type</p>
                                {% endif %}
                            </div>

                            <!-- Department Field -->
                            <div class="space-y-1">
                                <label for="{{ form.department.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Department <span class="text-red-500">*</span></label>
                                {{ form.department|add_class:"w-full px-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white dark:bg-gray-700 text-gray-900 dark:text-white" }}
                                {% if form.department.errors %}
                                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{{ form.department.errors|join:', ' }}</p>
                                {% else %}
                                <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Select the department this activity type belongs to</p>
                                {% endif %}
                            </div>

                            <!-- Form Actions -->
                            <div class="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
                                {% if editing %}
                                <a href="{% url 'admin_section:add_activity_type' %}" class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-800 rounded-lg border border-gray-300 dark:border-gray-600 hover:bg-gray-200 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors duration-200">
                                    <i class="fas fa-times mr-1"></i> Cancel
                                </a>
                                <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-yellow-500 rounded-lg hover:bg-yellow-600 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 transition-colors duration-200">
                                    <i class="fas fa-save mr-1"></i> Update Activity Type
                                </button>
                                {% else %}
                                <button type="reset" class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-800 rounded-lg border border-gray-300 dark:border-gray-600 hover:bg-gray-200 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors duration-200">
                                    <i class="fas fa-undo mr-1"></i> Reset
                                </button>
                                <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200">
                                    <i class="fas fa-plus-circle mr-1"></i> Add Activity Type
                                </button>
                                {% endif %}
                            </div>
                        </form>
                    </div>

                    <!-- Messages -->
                    {% if messages %}
                    <div class="mt-4">
                        {% for message in messages %}
                        <div class="p-4 rounded-lg flex items-start {% if message.tags == 'success' %}bg-green-50 text-green-800 dark:bg-green-900/20 dark:text-green-300{% else %}bg-red-50 text-red-800 dark:bg-red-900/20 dark:text-red-300{% endif %}">
                            <div class="flex-shrink-0">
                                <i class="fas {% if message.tags == 'success' %}fa-check-circle text-green-500{% else %}fa-exclamation-circle text-red-500{% endif %} mt-0.5"></i>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium">{{ message }}</p>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}
                </div>

            <!-- Messages -->
            {% if messages %}
                <div class="mt-4">
                    {% for message in messages %}
                        <div class="p-4 rounded-md {% if message.tags == 'success' %}bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-300{% else %}bg-red-100 text-red-700 dark:bg-red-900/20 dark:text-red-300{% endif %}">
                            {{ message }}
                        </div>
                    {% endfor %}
                </div>
            {% endif %}

                <!-- Activity Types Table Section -->
                <div class="lg:col-span-2">
                    <div class="bg-white dark:bg-gray-800 shadow-lg rounded-lg overflow-hidden border-t-4 border-green-500">
                        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                            <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-100 flex items-center">
                                <i class="fas fa-list-ul text-green-500 mr-2"></i> Activity Types
                                <span class="ml-2 px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">{{ activity_types.paginator.count }}</span>
                            </h3>
                            <div class="relative w-full sm:w-64">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-search text-gray-400"></i>
                                </div>
                                <input type="text" id="table-search" class="w-full pl-10 pr-4 py-2 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:outline-none" placeholder="Search activity types...">
                            </div>
                        </div>

                        <div class="overflow-x-auto">
                            <table id="activity-table" class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                <thead class="bg-gray-50 dark:bg-gray-700">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            <div class="flex items-center">
                                                <span>Name</span>
                                                <i class="fas fa-sort ml-1 text-gray-400"></i>
                                            </div>
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                            <div class="flex items-center">
                                                <span>Department</span>
                                                <i class="fas fa-sort ml-1 text-gray-400"></i>
                                            </div>
                                        </th>
                                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                    {% for activity_type in activity_types %}
                                        <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150">
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ activity_type.name }}</div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                                    {{ activity_type.department }}
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                <div class="flex justify-end space-x-2">
                                                    <a href="{% url 'admin_section:edit_activity_type' activity_type.id %}"
                                                       class="p-1.5 bg-blue-50 text-blue-600 rounded hover:bg-blue-100 dark:bg-blue-900/30 dark:text-blue-400 dark:hover:bg-blue-900/50 transition-colors duration-200"
                                                       title="Edit Activity Type">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <form method="post" action="{% url 'admin_section:delete_activity_type' activity_type.id %}" class="inline" onsubmit="return confirm('Are you sure you want to delete this activity type?');">
                                                        {% csrf_token %}
                                                        <button type="submit"
                                                                class="p-1.5 bg-red-50 text-red-600 rounded hover:bg-red-100 dark:bg-red-900/30 dark:text-red-400 dark:hover:bg-red-900/50 transition-colors duration-200"
                                                                title="Delete Activity Type">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    {% empty %}
                                        <tr>
                                            <td colspan="3" class="px-6 py-8 text-center">
                                                <div class="flex flex-col items-center justify-center text-gray-500 dark:text-gray-400">
                                                    <i class="fas fa-clipboard-list text-4xl mb-3"></i>
                                                    <p class="text-lg font-medium">No activity types found</p>
                                                    <p class="text-sm mt-1">Create your first activity type using the form</p>
                                                </div>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        {% if activity_types.paginator.num_pages > 1 %}
                            <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
                                <div class="flex flex-col sm:flex-row justify-between items-center">
                                    <div class="text-sm text-gray-700 dark:text-gray-300 mb-4 sm:mb-0">
                                        Showing <span class="font-medium">{{ activity_types.start_index }}</span> to <span class="font-medium">{{ activity_types.end_index }}</span> of <span class="font-medium">{{ activity_types.paginator.count }}</span> activity types
                                    </div>
                                    <nav class="flex justify-center">
                                        <ul class="flex items-center space-x-1">
                                            {% if activity_types.has_previous %}
                                                <li>
                                                    <a href="?page=1" class="px-2 py-1 rounded-md bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors duration-200" title="First Page">
                                                        <i class="fas fa-angle-double-left"></i>
                                                    </a>
                                                </li>
                                                <li>
                                                    <a href="?page={{ activity_types.previous_page_number }}" class="px-3 py-1 rounded-md bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors duration-200">
                                                        <i class="fas fa-angle-left mr-1"></i> Prev
                                                    </a>
                                                </li>
                                            {% endif %}

                                            {% for num in activity_types.paginator.page_range %}
                                                {% if activity_types.number == num %}
                                                    <li>
                                                        <span class="px-3 py-1 rounded-md bg-blue-600 text-white font-medium">
                                                            {{ num }}
                                                        </span>
                                                    </li>
                                                {% elif num > activity_types.number|add:-3 and num < activity_types.number|add:3 %}
                                                    <li>
                                                        <a href="?page={{ num }}" class="px-3 py-1 rounded-md bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors duration-200">
                                                            {{ num }}
                                                        </a>
                                                    </li>
                                                {% endif %}
                                            {% endfor %}

                                            {% if activity_types.has_next %}
                                                <li>
                                                    <a href="?page={{ activity_types.next_page_number }}" class="px-3 py-1 rounded-md bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors duration-200">
                                                        Next <i class="fas fa-angle-right ml-1"></i>
                                                    </a>
                                                </li>
                                                <li>
                                                    <a href="?page={{ activity_types.paginator.num_pages }}" class="px-2 py-1 rounded-md bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors duration-200" title="Last Page">
                                                        <i class="fas fa-angle-double-right"></i>
                                                    </a>
                                                </li>
                                            {% endif %}
                                        </ul>
                                    </nav>
                                </div>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript for Table Filtering and Enhanced UX -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Table search functionality
            const searchInput = document.getElementById('table-search');
            const table = document.getElementById('activity-table');
            const tbody = table.getElementsByTagName('tbody')[0];
            const rows = tbody.getElementsByTagName('tr');
            const emptyRow = document.querySelector('tr td[colspan="3"]')?.closest('tr');
            let searchTimeout;

            // Function to filter table rows
            function filterTable() {
                const filter = searchInput.value.toLowerCase().trim();
                let visibleCount = 0;

                // Don't filter if we have the empty message row
                if (emptyRow && rows.length === 1) return;

                for (let i = 0; i < rows.length; i++) {
                    // Skip the empty message row if it exists
                    if (rows[i] === emptyRow) continue;

                    const nameCell = rows[i].querySelector('td:first-child');
                    const departmentCell = rows[i].querySelector('td:nth-child(2)');

                    if (!nameCell || !departmentCell) continue;

                    const name = nameCell.textContent.toLowerCase();
                    const department = departmentCell.textContent.toLowerCase();

                    if (filter === '' || name.includes(filter) || department.includes(filter)) {
                        rows[i].style.display = '';
                        visibleCount++;
                    } else {
                        rows[i].style.display = 'none';
                    }
                }

                // Show a "no results" message if no matches found
                let noResultsRow = tbody.querySelector('.no-results-row');

                if (visibleCount === 0 && filter !== '') {
                    if (!noResultsRow) {
                        noResultsRow = document.createElement('tr');
                        noResultsRow.className = 'no-results-row';
                        noResultsRow.innerHTML = `
                            <td colspan="3" class="px-6 py-8 text-center">
                                <div class="flex flex-col items-center justify-center text-gray-500 dark:text-gray-400">
                                    <i class="fas fa-search text-4xl mb-3"></i>
                                    <p class="text-lg font-medium">No matching activity types</p>
                                    <p class="text-sm mt-1">Try adjusting your search criteria</p>
                                </div>
                            </td>
                        `;
                        tbody.appendChild(noResultsRow);
                    }
                } else if (noResultsRow) {
                    noResultsRow.remove();
                }
            }

            // Debounced search to improve performance
            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(filterTable, 200);
            });

            // Clear search button
            searchInput.addEventListener('keyup', function(e) {
                if (e.key === 'Escape') {
                    searchInput.value = '';
                    filterTable();
                }
            });

            // Auto-hide messages after 5 seconds
            const messages = document.querySelectorAll('.p-4.rounded-lg.flex.items-start');
            if (messages.length > 0) {
                setTimeout(function() {
                    messages.forEach(function(message) {
                        message.style.opacity = '0';
                        message.style.transition = 'opacity 0.5s ease-out';
                        setTimeout(function() {
                            message.remove();
                        }, 500);
                    });
                }, 5000);
            }
        });
    </script>
{% endblock %}