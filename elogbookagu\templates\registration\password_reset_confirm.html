{% extends 'base.html' %}

{% block title %}Set New Password - MedLogBook{% endblock %}

{% block extra_head %}
  <style>
    @keyframes float {
      0% { transform: translateY(0px); }
      50% { transform: translateY(-10px); }
      100% { transform: translateY(0px); }
    }

    .animate-float {
      animation: float 6s ease-in-out infinite;
    }

    .bg-pattern {
      background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    }

    .form-input:focus {
      box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.25);
    }

    .password-strength {
      height: 5px;
      border-radius: 2px;
      margin-top: 8px;
      transition: all 0.3s ease;
    }

    .password-strength-text {
      font-size: 0.75rem;
      margin-top: 4px;
      transition: all 0.3s ease;
    }
  </style>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const passwordInput = document.getElementById('id_new_password1');
      const strengthBar = document.getElementById('password-strength-bar');
      const strengthText = document.getElementById('password-strength-text');

      if (passwordInput && strengthBar && strengthText) {
        passwordInput.addEventListener('input', function() {
          const password = this.value;
          let strength = 0;

          // Length check
          if (password.length >= 8) strength += 1;

          // Contains lowercase
          if (/[a-z]/.test(password)) strength += 1;

          // Contains uppercase
          if (/[A-Z]/.test(password)) strength += 1;

          // Contains number
          if (/[0-9]/.test(password)) strength += 1;

          // Contains special char
          if (/[^A-Za-z0-9]/.test(password)) strength += 1;

          // Update UI based on strength
          switch(strength) {
            case 0:
              strengthBar.style.width = '0%';
              strengthBar.className = 'password-strength bg-gray-200 dark:bg-gray-700';
              strengthText.textContent = '';
              break;
            case 1:
              strengthBar.style.width = '20%';
              strengthBar.className = 'password-strength bg-red-500';
              strengthText.textContent = 'Very Weak';
              strengthText.className = 'password-strength-text text-red-500';
              break;
            case 2:
              strengthBar.style.width = '40%';
              strengthBar.className = 'password-strength bg-orange-500';
              strengthText.textContent = 'Weak';
              strengthText.className = 'password-strength-text text-orange-500';
              break;
            case 3:
              strengthBar.style.width = '60%';
              strengthBar.className = 'password-strength bg-yellow-500';
              strengthText.textContent = 'Medium';
              strengthText.className = 'password-strength-text text-yellow-500';
              break;
            case 4:
              strengthBar.style.width = '80%';
              strengthBar.className = 'password-strength bg-blue-500';
              strengthText.textContent = 'Strong';
              strengthText.className = 'password-strength-text text-blue-500';
              break;
            case 5:
              strengthBar.style.width = '100%';
              strengthBar.className = 'password-strength bg-green-500';
              strengthText.textContent = 'Very Strong';
              strengthText.className = 'password-strength-text text-green-500';
              break;
          }
        });
      }
    });
  </script>
{% endblock %}

{% block navbar %}
  {% include 'components/public_navbar.html' %}
{% endblock %}

{% block content_container %}
  <!-- Full-width Reset Password Section -->
  <section class="w-full min-h-screen bg-gradient-to-r from-blue-600 to-purple-700 dark:from-gray-800 dark:to-gray-900 bg-pattern py-16">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8 flex justify-center items-center">
      <div class="max-w-md w-full">
        {% if validlink %}
          <!-- Reset Password Card -->
          <div class="bg-white dark:bg-gray-800 rounded-xl shadow-2xl overflow-hidden">
            <!-- Card Header with Icon -->
            <div class="bg-blue-50 dark:bg-blue-900/30 p-6 text-center">
              <div class="inline-flex items-center justify-center w-16 h-16 bg-blue-100 dark:bg-blue-800 rounded-full mb-4 animate-float">
                <i class="fas fa-lock text-blue-600 dark:text-blue-400 text-2xl"></i>
              </div>
              <h2 class="text-2xl font-bold text-gray-900 dark:text-white">Set New Password</h2>
              <p class="mt-2 text-gray-600 dark:text-gray-300">Create a strong, secure password for your account.</p>
            </div>

            <!-- Form Section -->
            <div class="p-6">
              {% if form.errors %}
                <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-3 rounded-lg mb-6">
                  <p class="font-medium">Please correct the errors below:</p>
                  <ul class="list-disc list-inside text-sm mt-2">
                    {% for field in form %}
                      {% for error in field.errors %}
                        <li>{{ field.label }}: {{ error }}</li>
                      {% endfor %}
                    {% endfor %}
                    {% for error in form.non_field_errors %}
                      <li>{{ error }}</li>
                    {% endfor %}
                  </ul>
                </div>
              {% endif %}

              <form method="post" class="space-y-6">
                {% csrf_token %}

                <!-- New Password Field -->
                <div>
                  <label for="id_new_password1" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">New Password</label>
                  <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <i class="fas fa-lock text-gray-400"></i>
                    </div>
                    <input type="password" name="new_password1" id="id_new_password1" required
                           class="form-input w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:border-blue-500 dark:focus:border-blue-500">
                  </div>

                  <!-- Password Strength Indicator -->
                  <div class="mt-2">
                    <div class="bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                      <div id="password-strength-bar" class="password-strength bg-gray-200 dark:bg-gray-700" style="width: 0%"></div>
                    </div>
                    <div id="password-strength-text" class="password-strength-text text-gray-500 dark:text-gray-400"></div>
                  </div>

                  <!-- Password Requirements -->
                  <div class="mt-2 text-xs text-gray-500 dark:text-gray-400">
                    <p>Your password must:</p>
                    <ul class="list-disc list-inside mt-1 space-y-1">
                      <li>Be at least 8 characters long</li>
                      <li>Contain at least one uppercase letter</li>
                      <li>Contain at least one lowercase letter</li>
                      <li>Contain at least one number</li>
                      <li>Not be too similar to your personal information</li>
                    </ul>
                  </div>
                </div>

                <!-- Confirm Password Field -->
                <div>
                  <label for="id_new_password2" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Confirm New Password</label>
                  <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <i class="fas fa-lock-open text-gray-400"></i>
                    </div>
                    <input type="password" name="new_password2" id="id_new_password2" required
                           class="form-input w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:border-blue-500 dark:focus:border-blue-500">
                  </div>
                  <p class="mt-2 text-xs text-gray-500 dark:text-gray-400">Enter the same password as above, for verification.</p>
                </div>

                <!-- Submit Button -->
                <div>
                  <button type="submit" class="w-full flex justify-center items-center px-4 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg shadow-md transition duration-200 transform hover:translate-y-[-1px]">
                    <i class="fas fa-check-circle mr-2"></i> Set New Password
                  </button>
                </div>
              </form>
            </div>

            <!-- Security Note -->
            <div class="px-6 pb-6">
              <div class="border-t border-gray-200 dark:border-gray-700 pt-4 mt-2">
                <div class="flex items-start">
                  <div class="flex-shrink-0 mt-1">
                    <i class="fas fa-shield-alt text-gray-400"></i>
                  </div>
                  <p class="ml-3 text-xs text-gray-500 dark:text-gray-400">
                    For security reasons, we recommend using a unique password that you don't use for other websites.
                  </p>
                </div>
              </div>
            </div>
          </div>
        {% else %}
          <!-- Invalid Link Card -->
          <div class="bg-white dark:bg-gray-800 rounded-xl shadow-2xl overflow-hidden">
            <div class="bg-red-50 dark:bg-red-900/30 p-6 text-center">
              <div class="inline-flex items-center justify-center w-16 h-16 bg-red-100 dark:bg-red-800 rounded-full mb-4">
                <i class="fas fa-exclamation-triangle text-red-600 dark:text-red-400 text-2xl"></i>
              </div>
              <h2 class="text-2xl font-bold text-gray-900 dark:text-white">Invalid Reset Link</h2>
              <p class="mt-2 text-gray-600 dark:text-gray-300">The password reset link is invalid or has expired.</p>
            </div>

            <div class="p-6 text-center">
              <p class="text-gray-600 dark:text-gray-300 mb-6">
                The password reset link you clicked is no longer valid. This may be because:
              </p>

              <ul class="text-left text-gray-600 dark:text-gray-300 space-y-2 mb-8">
                <li class="flex items-start">
                  <i class="fas fa-circle text-xs mt-1.5 mr-2 text-gray-400"></i>
                  <span>The link has already been used</span>
                </li>
                <li class="flex items-start">
                  <i class="fas fa-circle text-xs mt-1.5 mr-2 text-gray-400"></i>
                  <span>The link has expired (they're valid for 24 hours)</span>
                </li>
                <li class="flex items-start">
                  <i class="fas fa-circle text-xs mt-1.5 mr-2 text-gray-400"></i>
                  <span>The link was copied incorrectly</span>
                </li>
              </ul>

              <a href="{% url 'password_reset' %}" class="inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200">
                <i class="fas fa-redo mr-2"></i> Request a New Reset Link
              </a>
            </div>
          </div>
        {% endif %}

        <!-- Help Text -->
        <div class="mt-6 text-center">
          <p class="text-sm text-white/80">
            Need help? <a href="#" class="font-medium text-white hover:text-white/90 underline">Contact Support</a>
          </p>
        </div>
      </div>
    </div>
  </section>
{% endblock %}
