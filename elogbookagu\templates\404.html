{% extends "base.html" %}
{% load static %}

{% block title %}404 - Page Not Found | MedLogBook{% endblock %}

{% block extra_head %}
<style>
  @keyframes float {

    0%,
    100% {
      transform: translateY(0px);
    }

    50% {
      transform: translateY(-20px);
    }
  }

  @keyframes bounce {

    0%,
    20%,
    50%,
    80%,
    100% {
      transform: translateY(0);
    }

    40% {
      transform: translateY(-30px);
    }

    60% {
      transform: translateY(-15px);
    }
  }

  @keyframes pulse-glow {

    0%,
    100% {
      box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
    }

    50% {
      box-shadow: 0 0 40px rgba(59, 130, 246, 0.6);
    }
  }

  @keyframes slide-in {
    from {
      opacity: 0;
      transform: translateY(30px);
    }

    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-bounce-custom {
    animation: bounce 2s infinite;
  }

  .animate-pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite;
  }

  .animate-slide-in {
    animation: slide-in 0.6s ease-out;
  }

  .bg-pattern {
    background-image:
      radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, rgba(147, 51, 234, 0.1) 0%, transparent 50%);
  }

  .glass-effect {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .dark .glass-effect {
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
</style>
{% endblock %}

{% block content_container %}
<!-- Full-width 404 Section -->
<section
  class="w-full min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 bg-pattern relative overflow-hidden">
  <!-- Animated Background Elements -->
  <div class="absolute inset-0 overflow-hidden pointer-events-none">
    <div class="absolute top-20 left-10 w-32 h-32 bg-blue-200 dark:bg-blue-800 rounded-full opacity-20 animate-float">
    </div>
    <div
      class="absolute top-40 right-20 w-24 h-24 bg-purple-200 dark:bg-purple-800 rounded-full opacity-20 animate-float"
      style="animation-delay: 1s;"></div>
    <div
      class="absolute bottom-20 left-1/4 w-40 h-40 bg-indigo-200 dark:bg-indigo-800 rounded-full opacity-20 animate-float"
      style="animation-delay: 2s;"></div>
    <div
      class="absolute bottom-40 right-1/3 w-28 h-28 bg-pink-200 dark:bg-pink-800 rounded-full opacity-20 animate-float"
      style="animation-delay: 0.5s;"></div>
  </div>

  <div class="container mx-auto px-4 sm:px-6 lg:px-8 py-16 relative z-10">
    <div class="max-w-6xl mx-auto">

      <!-- Header with AGU Logo -->
      <div class="text-center mb-12 animate-slide-in">
        <div class="flex flex-col items-center justify-center mb-6">
          <img src="/media/agulogo.png" alt="Arabian Gulf University Logo"
            class="h-20 w-auto mb-4 animate-pulse-glow rounded-lg shadow-lg">


          <div class="text-center">
            <h1 class="text-2xl md:text-3xl font-bold text-gray-800 dark:text-white">Arabian Gulf University</h1>
            <p class="text-sm md:text-base text-gray-600 dark:text-gray-300 mt-1">MedLogBook Platform</p>
          </div>
        </div>
      </div>

      <!-- Main 404 Content -->
      <div class="grid lg:grid-cols-2 gap-8 lg:gap-12 items-center">

        <!-- Left Side - 404 Illustration -->
        <div class="text-center animate-slide-in order-2 lg:order-1" style="animation-delay: 0.2s;">
          <div class="relative flex flex-col items-center justify-center">
            <!-- Large 404 Text -->
            <div class="text-center mb-8">
              <h2
                class="text-8xl md:text-9xl lg:text-[10rem] font-black text-transparent bg-clip-text bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 dark:from-blue-400 dark:via-purple-400 dark:to-indigo-400 leading-none animate-bounce-custom">
                404
              </h2>
              <div
                class="absolute inset-0 text-8xl md:text-9xl lg:text-[10rem] font-black text-blue-100 dark:text-gray-800 leading-none -z-10 transform translate-x-2 translate-y-2">
                404
              </div>
            </div>


          </div>
        </div>

        <!-- Right Side - Error Message and Actions -->
        <div class="space-y-6 lg:space-y-8 animate-slide-in order-1 lg:order-2" style="animation-delay: 0.4s;">

          <!-- Error Message -->
          <div class="glass-effect rounded-2xl p-8 shadow-2xl">
            <div class="text-center lg:text-left">
              <h3 class="text-3xl md:text-4xl font-bold text-gray-800 dark:text-white mb-4">
                Oops! Page Not Found
              </h3>
              <p class="text-lg text-gray-600 dark:text-gray-300 mb-6 leading-relaxed">
                The page you're looking for seems to have taken a medical leave. Don't worry, our diagnostic team is
                here to help you find what you need!
              </p>

              <!-- Error Details -->
              <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
                <div class="flex items-center">
                  <i class="fas fa-exclamation-triangle text-red-500 mr-3"></i>
                  <div>
                    <p class="text-sm font-medium text-red-800 dark:text-red-200">Error Code: 404</p>
                    <p class="text-sm text-red-600 dark:text-red-300">The requested resource could not be found on this
                      server.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Quick Actions -->
          <div class="glass-effect rounded-2xl p-8 shadow-2xl">
            <h4 class="text-xl font-semibold text-gray-800 dark:text-white mb-6">
              Quick Navigation
            </h4>

            <div class="grid sm:grid-cols-2 gap-4 mb-6">
              <a href="{% url 'home_page' %}"
                class="group flex items-center justify-center p-4 bg-blue-500 hover:bg-blue-600 text-white rounded-xl transition-all duration-300 transform hover:scale-105 hover:shadow-lg">
                <div class="text-center">
                  <div class="font-semibold">Home Page</div>
                  <div class="text-sm opacity-90">Return to homepage</div>
                </div>
              </a>

              <a href="{% url 'login' %}"
                class="group flex items-center justify-center p-4 bg-green-500 hover:bg-green-600 text-white rounded-xl transition-all duration-300 transform hover:scale-105 hover:shadow-lg">
                <div class="text-center">
                  <div class="font-semibold">Login</div>
                  <div class="text-sm opacity-90">Access your account</div>
                </div>
              </a>
            </div>


          </div>



          <!-- Help & Support -->
          <div class="glass-effect rounded-2xl p-8 shadow-2xl">
            <h4 class="text-xl font-semibold text-gray-800 dark:text-white mb-4">
              Need Help?
            </h4>
            <p class="text-gray-600 dark:text-gray-300 mb-4">
              If you believe this is an error or need assistance, please contact our support team.
            </p>

            <div class="flex flex-col sm:flex-row gap-3">
              <a href="mailto:<EMAIL>"
                class="flex items-center justify-center px-4 py-2 bg-orange-500 hover:bg-orange-600 text-white rounded-lg transition-all duration-300 hover:shadow-lg">
                Email Support
              </a>
              <button onclick="window.history.back()"
                class="flex items-center justify-center px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg transition-all duration-300 hover:shadow-lg">
                Go Back
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Footer Message -->
      <div class="text-center mt-16 animate-slide-in" style="animation-delay: 0.6s;">
        <div class="glass-effect rounded-xl p-6 inline-block">
          <p class="text-gray-600 dark:text-gray-300">
            Thank you for using MedLogBook - Your Medical Education Partner
          </p>
        </div>
      </div>
    </div>
  </div>
</section>

<script>
  function performSearch(query) {
    if (query.trim()) {
      // You can customize this to redirect to your search page
      // For now, it will redirect to home page with search parameter
      window.location.href = `{% url 'home_page' %}?search=${encodeURIComponent(query)}`;
    }
  }

  // Add some interactive effects
  document.addEventListener('DOMContentLoaded', function () {
    // Add hover effects to floating icons
    const floatingIcons = document.querySelectorAll('.animate-float');
    floatingIcons.forEach(icon => {
      icon.addEventListener('mouseenter', function () {
        this.style.animationPlayState = 'paused';
        this.style.transform = 'translateY(-10px) scale(1.2)';
      });

      icon.addEventListener('mouseleave', function () {
        this.style.animationPlayState = 'running';
        this.style.transform = '';
      });
    });

    // Add click effect to the 404 number
    const errorNumber = document.querySelector('.animate-bounce-custom');
    if (errorNumber) {
      errorNumber.addEventListener('click', function () {
        this.style.animation = 'none';
        setTimeout(() => {
          this.style.animation = 'bounce 2s infinite';
        }, 100);
      });
    }
  });
</script>
{% endblock %}