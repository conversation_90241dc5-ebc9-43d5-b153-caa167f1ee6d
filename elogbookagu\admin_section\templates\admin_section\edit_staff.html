{% extends 'base.html' %}

{% block title %}
  Edit Staff
{% endblock %}

{% block navbar %}
  {% include 'components/admin_auth_navbar.html' %}
{% endblock %}

{% block content %}
  <div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-8">
      <h1 class="text-3xl font-bold text-gray-800 dark:text-white">Edit Staff</h1>
      <a href="{% url 'admin_section:add_staff' %}" class="px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg transition-colors duration-200 flex items-center">
        <i class="fas fa-arrow-left mr-2"></i> Back to Staff List
      </a>
    </div>

    <!-- Messages -->
    {% if messages %}
      <div class="mb-6">
        {% for message in messages %}
          <div class="p-4 mb-4 rounded-lg flex items-start {% if message.tags == 'success' %}bg-green-100 text-green-800 dark:bg-green-800/50 dark:text-green-100{% elif message.tags == 'error' %}bg-red-100 text-red-800 dark:bg-red-800/50 dark:text-red-100{% else %}bg-blue-100 text-blue-800 dark:bg-blue-800/50 dark:text-blue-100{% endif %}">
            <div class="mr-3 mt-0.5">
              {% if message.tags == 'success' %}
                <i class="fas fa-check-circle"></i>
              {% elif message.tags == 'error' %}
                <i class="fas fa-exclamation-circle"></i>
              {% else %}
                <i class="fas fa-info-circle"></i>
              {% endif %}
            </div>
            <div>
              {{ message }}
            </div>
            <button type="button" class="ml-auto -mx-1.5 -my-1.5 rounded-lg focus:ring-2 p-1.5 inline-flex h-8 w-8 hover:bg-gray-200 dark:hover:bg-gray-700" onclick="this.parentElement.remove()">
              <span class="sr-only">Close</span>
              <i class="fas fa-times"></i>
            </button>
          </div>
        {% endfor %}
      </div>
    {% endif %}

    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
      <div class="flex items-center mb-6">
        <div class="flex-shrink-0 h-16 w-16 mr-4">
          {% if staff.user.profile_photo %}
            <img class="h-16 w-16 rounded-full object-cover" src="{{ staff.user.profile_photo.url }}" alt="{{ staff.user.get_full_name }}">
          {% else %}
            <div class="h-16 w-16 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
              <span class="text-blue-600 dark:text-blue-300 text-xl font-medium">{{ staff.user.first_name|first }}{{ staff.user.last_name|first }}</span>
            </div>
          {% endif %}
        </div>
        <div>
          <h2 class="text-xl font-semibold text-gray-800 dark:text-white">{{ staff.user.get_full_name }}</h2>
          <p class="text-gray-500 dark:text-gray-400">{{ staff.user.email }}</p>
        </div>
      </div>

      <form method="post" class="space-y-6">
        {% csrf_token %}
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Left Column -->
          <div class="space-y-4">
            <div>
              <label for="{{ user_form.username.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Username <span class="text-red-500">*</span></label>
              {{ user_form.username }}
              {% if user_form.username.errors %}
                <p class="text-red-500 text-xs mt-1">{{ user_form.username.errors.0 }}</p>
              {% endif %}
            </div>
            <div>
              <label for="{{ user_form.email.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Email <span class="text-red-500">*</span></label>
              {{ user_form.email }}
              {% if user_form.email.errors %}
                <p class="text-red-500 text-xs mt-1">{{ user_form.email.errors.0 }}</p>
              {% endif %}
            </div>
            <div>
              <label for="{{ user_form.first_name.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">First Name <span class="text-red-500">*</span></label>
              {{ user_form.first_name }}
              {% if user_form.first_name.errors %}
                <p class="text-red-500 text-xs mt-1">{{ user_form.first_name.errors.0 }}</p>
              {% endif %}
            </div>
            <div>
              <label for="{{ user_form.last_name.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Last Name <span class="text-red-500">*</span></label>
              {{ user_form.last_name }}
              {% if user_form.last_name.errors %}
                <p class="text-red-500 text-xs mt-1">{{ user_form.last_name.errors.0 }}</p>
              {% endif %}
            </div>
          </div>

          <!-- Right Column -->
          <div class="space-y-4">
            <div>
              <label for="{{ user_form.phone_no.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Phone Number</label>
              {{ user_form.phone_no }}
              {% if user_form.phone_no.errors %}
                <p class="text-red-500 text-xs mt-1">{{ user_form.phone_no.errors.0 }}</p>
              {% endif %}
            </div>
            <div>
              <label for="{{ user_form.city.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">City</label>
              {{ user_form.city }}
              {% if user_form.city.errors %}
                <p class="text-red-500 text-xs mt-1">{{ user_form.city.errors.0 }}</p>
              {% endif %}
            </div>
            <div>
              <label for="{{ user_form.country.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Country</label>
              {{ user_form.country }}
              {% if user_form.country.errors %}
                <p class="text-red-500 text-xs mt-1">{{ user_form.country.errors.0 }}</p>
              {% endif %}
            </div>
            <div>
              <label for="{{ staff_form.departments.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Departments</label>
              {{ staff_form.departments }}
              {% if staff_form.departments.errors %}
                <p class="text-red-500 text-xs mt-1">{{ staff_form.departments.errors.0 }}</p>
              {% endif %}
              <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">Hold Ctrl/Cmd to select multiple departments</p>
            </div>
          </div>
        </div>

        <div>
          <label for="{{ user_form.bio.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Bio</label>
          {{ user_form.bio }}
          {% if user_form.bio.errors %}
            <p class="text-red-500 text-xs mt-1">{{ user_form.bio.errors.0 }}</p>
          {% endif %}
        </div>

        <div class="flex justify-end space-x-4 pt-4">
          <a href="{% url 'admin_section:add_staff' %}" class="px-6 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg transition-colors duration-200">
            Cancel
          </a>
          <button type="submit" class="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200">
            <i class="fas fa-save mr-2"></i> Save Changes
          </button>
        </div>
      </form>
    </div>
  </div>
{% endblock %}
