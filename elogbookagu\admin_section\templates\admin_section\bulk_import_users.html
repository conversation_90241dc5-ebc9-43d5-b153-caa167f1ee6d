{% extends 'base.html' %}

{% block title %}Bulk Import Users{% endblock %}

{% block navbar %}
  {% include 'components/admin_auth_navbar.html' %}
{% endblock %}

{% block content %}
  <div class="container mx-auto px-4 py-8">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 max-w-7xl mx-auto">
      <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800 dark:text-white">Bulk Import Users</h2>
        <a href="{% url 'admin_section:add_user' %}" class="inline-flex items-center px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white rounded-lg hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors duration-200">
          <i class="fas fa-user-plus mr-2"></i> Add Single User
        </a>
      </div>

      <!-- Messages section -->
      {% if messages %}
        <div class="mb-6" id="messages-container">
          {% for message in messages %}
            <div class="p-4 mb-4 rounded-lg flex items-center justify-between {% if message.tags == 'success' %}bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100{% elif message.tags == 'error' %}bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100{% elif message.tags == 'warning' %}bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100{% else %}bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100{% endif %}">
              <div class="flex items-center">
                <i class="{% if message.tags == 'success' %}fas fa-check-circle{% elif message.tags == 'error' %}fas fa-exclamation-circle{% elif message.tags == 'warning' %}fas fa-exclamation-triangle{% else %}fas fa-info-circle{% endif %} mr-3 text-xl"></i>
                <span>{{ message }}</span>
              </div>
              <button type="button" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 focus:outline-none" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
              </button>
            </div>
          {% endfor %}
        </div>
      {% endif %}

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Upload Form -->
        <div>
          <div class="bg-gray-50 dark:bg-gray-700 rounded-lg shadow-md p-6 border border-gray-200 dark:border-gray-600 transition-all duration-300 hover:shadow-lg">
            <div class="flex items-center mb-4">
              <i class="fas fa-file-upload text-blue-500 dark:text-blue-400 text-xl mr-2"></i>
              <h3 class="text-xl font-semibold text-gray-800 dark:text-white">Upload CSV File</h3>
            </div>

            <form method="post" enctype="multipart/form-data" class="space-y-5" id="upload-form">
              {% csrf_token %}

              <div class="transition-all duration-300 hover:bg-white dark:hover:bg-gray-600 p-3 rounded-lg">
                <label for="{{ form.user_type.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">User Type</label>
                <div class="relative">
                  {{ form.user_type }}
                  <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700 dark:text-gray-300">
                    <i class="fas fa-chevron-down text-xs"></i>
                  </div>
                </div>
                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">Select the type of users you want to import</p>
              </div>

              <div class="transition-all duration-300 hover:bg-white dark:hover:bg-gray-600 p-3 rounded-lg">
                <label for="{{ form.csv_file.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">CSV File</label>
                <div class="relative">
                  <div class="flex items-center justify-center w-full">
                    <label for="{{ form.csv_file.id_for_label }}" class="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 dark:hover:bg-gray-600 dark:bg-gray-700 hover:bg-gray-100 dark:border-gray-600 dark:hover:border-gray-500">
                      <div class="flex flex-col items-center justify-center pt-5 pb-6" id="file-upload-placeholder">
                        <i class="fas fa-cloud-upload-alt mb-3 text-gray-400 text-2xl"></i>
                        <p class="mb-2 text-sm text-gray-500 dark:text-gray-400"><span class="font-semibold">Click to upload</span> or drag and drop</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400">CSV file only (MAX. 5MB)</p>
                      </div>
                      <div class="hidden items-center justify-center pt-5 pb-6" id="file-selected-info">
                        <i class="fas fa-file-csv mb-3 text-green-500 text-2xl"></i>
                        <p class="mb-2 text-sm text-gray-700 dark:text-gray-300 font-medium" id="selected-filename">No file selected</p>
                        <p class="text-xs text-gray-500 dark:text-gray-400">Click to change file</p>
                      </div>
                      {{ form.csv_file }}
                    </label>
                  </div>
                </div>
                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">{{ form.csv_file.help_text }}</p>
              </div>

              <div class="pt-4">
                <button type="submit" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-4 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition duration-200 ease-in-out flex items-center justify-center">
                  <i class="fas fa-upload mr-2"></i>
                  <span>Upload and Import</span>
                </button>
              </div>
            </form>
          </div>

          <!-- Download Sample Templates -->
          <div class="mt-6 bg-gray-50 dark:bg-gray-700 rounded-lg shadow-md p-6 border border-gray-200 dark:border-gray-600 transition-all duration-300 hover:shadow-lg">
            <div class="flex items-center mb-4">
              <i class="fas fa-download text-green-500 dark:text-green-400 text-xl mr-2"></i>
              <h3 class="text-xl font-semibold text-gray-800 dark:text-white">Download Sample Templates</h3>
            </div>
            <p class="text-gray-600 dark:text-gray-300 mb-4">Download a sample CSV template for the user type you want to import:</p>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
              <a href="{% url 'admin_section:download_user_template' %}?type=student" data-template-type="student" class="flex items-center justify-center bg-white dark:bg-gray-800 hover:bg-green-50 dark:hover:bg-green-900 text-green-600 dark:text-green-400 font-medium py-3 px-4 rounded-lg border border-green-200 dark:border-green-800 transition duration-200 ease-in-out group">
                <i class="fas fa-user-graduate mr-2 group-hover:animate-bounce"></i>
                <span>Student Template</span>
              </a>
              <a href="{% url 'admin_section:download_user_template' %}?type=doctor" data-template-type="doctor" class="flex items-center justify-center bg-white dark:bg-gray-800 hover:bg-indigo-50 dark:hover:bg-indigo-900 text-indigo-600 dark:text-indigo-400 font-medium py-3 px-4 rounded-lg border border-indigo-200 dark:border-indigo-800 transition duration-200 ease-in-out group">
                <i class="fas fa-user-md mr-2 group-hover:animate-bounce"></i>
                <span>Doctor Template</span>
              </a>
              <a href="{% url 'admin_section:download_user_template' %}?type=staff" data-template-type="staff" class="flex items-center justify-center bg-white dark:bg-gray-800 hover:bg-purple-50 dark:hover:bg-purple-900 text-purple-600 dark:text-purple-400 font-medium py-3 px-4 rounded-lg border border-purple-200 dark:border-purple-800 transition duration-200 ease-in-out group">
                <i class="fas fa-user-tie mr-2 group-hover:animate-bounce"></i>
                <span>Staff Template</span>
              </a>
            </div>

            <div class="mt-4 text-center text-sm text-gray-500 dark:text-gray-400">
              <p>Templates include all required fields and example data</p>
            </div>
          </div>

          <!-- Export Users Section -->
          <div class="mt-6 bg-gray-50 dark:bg-gray-700 rounded-lg shadow-md p-6 border border-gray-200 dark:border-gray-600 transition-all duration-300 hover:shadow-lg">
            <div class="flex items-center mb-4">
              <i class="fas fa-file-export text-orange-500 dark:text-orange-400 text-xl mr-2"></i>
              <h3 class="text-xl font-semibold text-gray-800 dark:text-white">Export Users</h3>
            </div>
            <p class="text-gray-600 dark:text-gray-300 mb-4">Export existing users to CSV format for backup or analysis:</p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
              <a href="{% url 'admin_section:export_users' %}?type=student" class="flex items-center justify-center bg-white dark:bg-gray-800 hover:bg-green-50 dark:hover:bg-green-900 text-green-600 dark:text-green-400 font-medium py-3 px-4 rounded-lg border border-green-200 dark:border-green-800 transition duration-200 ease-in-out group">
                <i class="fas fa-user-graduate mr-2 group-hover:animate-bounce"></i>
                <span>Export Students</span>
              </a>
              <a href="{% url 'admin_section:export_users' %}?type=doctor" class="flex items-center justify-center bg-white dark:bg-gray-800 hover:bg-indigo-50 dark:hover:bg-indigo-900 text-indigo-600 dark:text-indigo-400 font-medium py-3 px-4 rounded-lg border border-indigo-200 dark:border-indigo-800 transition duration-200 ease-in-out group">
                <i class="fas fa-user-md mr-2 group-hover:animate-bounce"></i>
                <span>Export Doctors</span>
              </a>
              <a href="{% url 'admin_section:export_users' %}?type=staff" class="flex items-center justify-center bg-white dark:bg-gray-800 hover:bg-purple-50 dark:hover:bg-purple-900 text-purple-600 dark:text-purple-400 font-medium py-3 px-4 rounded-lg border border-purple-200 dark:border-purple-800 transition duration-200 ease-in-out group">
                <i class="fas fa-user-tie mr-2 group-hover:animate-bounce"></i>
                <span>Export Staff</span>
              </a>
              <a href="{% url 'admin_section:export_users' %}?type=all" class="flex items-center justify-center bg-white dark:bg-gray-800 hover:bg-orange-50 dark:hover:bg-orange-900 text-orange-600 dark:text-orange-400 font-medium py-3 px-4 rounded-lg border border-orange-200 dark:border-orange-800 transition duration-200 ease-in-out group">
                <i class="fas fa-users mr-2 group-hover:animate-bounce"></i>
                <span>Export All Users</span>
              </a>
            </div>

            <div class="mt-4 text-center text-sm text-gray-500 dark:text-gray-400">
              <p>Exports include comprehensive user information and account status</p>
            </div>
          </div>
        </div>

        <!-- Instructions and Results -->
        <div>
          {% if results %}
            <!-- Import Results -->
            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg shadow-md p-6 mb-6 border border-gray-200 dark:border-gray-600 transition-all duration-300 hover:shadow-lg">
              <div class="flex items-center mb-4">
                <i class="fas fa-clipboard-check text-blue-500 dark:text-blue-400 text-xl mr-2"></i>
                <h3 class="text-xl font-semibold text-gray-800 dark:text-white">Import Results</h3>
              </div>

              <div class="grid grid-cols-2 gap-4 mb-4">
                <div class="bg-white dark:bg-gray-800 border border-green-200 dark:border-green-800 p-4 rounded-lg text-center shadow-sm hover:shadow-md transition-all duration-300">
                  <div class="flex flex-col items-center">
                    <div class="rounded-full bg-green-100 dark:bg-green-900 p-3 mb-2">
                      <i class="fas fa-check text-green-600 dark:text-green-400 text-xl"></i>
                    </div>
                    <p class="text-sm text-gray-600 dark:text-gray-300 mb-1">Successfully Imported</p>
                    <p class="text-3xl font-bold text-green-600 dark:text-green-400">{{ results.success_count }}</p>
                  </div>
                </div>
                <div class="bg-white dark:bg-gray-800 border border-red-200 dark:border-red-800 p-4 rounded-lg text-center shadow-sm hover:shadow-md transition-all duration-300">
                  <div class="flex flex-col items-center">
                    <div class="rounded-full bg-red-100 dark:bg-red-900 p-3 mb-2">
                      <i class="fas fa-times text-red-600 dark:text-red-400 text-xl"></i>
                    </div>
                    <p class="text-sm text-gray-600 dark:text-gray-300 mb-1">Failed to Import</p>
                    <p class="text-3xl font-bold text-red-600 dark:text-red-400">{{ results.error_count }}</p>
                  </div>
                </div>
              </div>

              {% if results.error_messages %}
                <div class="mt-6">
                  <div class="flex items-center mb-2">
                    <i class="fas fa-exclamation-triangle text-yellow-500 mr-2"></i>
                    <h4 class="font-semibold text-gray-700 dark:text-gray-300">Error Details:</h4>
                  </div>
                  <div class="bg-white dark:bg-gray-800 p-4 rounded-lg max-h-60 overflow-y-auto border border-red-200 dark:border-red-800">
                    <ul class="list-disc pl-5 space-y-2">
                      {% for error in results.error_messages %}
                        <li class="text-sm text-red-600 dark:text-red-400 font-medium">{{ error }}</li>
                      {% endfor %}
                    </ul>
                    {% if results.total_errors > 10 %}
                      <div class="flex items-center justify-center mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
                        <p class="text-sm text-gray-500 dark:text-gray-400">
                          <i class="fas fa-info-circle mr-1"></i> Showing 10 of {{ results.total_errors }} errors.
                        </p>
                      </div>
                    {% endif %}
                  </div>
                </div>
              {% endif %}

              <div class="mt-4 flex justify-end">
                <button onclick="window.location.reload()" class="flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors duration-200">
                  <i class="fas fa-redo mr-1"></i> Import Another File
                </button>
              </div>
            </div>
          {% endif %}

          <!-- Instructions -->
          <div class="bg-gray-50 dark:bg-gray-700 rounded-lg shadow-md p-6 border border-gray-200 dark:border-gray-600 transition-all duration-300 hover:shadow-lg">
            <div class="flex items-center mb-4">
              <i class="fas fa-info-circle text-blue-500 dark:text-blue-400 text-xl mr-2"></i>
              <h3 class="text-xl font-semibold text-gray-800 dark:text-white">Instructions</h3>
            </div>

            <div class="space-y-6">
              <!-- Tabs for different sections -->
              <div x-data="{activeTab: 'fields'}" class="border-b border-gray-200 dark:border-gray-700">
                <div class="flex -mb-px">
                  <button @click="activeTab = 'fields'" :class="{'border-b-2 border-blue-500 text-blue-600 dark:text-blue-400': activeTab === 'fields', 'text-gray-500 dark:text-gray-400': activeTab !== 'fields'}" class="py-2 px-4 font-medium focus:outline-none">
                    <i class="fas fa-list-ul mr-1"></i> Required Fields
                  </button>
                  <button @click="activeTab = 'notes'" :class="{'border-b-2 border-blue-500 text-blue-600 dark:text-blue-400': activeTab === 'notes', 'text-gray-500 dark:text-gray-400': activeTab !== 'notes'}" class="py-2 px-4 font-medium focus:outline-none">
                    <i class="fas fa-sticky-note mr-1"></i> Important Notes
                  </button>
                  <button @click="activeTab = 'examples'" :class="{'border-b-2 border-blue-500 text-blue-600 dark:text-blue-400': activeTab === 'examples', 'text-gray-500 dark:text-gray-400': activeTab !== 'examples'}" class="py-2 px-4 font-medium focus:outline-none">
                    <i class="fas fa-code mr-1"></i> Examples
                  </button>
                </div>
              </div>

              <!-- Required Fields Tab -->
              <div x-show="activeTab === 'fields'" class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm">
                <h4 class="font-semibold text-gray-700 dark:text-gray-300 mb-3 flex items-center">
                  <i class="fas fa-asterisk text-red-500 mr-2 text-xs"></i> Required Fields
                </h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h5 class="font-medium text-gray-700 dark:text-gray-300 mb-2 border-b border-gray-200 dark:border-gray-700 pb-1">Common Fields (All Users)</h5>
                    <ul class="list-disc pl-5 space-y-1 text-gray-600 dark:text-gray-300">
                      <li><span class="font-medium">username</span> - Unique username for login</li>
                      <li><span class="font-medium">email</span> - Unique email address</li>
                      <li><span class="font-medium">password</span> - Strong password</li>
                      <li><span class="font-medium">first_name</span> - User's first name</li>
                      <li><span class="font-medium">last_name</span> - User's last name</li>
                      <li><span class="font-medium">phone_no</span> - Contact number</li>
                      <li><span class="font-medium">city</span> - User's city</li>
                      <li><span class="font-medium">country</span> - User's country</li>
                    </ul>
                  </div>
                  <div>
                    <h5 class="font-medium text-gray-700 dark:text-gray-300 mb-2 border-b border-gray-200 dark:border-gray-700 pb-1">Role-Specific Fields</h5>
                    <ul class="list-disc pl-5 space-y-1 text-gray-600 dark:text-gray-300">
                      <li><span class="font-medium">student_id</span> - Required for students</li>
                      <li><span class="font-medium">group</span> - Optional for students (B1, B2, etc.)</li>
                      <li><span class="font-medium">speciality</span> - Required for doctors</li>
                    </ul>
                  </div>
                </div>
              </div>

              <!-- Important Notes Tab -->
              <div x-show="activeTab === 'notes'" class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm">
                <h4 class="font-semibold text-gray-700 dark:text-gray-300 mb-3 flex items-center">
                  <i class="fas fa-exclamation-circle text-yellow-500 mr-2"></i> Important Notes
                </h4>
                <ul class="space-y-2 text-gray-600 dark:text-gray-300">
                  <li class="flex items-start">
                    <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                    <span>Select the appropriate user type before uploading</span>
                  </li>
                  <li class="flex items-start">
                    <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                    <span>Username and email must be unique in the system</span>
                  </li>
                  <li class="flex items-start">
                    <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                    <span>For students, Student ID must be unique</span>
                  </li>
                  <li class="flex items-start">
                    <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                    <span>For students, group can be specified as group name (B1, B2, A1, A2, etc.)</span>
                  </li>
                  <li class="flex items-start">
                    <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                    <span>Maximum file size: 5MB</span>
                  </li>
                  <li class="flex items-start">
                    <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                    <span>File must be in CSV format</span>
                  </li>
                  <li class="flex items-start">
                    <i class="fas fa-check-circle text-green-500 mt-1 mr-2"></i>
                    <span>Required fields vary by user type (see examples tab)</span>
                  </li>
                </ul>
              </div>

              <!-- Example Formats Tab -->
              <div x-show="activeTab === 'examples'" class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm">
                <h4 class="font-semibold text-gray-700 dark:text-gray-300 mb-3 flex items-center">
                  <i class="fas fa-code text-blue-500 mr-2"></i> Example Formats
                </h4>

                <div x-data="{activeExample: 'student'}" class="mb-4">
                  <div class="flex space-x-2 mb-3">
                    <button @click="activeExample = 'student'" :class="{'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200': activeExample === 'student', 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300': activeExample !== 'student'}" class="px-3 py-1 rounded-md text-sm font-medium transition-colors duration-200">
                      <i class="fas fa-user-graduate mr-1"></i> Student
                    </button>
                    <button @click="activeExample = 'doctor'" :class="{'bg-indigo-100 dark:bg-indigo-900 text-indigo-800 dark:text-indigo-200': activeExample === 'doctor', 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300': activeExample !== 'doctor'}" class="px-3 py-1 rounded-md text-sm font-medium transition-colors duration-200">
                      <i class="fas fa-user-md mr-1"></i> Doctor
                    </button>
                    <button @click="activeExample = 'staff'" :class="{'bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200': activeExample === 'staff', 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300': activeExample !== 'staff'}" class="px-3 py-1 rounded-md text-sm font-medium transition-colors duration-200">
                      <i class="fas fa-user-tie mr-1"></i> Staff
                    </button>
                  </div>

                  <!-- Student Format -->
                  <div x-show="activeExample === 'student'" class="border border-green-200 dark:border-green-800 rounded-lg overflow-hidden">
                    <div class="bg-green-50 dark:bg-green-900 px-4 py-2 border-b border-green-200 dark:border-green-800">
                      <h5 class="font-medium text-green-800 dark:text-green-200">Student CSV Format</h5>
                    </div>
                    <div class="bg-white dark:bg-gray-800 p-3 overflow-x-auto">
                      <code class="text-sm font-mono">
                        username,email,password,first_name,last_name,student_id,group,phone_no,city,country<br>
                        john.doe,<EMAIL>,SecurePass123,John,Doe,STU001,B1,1234567890,New York,USA<br>
                        jane.smith,<EMAIL>,SecurePass456,Jane,Smith,STU002,A2,0987654321,London,UK
                      </code>
                    </div>
                    <div class="bg-green-50 dark:bg-green-900 px-4 py-2 border-t border-green-200 dark:border-green-800 text-xs text-green-800 dark:text-green-200">
                      <i class="fas fa-info-circle mr-1"></i> For the 'group' column, use the group name (A1, A2, B1, B2, etc.) rather than the group ID number.
                    </div>
                  </div>

                  <!-- Doctor Format -->
                  <div x-show="activeExample === 'doctor'" class="border border-indigo-200 dark:border-indigo-800 rounded-lg overflow-hidden">
                    <div class="bg-indigo-50 dark:bg-indigo-900 px-4 py-2 border-b border-indigo-200 dark:border-indigo-800">
                      <h5 class="font-medium text-indigo-800 dark:text-indigo-200">Doctor CSV Format</h5>
                    </div>
                    <div class="bg-white dark:bg-gray-800 p-3 overflow-x-auto">
                      <code class="text-sm font-mono">
                        username,email,password,first_name,last_name,speciality,phone_no,city,country<br>
                        john.doc,<EMAIL>,SecurePass123,John,Doctor,Cardiology,1234567890,New York,USA<br>
                        jane.doc,<EMAIL>,SecurePass456,Jane,Doctor,Neurology,0987654321,London,UK
                      </code>
                    </div>
                  </div>

                  <!-- Staff Format -->
                  <div x-show="activeExample === 'staff'" class="border border-purple-200 dark:border-purple-800 rounded-lg overflow-hidden">
                    <div class="bg-purple-50 dark:bg-purple-900 px-4 py-2 border-b border-purple-200 dark:border-purple-800">
                      <h5 class="font-medium text-purple-800 dark:text-purple-200">Staff CSV Format</h5>
                    </div>
                    <div class="bg-white dark:bg-gray-800 p-3 overflow-x-auto">
                      <code class="text-sm font-mono">
                        username,email,password,first_name,last_name,phone_no,city,country<br>
                        john.staff,<EMAIL>,SecurePass123,John,Staff,1234567890,New York,USA<br>
                        jane.staff,<EMAIL>,SecurePass456,Jane,Staff,0987654321,London,UK
                      </code>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // File upload functionality
      const fileInput = document.getElementById('{{ form.csv_file.id_for_label }}');
      const fileUploadPlaceholder = document.getElementById('file-upload-placeholder');
      const fileSelectedInfo = document.getElementById('file-selected-info');
      const selectedFilename = document.getElementById('selected-filename');

      if (fileInput) {
        // Style the file input
        fileInput.classList.add('hidden');

        // Handle file selection
        fileInput.addEventListener('change', function() {
          if (fileInput.files.length > 0) {
            const file = fileInput.files[0];
            selectedFilename.textContent = file.name;
            fileUploadPlaceholder.classList.add('hidden');
            fileSelectedInfo.classList.remove('hidden');
            fileSelectedInfo.classList.add('flex');

            // Validate file type
            if (!file.name.toLowerCase().endsWith('.csv')) {
              alert('Please select a CSV file');
              resetFileInput();
            }

            // Validate file size (5MB max)
            if (file.size > 5 * 1024 * 1024) {
              alert('File size exceeds 5MB limit');
              resetFileInput();
            }
          } else {
            resetFileInput();
          }
        });

        function resetFileInput() {
          fileInput.value = '';
          fileUploadPlaceholder.classList.remove('hidden');
          fileSelectedInfo.classList.add('hidden');
          fileSelectedInfo.classList.remove('flex');
        }
      }

      // User type selection
      const userTypeSelect = document.getElementById('{{ form.user_type.id_for_label }}');
      if (userTypeSelect) {
        // Style the select element
        userTypeSelect.classList.add('block', 'w-full', 'rounded-md', 'border-gray-300', 'dark:border-gray-600', 'dark:bg-gray-700', 'dark:text-white', 'shadow-sm', 'focus:border-blue-500', 'focus:ring-blue-500', 'sm:text-sm');

        // Handle user type change
        userTypeSelect.addEventListener('change', function() {
          console.log('User type changed to:', this.value);

          // You could update the UI based on the selected user type
          // For example, highlight the corresponding template download button
          const userType = this.value.toLowerCase();
          const templateButtons = document.querySelectorAll('[data-template-type]');

          templateButtons.forEach(button => {
            if (button.dataset.templateType === userType) {
              button.classList.add('ring-2', 'ring-blue-500');
            } else {
              button.classList.remove('ring-2', 'ring-blue-500');
            }
          });
        });
      }

      // Auto-hide messages after 5 seconds
      const messages = document.querySelectorAll('#messages-container > div');
      if (messages.length > 0) {
        setTimeout(() => {
          messages.forEach(message => {
            message.classList.add('opacity-0', 'transform', 'translate-y-2');
            setTimeout(() => {
              message.remove();
            }, 300);
          });
        }, 5000);
      }

      // Export functionality with loading states
      const exportButtons = document.querySelectorAll('a[href*="export-users"]');
      exportButtons.forEach(button => {
        button.addEventListener('click', function(e) {
          // Add loading state
          const originalContent = this.innerHTML;
          const userType = this.href.includes('type=student') ? 'Students' :
                          this.href.includes('type=doctor') ? 'Doctors' :
                          this.href.includes('type=staff') ? 'Staff' : 'All Users';

          this.innerHTML = `
            <i class="fas fa-spinner fa-spin mr-2"></i>
            <span>Exporting ${userType}...</span>
          `;
          this.classList.add('pointer-events-none', 'opacity-75');

          // Reset button after 3 seconds
          setTimeout(() => {
            this.innerHTML = originalContent;
            this.classList.remove('pointer-events-none', 'opacity-75');
          }, 3000);
        });
      });
    });
  </script>
{% endblock %}

