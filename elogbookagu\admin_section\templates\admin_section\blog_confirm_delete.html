{% extends 'base.html' %}

{% block title %}
  Delete Blog Post
{% endblock %}

{% block navbar %}
{% include 'components/admin_auth_navbar.html' %}
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
  <div class="max-w-2xl mx-auto">
    <!-- Back Button -->
    <a href="{% url 'admin_section:blog_detail' blog.id %}" class="inline-flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 mb-6">
      <svg class="w-5 h-5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
      </svg>
      Back to Blog Post
    </a>
    
    <!-- Confirmation Card -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
      <div class="p-6">
        <div class="flex items-center justify-center mb-6">
          <div class="bg-red-100 dark:bg-red-900/30 rounded-full p-3">
            <svg class="w-8 h-8 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
            </svg>
          </div>
        </div>
        
        <h1 class="text-2xl font-bold text-center text-gray-800 dark:text-white mb-4">Delete Blog Post</h1>
        <p class="text-center text-gray-600 dark:text-gray-400 mb-6">
          Are you sure you want to delete the blog post "<span class="font-semibold">{{ blog.title }}</span>"? This action cannot be undone.
        </p>
        
        <!-- Blog Info -->
        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6">
          <div class="flex items-start">
            {% if blog.featured_image %}
              <div class="flex-shrink-0 mr-4">
                <img src="{{ blog.featured_image.url }}" alt="{{ blog.title }}" class="h-16 w-16 object-cover rounded-md">
              </div>
            {% endif %}
            <div>
              <h3 class="font-medium text-gray-800 dark:text-white">{{ blog.title }}</h3>
              <p class="text-sm text-gray-500 dark:text-gray-400">{{ blog.summary|truncatechars:100 }}</p>
              <div class="mt-1 flex items-center text-xs text-gray-500 dark:text-gray-400">
                <span>Created on {{ blog.created_at|date:"M d, Y" }}</span>
                <span class="mx-2">•</span>
                <span>
                  {% for cat_value, cat_name in blog.CATEGORY_CHOICES %}
                    {% if cat_value == blog.category %}{{ cat_name }}{% endif %}
                  {% endfor %}
                </span>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Warning about attachments -->
        {% if blog.attachment or blog.featured_image %}
          <div class="bg-yellow-50 dark:bg-yellow-900/20 border-l-4 border-yellow-400 dark:border-yellow-600 p-4 mb-6">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-yellow-400 dark:text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                </svg>
              </div>
              <div class="ml-3">
                <p class="text-sm text-yellow-700 dark:text-yellow-200">
                  This blog post has 
                  {% if blog.attachment and blog.featured_image %}
                    an attachment and a featured image
                  {% elif blog.attachment %}
                    an attachment
                  {% else %}
                    a featured image
                  {% endif %}
                  that will also be deleted.
                </p>
              </div>
            </div>
          </div>
        {% endif %}
        
        <!-- Confirmation Form -->
        <form method="post" class="mt-6">
          {% csrf_token %}
          <div class="flex justify-center space-x-4">
            <a href="{% url 'admin_section:blog_detail' blog.id %}" class="px-4 py-2 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-lg transition duration-300">
              Cancel
            </a>
            <button type="submit" class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition duration-300">
              Delete Blog Post
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>
{% endblock %}
