{% extends 'base.html' %}

{% block title %}
  Doctor Help & Support
{% endblock %}

{% block navbar %}
  {% include './components/doc_auth_navbar.html' %}
{% endblock %}

{% block extra_head %}
<style>
  /* Optimize table rendering */
  table {
    contain: content;
    will-change: transform;
  }

  /* Optimize modal animations */
  .modal {
    backface-visibility: hidden;
    transform: translateZ(0);
    will-change: opacity, transform;
  }

  /* Optimize loading */
  .loading-spinner {
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
</style>
{% endblock %}

{% block content %}
  <div class="p-4 sm:p-6 md:p-8 lg:p-10 max-w-7xl mx-auto">
    <!-- Page Header with Title and Export Buttons -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
      <div>
        <h2 class="text-2xl sm:text-3xl font-bold text-gray-800 dark:text-white">
          {% if selected_status == 'pending' %}
            <span class="flex items-center">
              <i class="fas fa-clock text-yellow-500 mr-2"></i> Pending Review Logs
            </span>
          {% elif selected_status == 'approved' %}
            <span class="flex items-center">
              <i class="fas fa-check-circle text-green-500 mr-2"></i> Approved Logs
            </span>
          {% elif selected_status == 'rejected' %}
            <span class="flex items-center">
              <i class="fas fa-times-circle text-red-500 mr-2"></i> Rejected Logs
            </span>
          {% else %}
            <span class="flex items-center">
              <i class="fas fa-clipboard-check text-blue-500 mr-2"></i> All Student Logs
            </span>
          {% endif %}
        </h2>
        <p class="text-gray-600 dark:text-gray-400 mt-1">Review and approve student clinical activity logs</p>
      </div>

      <!-- Export Buttons -->
      <div class="flex space-x-2">
        <button id="export-csv" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 flex items-center transition-all duration-200 transform hover:scale-105">
          <i class="fas fa-file-csv mr-2"></i>
          Export CSV
        </button>
        <button id="export-pdf" class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 flex items-center transition-all duration-200 transform hover:scale-105">
          <i class="fas fa-file-pdf mr-2"></i>
          Export PDF
        </button>
      </div>
    </div>

    <!-- Doctor Info Card -->
    <div class="mb-6 bg-white dark:bg-gray-800 p-4 sm:p-6 rounded-xl shadow-md border border-gray-200 dark:border-gray-700 transition-all duration-300 hover:shadow-lg">
      <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
        <i class="fas fa-user-md text-blue-500 mr-2"></i> Doctor Information
      </h3>
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        <div class="flex items-center">
          <span class="text-gray-500 dark:text-gray-400 w-24">Name:</span>
          <span class="font-medium text-gray-800 dark:text-white">{{ request.user.get_full_name }}</span>
        </div>
        <div class="flex items-center">
          <span class="text-gray-500 dark:text-gray-400 w-24">Username:</span>
          <span class="font-medium text-gray-800 dark:text-white">{{ request.user.username }}</span>
        </div>
        <div class="flex items-center">
          <span class="text-gray-500 dark:text-gray-400 w-24">Departments:</span>
          <span class="font-medium text-gray-800 dark:text-white">
            {% for dept in departments %}
              {{ dept.name }}{% if not forloop.last %}, {% endif %}
            {% empty %}
              No departments assigned
            {% endfor %}
          </span>
        </div>
      </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
      <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow border border-gray-200 dark:border-gray-700">
        <div class="flex items-center">
          <div class="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
            <i class="fas fa-clipboard-list text-blue-600 dark:text-blue-400"></i>
          </div>
          <div class="ml-3">
            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Logs</p>
            <p class="text-lg font-semibold text-gray-900 dark:text-white">{{ stats.total }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow border border-gray-200 dark:border-gray-700">
        <div class="flex items-center">
          <div class="p-2 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
            <i class="fas fa-clock text-yellow-600 dark:text-yellow-400"></i>
          </div>
          <div class="ml-3">
            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Pending</p>
            <p class="text-lg font-semibold text-gray-900 dark:text-white">{{ stats.pending }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow border border-gray-200 dark:border-gray-700">
        <div class="flex items-center">
          <div class="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
            <i class="fas fa-check-circle text-green-600 dark:text-green-400"></i>
          </div>
          <div class="ml-3">
            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Approved</p>
            <p class="text-lg font-semibold text-gray-900 dark:text-white">{{ stats.approved }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-gray-800 p-4 rounded-lg shadow border border-gray-200 dark:border-gray-700">
        <div class="flex items-center">
          <div class="p-2 bg-red-100 dark:bg-red-900 rounded-lg">
            <i class="fas fa-times-circle text-red-600 dark:text-red-400"></i>
          </div>
          <div class="ml-3">
            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Rejected</p>
            <p class="text-lg font-semibold text-gray-900 dark:text-white">{{ stats.rejected }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Filters Section -->
    <div class="mb-6 bg-white dark:bg-gray-800 p-4 sm:p-6 rounded-xl shadow-md border border-gray-200 dark:border-gray-700 transition-all duration-300 hover:shadow-lg">
      <h3 class="text-lg font-semibold text-gray-800 dark:text-white mb-4 flex items-center">
        <i class="fas fa-filter text-blue-500 mr-2"></i> Filters & Search
      </h3>

      <form id="filterForm" method="get" class="space-y-4">
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <!-- Status Filter -->
          <div class="relative">
            <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Status</label>
            <div class="relative">
              <select name="status" id="status" class="block w-full pl-10 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 rounded-lg dark:bg-gray-700 dark:border-gray-600 dark:text-white transition-colors duration-200">
                <option value="all" {% if selected_status == 'all' %} selected{% endif %}>All Records</option>
                <option value="pending"{% if selected_status == 'pending' %} selected{% endif %}>Pending Review</option>
                <option value="approved"{% if selected_status == 'approved' %} selected{% endif %}>Approved</option>
                <option value="rejected"{% if selected_status == 'rejected' %} selected{% endif %}>Rejected</option>
              </select>
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <i class="fas fa-list-ul text-gray-400"></i>
              </div>
            </div>
          </div>

          <!-- Department Filter -->
          <div class="relative">
            <label for="department" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Department</label>
            <div class="relative">
              <select name="department" id="department" class="block w-full pl-10 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 rounded-lg dark:bg-gray-700 dark:border-gray-600 dark:text-white transition-colors duration-200">
                <option value="">All Departments</option>
                {% for dept in departments %}
                  <option value="{{ dept.id }}" {% if selected_department == dept.id|stringformat:"s" %}selected{% endif %}>
                    {{ dept.name }}
                  </option>
                {% endfor %}
              </select>
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <i class="fas fa-building text-gray-400"></i>
              </div>
            </div>
          </div>

          <!-- Search -->
          <div class="relative">
            <label for="q" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Search</label>
            <div class="relative">
              <input type="text" name="q" id="q" value="{{ search_query }}" placeholder="Search students, patient ID..." class="block w-full pl-10 pr-4 py-2 text-base border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 rounded-lg dark:bg-gray-700 dark:border-gray-600 dark:text-white transition-colors duration-200">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <i class="fas fa-search text-gray-400"></i>
              </div>
            </div>
          </div>

          <!-- Filter Actions -->
          <div class="flex items-end space-x-2">
            <button type="submit" class="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200 transform hover:scale-105">
              <i class="fas fa-search mr-2"></i>Apply
            </button>
            <a href="{% url 'doctor_section:doctor_reviews' %}" class="flex-1 bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg text-sm font-medium text-center focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-200 transform hover:scale-105">
              <i class="fas fa-undo mr-2"></i>Reset
            </a>
          </div>
        </div>

        <!-- Applied Filters Badges -->
        <div class="flex flex-wrap gap-2">
          {% if selected_status != 'all' %}
            <span class="px-2.5 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 flex items-center">
              Status: {{ selected_status|title }}
            </span>
          {% endif %}

          {% if selected_department %}
            <span class="px-2.5 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200 flex items-center">
              {% for dept in departments %}
                {% if selected_department == dept.id|stringformat:"s" %}
                  Dept: {{ dept.name }}
                {% endif %}
              {% endfor %}
            </span>
          {% endif %}

          {% if search_query %}
            <span class="px-2.5 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 flex items-center">
              Search: "{{ search_query }}"
            </span>
          {% endif %}
        </div>
      </form>
    </div>

    <!-- Quick Filter Buttons -->
    <div class="flex flex-wrap gap-2 mb-6">
      <a href="?status=all" class="inline-flex items-center px-3 py-2 rounded-full text-sm font-medium transition-all duration-200 {% if selected_status == 'all' %}bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100{% else %}bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600{% endif %}">
        <i class="fas fa-list mr-2"></i>All ({{ stats.total }})
      </a>
      <a href="?status=pending" class="inline-flex items-center px-3 py-2 rounded-full text-sm font-medium transition-all duration-200 {% if selected_status == 'pending' %}bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100{% else %}bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600{% endif %}">
        <i class="fas fa-clock mr-2"></i>Pending ({{ stats.pending }})
      </a>
      <a href="?status=approved" class="inline-flex items-center px-3 py-2 rounded-full text-sm font-medium transition-all duration-200 {% if selected_status == 'approved' %}bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100{% else %}bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600{% endif %}">
        <i class="fas fa-check-circle mr-2"></i>Approved ({{ stats.approved }})
      </a>
      <a href="?status=rejected" class="inline-flex items-center px-3 py-2 rounded-full text-sm font-medium transition-all duration-200 {% if selected_status == 'rejected' %}bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100{% else %}bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600{% endif %}">
        <i class="fas fa-times-circle mr-2"></i>Rejected ({{ stats.rejected }})
      </a>
    </div>

    <!-- Auto-assign Section (if needed) -->
    {% if show_auto_assign %}
    <div class="mb-6 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-xl p-6">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <i class="fas fa-exclamation-triangle text-yellow-600 text-xl"></i>
        </div>
        <div class="ml-3 flex-1">
          <h3 class="text-sm font-medium text-yellow-800 dark:text-yellow-300">Department Assignment Required</h3>
          <div class="mt-2 text-sm text-yellow-700 dark:text-yellow-400">
            <p>You are not assigned to any departments. Available departments:</p>
            <ul class="list-disc list-inside mt-1">
              {% for dept in available_departments %}
                <li>{{ dept.name }}</li>
              {% endfor %}
            </ul>
          </div>
          <div class="mt-4">
            <a href="?auto_assign=true" class="inline-flex items-center px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white text-sm font-medium rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 transition-all duration-200 transform hover:scale-105">
              <i class="fas fa-magic mr-2"></i>Auto-assign to All Departments
            </a>
          </div>
        </div>
      </div>
    </div>
    {% endif %}

    <!-- Records Table Card -->
    <div class="bg-white dark:bg-gray-800 shadow-md rounded-xl border border-gray-200 dark:border-gray-700 overflow-hidden transition-all duration-300 hover:shadow-lg">
      <!-- Table Header with Count -->
      <div class="bg-gray-50 dark:bg-gray-700 px-6 py-4 border-b border-gray-200 dark:border-gray-600 flex justify-between items-center">
        <h3 class="text-lg font-semibold text-gray-800 dark:text-white flex items-center">
          <i class="fas fa-clipboard-check text-blue-500 mr-2"></i>
          <span>Student Logs</span>
          {% if logs %}
            <span class="ml-2 px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
              {{ logs.paginator.count }}
            </span>
          {% endif %}
        </h3>
      </div>

      {% if logs %}
        <!-- Table Container with Loading State -->
        <div id="tableContainer" class="relative">
          <!-- Loading Overlay -->
          <div id="tableLoading" class="hidden absolute inset-0 bg-white bg-opacity-75 dark:bg-gray-800 dark:bg-opacity-75 z-10">
            <div class="flex items-center justify-center h-full">
              <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
            </div>
          </div>

          <!-- Table with Responsive Scroll -->
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead class="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th scope="col" class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Student</th>
                  <th scope="col" class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Date</th>
                  <th scope="col" class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Department</th>
                  <th scope="col" class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Activity Type</th>
                  <th scope="col" class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Core Diagnosis</th>
                  <th scope="col" class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                  <th scope="col" class="px-4 sm:px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {% for log in logs %}
                  <tr class="{% cycle 'bg-white' 'bg-gray-50' %} dark:bg-gray-800 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-150">
                    <td class="px-4 sm:px-6 py-4 whitespace-nowrap">
                      <div class="flex items-center">
                        <div class="flex-shrink-0 h-10 w-10">
                          <div class="h-10 w-10 rounded-full bg-blue-100 dark:bg-blue-800 flex items-center justify-center">
                            <i class="fas fa-user text-blue-600 dark:text-blue-300"></i>
                          </div>
                        </div>
                        <div class="ml-4">
                          <div class="text-sm font-medium text-gray-900 dark:text-white">
                            {{ log.student.user.get_full_name }}
                          </div>
                          <div class="text-sm text-gray-500 dark:text-gray-400">
                            ID: {{ log.student.student_id }}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td class="px-4 sm:px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">{{ log.date }}</td>
                    <td class="px-4 sm:px-6 py-4 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300">{{ log.department.name }}</td>
                    <td class="px-4 sm:px-6 py-4 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300">{{ log.activity_type.name }}</td>
                    <td class="px-4 sm:px-6 py-4 whitespace-nowrap text-sm text-gray-700 dark:text-gray-300">{{ log.core_diagnosis.name }}</td>
                    <td class="px-4 sm:px-6 py-4 whitespace-nowrap">
                      {% if log.is_reviewed %}
                        {% if log.reviewer_comments and log.reviewer_comments|lower|slice:":9" == 'rejected:' %}
                          <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                            <i class="fas fa-times-circle mr-1"></i> Rejected
                          </span>
                        {% else %}
                          <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                            <i class="fas fa-check-circle mr-1"></i> Approved
                          </span>
                        {% endif %}
                      {% else %}
                        <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                          <i class="fas fa-clock mr-1"></i> Pending
                        </span>
                      {% endif %}
                    </td>
                    <td class="px-4 sm:px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div class="flex items-center justify-end space-x-3">
                        <button type="button" onclick="showLogDetails({{ log.id }})" class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 transition-colors duration-200" title="View Details">
                          <i class="fas fa-eye"></i>
                        </button>
                        <a href="{% url 'doctor_section:review_log' log.id %}" class="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 transition-colors duration-200" title="Review Log">
                          <i class="fas fa-edit"></i>
                        </a>
                      </div>
                    </td>
                  </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>

      <!-- Pagination -->
      {% if logs.has_other_pages %}
      <div class="bg-white dark:bg-gray-800 px-4 py-3 flex items-center justify-between border-t border-gray-200 dark:border-gray-700 sm:px-6">
        <div class="flex-1 flex justify-between sm:hidden">
          {% if logs.has_previous %}
            <a href="?page={{ logs.previous_page_number }}{% if selected_department %}&department={{ selected_department }}{% endif %}{% if selected_status %}&status={{ selected_status }}{% endif %}{% if search_query %}&q={{ search_query }}{% endif %}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">Previous</a>
          {% endif %}
          {% if logs.has_next %}
            <a href="?page={{ logs.next_page_number }}{% if selected_department %}&department={{ selected_department }}{% endif %}{% if selected_status %}&status={{ selected_status }}{% endif %}{% if search_query %}&q={{ search_query }}{% endif %}" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">Next</a>
          {% endif %}
        </div>
        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
          <div>
            <p class="text-sm text-gray-700 dark:text-gray-300">
              Showing <span class="font-medium">{{ logs.start_index }}</span> to <span class="font-medium">{{ logs.end_index }}</span> of <span class="font-medium">{{ logs.paginator.count }}</span> results
            </p>
          </div>
          <div>
            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
              {% if logs.has_previous %}
                <a href="?page={{ logs.previous_page_number }}{% if selected_department %}&department={{ selected_department }}{% endif %}{% if selected_status %}&status={{ selected_status }}{% endif %}{% if search_query %}&q={{ search_query }}{% endif %}" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                  <span class="sr-only">Previous</span>
                  <i class="fas fa-chevron-left h-5 w-5"></i>
                </a>
              {% endif %}
              
              {% for i in logs.paginator.page_range %}
                {% if logs.number == i %}
                  <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-blue-50 text-sm font-medium text-blue-600">{{ i }}</span>
                {% elif i > logs.number|add:'-3' and i < logs.number|add:'3' %}
                  <a href="?page={{ i }}{% if selected_department %}&department={{ selected_department }}{% endif %}{% if selected_status %}&status={{ selected_status }}{% endif %}{% if search_query %}&q={{ search_query }}{% endif %}" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">{{ i }}</a>
                {% endif %}
              {% endfor %}
              
              {% if logs.has_next %}
                <a href="?page={{ logs.next_page_number }}{% if selected_department %}&department={{ selected_department }}{% endif %}{% if selected_status %}&status={{ selected_status }}{% endif %}{% if search_query %}&q={{ search_query }}{% endif %}" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                  <span class="sr-only">Next</span>
                  <i class="fas fa-chevron-right h-5 w-5"></i>
                </a>
              {% endif %}
            </nav>
          </div>
        </div>
      </div>
      {% endif %}
    </div>
    {% else %}
    <!-- Empty State -->
    <div class="text-center py-12">
      <div class="mx-auto h-24 w-24 text-gray-400">
        <i class="fas fa-clipboard-list text-6xl"></i>
      </div>
      <h3 class="mt-4 text-lg font-medium text-gray-900 dark:text-white">No logs found</h3>
      <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
        {% if search_query or selected_department or selected_status != 'all' %}
          No logs match your current filter criteria. Try adjusting your filters.
        {% else %}
          No student logs have been submitted yet.
        {% endif %}
      </p>
      {% if search_query or selected_department or selected_status != 'all' %}
      <div class="mt-6">
        <a href="{% url 'doctor_section:doctor_reviews' %}" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
          <i class="fas fa-undo mr-2"></i>Clear All Filters
        </a>
      </div>
      {% endif %}
    </div>
    {% endif %}

  </div>
</div>

<!-- JavaScript for Export Functionality -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Export functionality
    const exportCsvBtn = document.getElementById('export-csv');
    const exportPdfBtn = document.getElementById('export-pdf');

    if (exportCsvBtn) {
        exportCsvBtn.addEventListener('click', function() {
            const params = new URLSearchParams(window.location.search);
            params.set('format', 'csv');
            window.open(`{% url "doctor_section:export_logs" %}?${params.toString()}`, '_blank');
        });
    }

    if (exportPdfBtn) {
        exportPdfBtn.addEventListener('click', function() {
            const params = new URLSearchParams(window.location.search);
            params.set('format', 'pdf');
            window.open(`{% url "doctor_section:export_logs" %}?${params.toString()}`, '_blank');
        });
    }

    // Auto-submit form on filter change
    const departmentSelect = document.getElementById('department');
    const statusSelect = document.getElementById('status');

    if (departmentSelect) {
        departmentSelect.addEventListener('change', function() {
            this.form.submit();
        });
    }

    if (statusSelect) {
        statusSelect.addEventListener('change', function() {
            this.form.submit();
        });
    }

    // Search on Enter key
    const searchInput = document.getElementById('search');
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                this.form.submit();
            }
        });
    }
});
</script>
{% endblock %}
