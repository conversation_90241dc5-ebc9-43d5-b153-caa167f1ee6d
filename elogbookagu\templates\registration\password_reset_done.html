{% extends 'base.html' %}

{% block title %}Password Reset Email Sent - MedLogBook{% endblock %}

{% block extra_head %}
  <style>
    @keyframes float {
      0% { transform: translateY(0px); }
      50% { transform: translateY(-10px); }
      100% { transform: translateY(0px); }
    }

    .animate-float {
      animation: float 6s ease-in-out infinite;
    }

    .bg-pattern {
      background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    }
  </style>
{% endblock %}

{% block navbar %}
  {% include 'components/public_navbar.html' %}
{% endblock %}

{% block content_container %}
  <!-- Full-width Reset Password Section -->
  <section class="w-full min-h-screen bg-gradient-to-r from-blue-600 to-purple-700 dark:from-gray-800 dark:to-gray-900 bg-pattern py-16">
    <div class="container mx-auto px-4 sm:px-6 lg:px-8 flex justify-center items-center">
      <div class="max-w-md w-full">
        <!-- Success Card -->
        <div class="bg-white dark:bg-gray-800 rounded-xl shadow-2xl overflow-hidden">
          <!-- Card Header with Icon -->
          <div class="bg-green-50 dark:bg-green-900/30 p-6 text-center">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-green-100 dark:bg-green-800 rounded-full mb-4 animate-float">
              <i class="fas fa-envelope-open-text text-green-600 dark:text-green-400 text-2xl"></i>
            </div>
            <h2 class="text-2xl font-bold text-gray-900 dark:text-white">Check Your Email</h2>
            <p class="mt-2 text-gray-600 dark:text-gray-300">We've sent you instructions to reset your password.</p>
          </div>

          <!-- Content Section -->
          <div class="p-6">
            <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 text-blue-700 dark:text-blue-400 px-4 py-3 rounded-lg mb-6">
              <div class="flex">
                <div class="flex-shrink-0">
                  <i class="fas fa-info-circle text-blue-600 dark:text-blue-400 mt-0.5"></i>
                </div>
                <div class="ml-3">
                  <p class="text-sm">
                    If you don't receive an email within a few minutes, please check your spam folder or make sure you entered the correct email address.
                  </p>
                </div>
              </div>
            </div>

            <div class="space-y-4 text-gray-600 dark:text-gray-300">
              <p>
                We've emailed you instructions for setting a new password. You should receive the email shortly.
              </p>
              <p>
                The email contains a link that will allow you to securely reset your password. This link will expire in 24 hours for security reasons.
              </p>
            </div>

            <!-- Back to Login Link -->
            <div class="mt-8 text-center">
              <a href="{% url 'login' %}" class="inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-200">
                <i class="fas fa-sign-in-alt mr-2"></i> Return to Login
              </a>
            </div>
          </div>

          <!-- Support Section -->
          <div class="px-6 pb-6">
            <div class="border-t border-gray-200 dark:border-gray-700 pt-4 mt-2">
              <div class="flex items-start">
                <div class="flex-shrink-0 mt-1">
                  <i class="fas fa-headset text-gray-400"></i>
                </div>
                <p class="ml-3 text-xs text-gray-500 dark:text-gray-400">
                  If you continue to have problems, please contact our support team for assistance.
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- Help Text -->
        <div class="mt-6 text-center">
          <p class="text-sm text-white/80">
            Need help? <a href="#" class="font-medium text-white hover:text-white/90 underline">Contact Support</a>
          </p>
        </div>
      </div>
    </div>
  </section>
{% endblock %}
