{% extends 'base.html' %}
{% load static %}

{% block title %}Mapped Attendance{% endblock %}

{% block extra_head %}
<style>
  .table-container {
    overflow-x: auto;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  }
  
  .filter-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }
  
  .status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
  }
  
  .status-active {
    background-color: #dcfce7;
    color: #166534;
  }
  
  .status-inactive {
    background-color: #fef2f2;
    color: #991b1b;
  }
</style>
{% endblock %}

{% block navbar %}
  {% include 'components/admin_auth_navbar.html' %}
{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    
    <!-- Header Section -->
    <div class="mb-8">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Mapped Attendance</h1>
          <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
            Manage doctor and student group mappings to training sites
          </p>
        </div>
        <div class="mt-4 sm:mt-0">
          <a href="{% url 'admin_section:mapped_attendance_create' %}" 
             class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg shadow-sm transition-colors duration-200">
            <i class="fas fa-plus mr-2"></i>
            Create New Mapping
          </a>
        </div>
      </div>
    </div>

    <!-- Filters Section -->
    <div class="filter-card rounded-lg p-6 mb-8 text-white">
      <h3 class="text-lg font-semibold mb-4 flex items-center">
        <i class="fas fa-filter mr-2"></i>
        Filter Mappings
      </h3>
      
      <form method="get" class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <!-- Search -->
        <div>
          <label class="block text-sm font-medium mb-2">Search</label>
          <input type="text" name="search" value="{{ search_query }}" 
                 placeholder="Search by name or training site..."
                 class="w-full px-3 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500">
        </div>

        <!-- Log Year Filter -->
        <div>
          <label class="block text-sm font-medium mb-2">Log Year</label>
          <select name="log_year" class="w-full px-3 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500">
            <option value="">All Years</option>
            {% for year in log_years %}
              <option value="{{ year.id }}" {% if selected_log_year == year.id|stringformat:"s" %}selected{% endif %}>
                {{ year.year_name }}
              </option>
            {% endfor %}
          </select>
        </div>

        <!-- Training Site Filter -->
        <div>
          <label class="block text-sm font-medium mb-2">Training Site</label>
          <select name="training_site" class="w-full px-3 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500">
            <option value="">All Sites</option>
            {% for site in training_sites %}
              <option value="{{ site.id }}" {% if selected_training_site == site.id|stringformat:"s" %}selected{% endif %}>
                {{ site.name }}
              </option>
            {% endfor %}
          </select>
        </div>

        <!-- Status Filter -->
        <div>
          <label class="block text-sm font-medium mb-2">Status</label>
          <select name="is_active" class="w-full px-3 py-2 rounded-md border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500">
            <option value="">All Status</option>
            <option value="true" {% if selected_is_active == "true" %}selected{% endif %}>Active</option>
            <option value="false" {% if selected_is_active == "false" %}selected{% endif %}>Inactive</option>
          </select>
        </div>

        <!-- Filter Buttons -->
        <div class="md:col-span-4 flex gap-2">
          <button type="submit" class="px-4 py-2 bg-white text-blue-600 rounded-md hover:bg-gray-100 transition-colors duration-200 font-medium">
            <i class="fas fa-search mr-2"></i>Apply Filters
          </button>
          <a href="{% url 'admin_section:mapped_attendance_list' %}" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors duration-200 font-medium">
            <i class="fas fa-times mr-2"></i>Clear
          </a>
        </div>
      </form>
    </div>

    <!-- Results Section -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
          Mapped Attendance Records
          {% if page_obj.paginator.count %}
            <span class="text-sm font-normal text-gray-500 dark:text-gray-400">
              ({{ page_obj.paginator.count }} total)
            </span>
          {% endif %}
        </h3>
      </div>

      {% if page_obj %}
        <div class="table-container">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Mapping Name
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Training Site
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Log Year
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Doctors
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Groups
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Status
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {% for mapping in page_obj %}
                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm font-medium text-gray-900 dark:text-white">
                      {{ mapping.name }}
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900 dark:text-white">
                      {{ mapping.training_site.name }}
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900 dark:text-white">
                      {{ mapping.log_year.year_name }}
                      {% if mapping.log_year_section %}
                        <br><span class="text-xs text-gray-500">{{ mapping.log_year_section.year_section_name }}</span>
                      {% endif %}
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900 dark:text-white">
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {{ mapping.doctors.count }} doctor{{ mapping.doctors.count|pluralize }}
                      </span>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900 dark:text-white">
                      <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        {{ mapping.groups.count }} group{{ mapping.groups.count|pluralize }}
                      </span>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    {% if mapping.is_active %}
                      <span class="status-badge status-active">
                        <i class="fas fa-check-circle mr-1"></i>Active
                      </span>
                    {% else %}
                      <span class="status-badge status-inactive">
                        <i class="fas fa-times-circle mr-1"></i>Inactive
                      </span>
                    {% endif %}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div class="flex space-x-2">
                      <a href="{% url 'admin_section:mapped_attendance_detail' mapping.pk %}" 
                         class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300" 
                         title="View Details">
                        <i class="fas fa-eye"></i>
                      </a>
                      <a href="{% url 'admin_section:mapped_attendance_edit' mapping.pk %}" 
                         class="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300" 
                         title="Edit">
                        <i class="fas fa-edit"></i>
                      </a>
                      <a href="{% url 'admin_section:mapped_attendance_delete' mapping.pk %}" 
                         class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300" 
                         title="Delete">
                        <i class="fas fa-trash"></i>
                      </a>
                    </div>
                  </td>
                </tr>
              {% empty %}
                <tr>
                  <td colspan="7" class="px-6 py-12 text-center">
                    <div class="text-gray-500 dark:text-gray-400">
                      <i class="fas fa-inbox text-4xl mb-4"></i>
                      <p class="text-lg font-medium">No mapped attendance records found</p>
                      <p class="text-sm">Create your first mapping to get started.</p>
                    </div>
                  </td>
                </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>

        <!-- Pagination -->
        {% if page_obj.has_other_pages %}
          <div class="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
            <div class="flex items-center justify-between">
              <div class="text-sm text-gray-700 dark:text-gray-300">
                Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} results
              </div>
              <div class="flex space-x-2">
                {% if page_obj.has_previous %}
                  <a href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_log_year %}&log_year={{ selected_log_year }}{% endif %}{% if selected_training_site %}&training_site={{ selected_training_site }}{% endif %}{% if selected_is_active %}&is_active={{ selected_is_active }}{% endif %}" 
                     class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700">
                    Previous
                  </a>
                {% endif %}
                
                {% for num in page_obj.paginator.page_range %}
                  {% if page_obj.number == num %}
                    <span class="px-3 py-2 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-300 rounded-md dark:bg-blue-900 dark:border-blue-600 dark:text-blue-300">
                      {{ num }}
                    </span>
                  {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                    <a href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_log_year %}&log_year={{ selected_log_year }}{% endif %}{% if selected_training_site %}&training_site={{ selected_training_site }}{% endif %}{% if selected_is_active %}&is_active={{ selected_is_active }}{% endif %}" 
                       class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700">
                      {{ num }}
                    </a>
                  {% endif %}
                {% endfor %}
                
                {% if page_obj.has_next %}
                  <a href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if selected_log_year %}&log_year={{ selected_log_year }}{% endif %}{% if selected_training_site %}&training_site={{ selected_training_site }}{% endif %}{% if selected_is_active %}&is_active={{ selected_is_active }}{% endif %}" 
                     class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 dark:bg-gray-800 dark:border-gray-600 dark:text-gray-300 dark:hover:bg-gray-700">
                    Next
                  </a>
                {% endif %}
              </div>
            </div>
          </div>
        {% endif %}
      {% else %}
        <div class="px-6 py-12 text-center">
          <div class="text-gray-500 dark:text-gray-400">
            <i class="fas fa-inbox text-4xl mb-4"></i>
            <p class="text-lg font-medium">No mapped attendance records found</p>
            <p class="text-sm">Create your first mapping to get started.</p>
          </div>
        </div>
      {% endif %}
    </div>
  </div>
</div>
{% endblock %}
