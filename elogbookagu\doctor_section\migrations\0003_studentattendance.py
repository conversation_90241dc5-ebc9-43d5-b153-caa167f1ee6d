# Generated by Django 5.1.4 on 2025-06-02 09:00

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('accounts', '0008_alter_staff_departments'),
        ('admin_section', '0008_mappedattendance'),
        ('doctor_section', '0002_notification'),
    ]

    operations = [
        migrations.CreateModel(
            name='StudentAttendance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('status', models.CharField(choices=[('present', 'Present'), ('absent', 'Absent')], max_length=10)),
                ('marked_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('notes', models.TextField(blank=True, help_text='Optional notes about attendance')),
                ('doctor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='marked_attendances', to='accounts.doctor')),
                ('group', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attendances', to='admin_section.group')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attendances', to='accounts.student')),
                ('training_site', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attendances', to='admin_section.trainingsite')),
            ],
            options={
                'verbose_name': 'Student Attendance',
                'verbose_name_plural': 'Student Attendances',
                'ordering': ['-date', '-marked_at'],
                'unique_together': {('student', 'date', 'training_site')},
            },
        ),
    ]
