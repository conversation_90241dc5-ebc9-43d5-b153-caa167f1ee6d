{% extends 'base.html' %}

{% block title %}
  Login - ElogBook
{% endblock %}
{% block navbar %}
  {% include './components/public_navbar.html' %}
{% endblock %}

{% block extra_head %}
  <style>
    @keyframes float {
      0% { transform: translateY(0px); }
      50% { transform: translateY(-10px); }
      100% { transform: translateY(0px); }
    }

    .animate-float {
      animation: float 4s ease-in-out infinite;
    }

    .login-card {
      backdrop-filter: blur(10px);
      transition: transform 0.3s ease;
    }

    .login-card:hover {
      transform: translateY(-5px);
    }

    .decorative-shape {
      position: absolute;
      border-radius: 50%;
      background: linear-gradient(45deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1));
      animation: float 6s ease-in-out infinite;
    }

    .input-group {
      transition: all 0.3s ease;
    }

    .input-group:focus-within {
      transform: translateY(-2px);
    }

    .social-login-btn {
      transition: all 0.3s ease;
    }

    .social-login-btn:hover {
      transform: translateY(-2px);
    }
  </style>
{% endblock %}

{% block content_container %}
<div class="min-h-screen flex flex-col justify-center relative overflow-hidden bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-purple-900 py-12 sm:px-6 lg:px-8">
  <!-- Decorative Shapes -->
  <div class="decorative-shape w-72 h-72 -top-20 -left-20 opacity-30"></div>
  <div class="decorative-shape w-96 h-96 -bottom-32 -right-32 opacity-20"></div>
  <div class="decorative-shape w-48 h-48 top-1/4 right-1/4 opacity-25"></div>

  <!-- Main Content -->
  <div class="relative max-w-md w-full mx-auto px-4">
    <div class="login-card bg-white/80 dark:bg-gray-800/80 p-8 rounded-2xl shadow-2xl space-y-6 border border-gray-200/50 dark:border-gray-700/50">
      <!-- Logo and Title -->
      <div class="text-center space-y-4">
        <div class="flex justify-center">
          <div class="rounded-full bg-gradient-to-r from-blue-100 to-purple-100 dark:from-blue-900 dark:to-purple-900 p-4 inline-flex animate-float">
            <i class="fas fa-book-medical text-blue-600 dark:text-blue-400 text-3xl"></i>
          </div>
        </div>
        <h1 class="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-purple-600 dark:from-blue-400 dark:to-purple-400">Welcome Back</h1>
        <p class="text-gray-600 dark:text-gray-300">Sign in to your ElogBook account</p>

        {% if messages %}
          <div class="mt-4 space-y-2">
            {% for message in messages %}
              <div class="p-3 rounded-lg backdrop-blur-sm {% if message.tags == 'error' %}bg-red-100/90 text-red-700 dark:bg-red-900/30 dark:text-red-400{% else %}bg-blue-100/90 text-blue-700 dark:bg-blue-900/30 dark:text-blue-400{% endif %} shadow-sm">
                {{ message }}
              </div>
            {% endfor %}
          </div>
        {% endif %}
      </div>

      <!-- Login Form -->
      <form method="post" class="space-y-6">
        {% csrf_token %}
        <div class="space-y-5">
          <!-- Email Input -->
          <div class="input-group">
            <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Email Address</label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              
              </div>
              <input type="email" id="email" name="email" class="w-full pl-10 pr-4 py-2.5 bg-white/50 dark:bg-gray-700/50 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="<EMAIL>" required autocomplete="off" />
            </div>
          </div>

          <!-- Password Input -->
          <div class="input-group">
            <div class="flex items-center justify-between mb-1">
              <label for="password" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Password</label>
              <a href="{% url 'password_reset' %}" class="text-xs text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors">Forgot Password?</a>
            </div>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
               
              </div>
              <input type="password" id="password" name="password" class="w-full pl-10 pr-4 py-2.5 bg-white/50 dark:bg-gray-700/50 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500" placeholder="••••••••" required autocomplete="off" />
            </div>
          </div>
        </div>

        <!-- Submit Button -->
        <button type="submit" class="w-full flex justify-center items-center bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium py-2.5 px-4 rounded-lg transition duration-300 transform hover:scale-[1.02] focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 shadow-lg">
          <i class="fas fa-sign-in-alt mr-2"></i> Sign In
        </button>

        <!-- Divider -->
        <div class="relative">
          <div class="absolute inset-0 flex items-center">
            <div class="w-full border-t border-gray-200 dark:border-gray-700"></div>
          </div>
          <div class="relative flex justify-center text-sm">
            <span class="px-2 bg-white/80 dark:bg-gray-800/80 text-gray-500 dark:text-gray-400">Or</span>
          </div>
        </div>
      </form>

      <!-- SSO Login -->
      <div class="text-center space-y-6">
        <a href="#" class="social-login-btn inline-flex items-center justify-center w-full px-4 py-2.5 bg-white dark:bg-gray-700 text-gray-800 dark:text-white font-medium border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-300 shadow-md">
          <img src="https://img.icons8.com/color/48/000000/microsoft.png" class="w-5 h-5 mr-2" alt="Microsoft" />
          <span>Continue with Microsoft Office 365</span>
        </a>

        <p class="text-xs text-gray-500 dark:text-gray-400">
          By signing in, you agree to our
          <a href="#" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors">Terms of Service</a> and
          <a href="#" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 transition-colors">Privacy Policy</a>
        </p>
      </div>
    </div>
  </div>
</div>



<script>
  document.addEventListener('DOMContentLoaded', function () {
    // Clear form fields on page load
    document.querySelector('input[name="email"]').value = ''
    document.querySelector('input[name="password"]').value = ''

    // Add focus effect to input fields
    const inputs = document.querySelectorAll('input[type="email"], input[type="password"]')
    inputs.forEach(input => {
      input.addEventListener('focus', function() {
        this.parentElement.classList.add('ring-2', 'ring-blue-100', 'dark:ring-blue-900')
      })
      input.addEventListener('blur', function() {
        this.parentElement.classList.remove('ring-2', 'ring-blue-100', 'dark:ring-blue-900')
      })
    })
  })
</script>
{% endblock %}
