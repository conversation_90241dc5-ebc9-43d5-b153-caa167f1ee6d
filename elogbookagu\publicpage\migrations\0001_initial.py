# Generated by Django 5.1.4 on 2025-02-04 08:10

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='LogYear',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('year_name', models.CharField(max_length=20, unique=True)),
            ],
        ),
        migrations.CreateModel(
            name='LogYearSection',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('year_section_name', models.CharField(max_length=20)),
                ('is_deleted', models.BooleanField(default=False)),
                ('year_name', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='log_year_sections', to='publicpage.logyear')),
            ],
        ),
        migrations.CreateModel(
            name='Group',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('group_name', models.CharField(max_length=50)),
                ('log_year', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='groups_log_year', to='publicpage.logyear')),
                ('log_year_section', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='groups_log_year_section', to='publicpage.logyearsection')),
            ],
        ),
        migrations.CreateModel(
            name='Department',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50)),
                ('log_year', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='department_log_year', to='publicpage.logyear')),
                ('log_year_section', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='department_log_year_section', to='publicpage.logyearsection')),
            ],
        ),
        migrations.CreateModel(
            name='TrainingSite',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('log_year', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='training_sites', to='publicpage.logyear')),
            ],
        ),
    ]
