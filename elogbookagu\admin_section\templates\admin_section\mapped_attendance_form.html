{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_head %}
<style>
  .form-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }
  
  .checkbox-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 0.75rem;
    max-height: 300px;
    overflow-y: auto;
    padding: 1rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    background-color: #f9fafb;
  }
  
  .checkbox-item {
    display: flex;
    align-items: center;
    padding: 0.5rem;
    border-radius: 0.375rem;
    background-color: white;
    border: 1px solid #e5e7eb;
    transition: all 0.2s;
  }
  
  .checkbox-item:hover {
    background-color: #f3f4f6;
    border-color: #3b82f6;
  }
  
  .checkbox-item input[type="checkbox"] {
    margin-right: 0.5rem;
  }
  
  .dark .checkbox-grid {
    background-color: #374151;
    border-color: #4b5563;
  }
  
  .dark .checkbox-item {
    background-color: #4b5563;
    border-color: #6b7280;
    color: white;
  }
  
  .dark .checkbox-item:hover {
    background-color: #6b7280;
    border-color: #3b82f6;
  }
</style>
{% endblock %}

{% block navbar %}
  {% include 'components/admin_auth_navbar.html' %}
{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
    
    <!-- Header Section -->
    <div class="form-container rounded-lg p-6 mb-8 text-white">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold">{{ title }}</h1>
          <p class="mt-2 text-blue-100">
            Map doctors and student groups to training sites for attendance management
          </p>
        </div>
        <div>
          <a href="{% url 'admin_section:mapped_attendance_list' %}" 
             class="inline-flex items-center px-4 py-2 bg-white text-blue-600 rounded-lg hover:bg-gray-100 transition-colors duration-200 font-medium">
            <i class="fas fa-arrow-left mr-2"></i>
            Back to List
          </a>
        </div>
      </div>
    </div>

    <!-- Form Section -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
          Mapping Details
        </h3>
      </div>

      <form method="post" class="p-6 space-y-6">
        {% csrf_token %}
        
        {% if form.non_field_errors %}
          <div class="bg-red-50 border border-red-200 rounded-md p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <i class="fas fa-exclamation-circle text-red-400"></i>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">
                  Please correct the following errors:
                </h3>
                <div class="mt-2 text-sm text-red-700">
                  {{ form.non_field_errors }}
                </div>
              </div>
            </div>
          </div>
        {% endif %}

        <!-- Basic Information -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Mapping Name -->
          <div>
            <label for="{{ form.name.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Mapping Name <span class="text-red-500">*</span>
            </label>
            {{ form.name }}
            {% if form.name.errors %}
              <p class="mt-1 text-sm text-red-600">{{ form.name.errors.0 }}</p>
            {% endif %}
            <p class="mt-1 text-sm text-gray-500">Enter a descriptive name for this mapping</p>
          </div>

          <!-- Log Year -->
          <div>
            <label for="{{ form.log_year.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Log Year <span class="text-red-500">*</span>
            </label>
            {{ form.log_year }}
            {% if form.log_year.errors %}
              <p class="mt-1 text-sm text-red-600">{{ form.log_year.errors.0 }}</p>
            {% endif %}
          </div>

          <!-- Log Year Section -->
          <div>
            <label for="{{ form.log_year_section.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Log Year Section
            </label>
            {{ form.log_year_section }}
            {% if form.log_year_section.errors %}
              <p class="mt-1 text-sm text-red-600">{{ form.log_year_section.errors.0 }}</p>
            {% endif %}
          </div>

          <!-- Training Site -->
          <div>
            <label for="{{ form.training_site.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Training Site <span class="text-red-500">*</span>
            </label>
            {{ form.training_site }}
            {% if form.training_site.errors %}
              <p class="mt-1 text-sm text-red-600">{{ form.training_site.errors.0 }}</p>
            {% endif %}
          </div>
        </div>

        <!-- Status -->
        <div>
          <label class="flex items-center">
            {{ form.is_active }}
            <span class="ml-2 text-sm font-medium text-gray-700 dark:text-gray-300">
              Active Mapping
            </span>
          </label>
          {% if form.is_active.errors %}
            <p class="mt-1 text-sm text-red-600">{{ form.is_active.errors.0 }}</p>
          {% endif %}
          <p class="mt-1 text-sm text-gray-500">Uncheck to deactivate this mapping</p>
        </div>

        <!-- Department Filter for Doctors -->
        <div>
          <label for="departmentFilter" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Filter Doctors by Department (Optional)
          </label>
          <select id="departmentFilter" class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
            <option value="">All Departments</option>
            {% for department in departments %}
              <option value="{{ department.id }}">{{ department.name }}</option>
            {% endfor %}
          </select>
          <p class="mt-1 text-sm text-gray-500">Filter doctors by their assigned department to make selection easier</p>
        </div>

        <!-- Doctors Selection -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Select Doctors
          </label>
          {% if form.doctors.errors %}
            <p class="mb-2 text-sm text-red-600">{{ form.doctors.errors.0 }}</p>
          {% endif %}
          <div id="doctorsContainer" class="checkbox-grid">
            {% for choice in form.doctors %}
              <div class="checkbox-item">
                {{ choice.tag }}
                <label for="{{ choice.id_for_label }}" class="text-sm font-medium cursor-pointer">
                  {{ choice.choice_label }}
                </label>
              </div>
            {% empty %}
              <div class="col-span-full text-center text-gray-500 py-4">
                No doctors available. Please add doctors first.
              </div>
            {% endfor %}
          </div>
          <p class="mt-2 text-sm text-gray-500">Select doctors who will be mapped to this training site</p>
        </div>

        <!-- Groups Selection -->
        <div>
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Select Student Groups
          </label>
          {% if form.groups.errors %}
            <p class="mb-2 text-sm text-red-600">{{ form.groups.errors.0 }}</p>
          {% endif %}
          <div class="checkbox-grid">
            {% for choice in form.groups %}
              <div class="checkbox-item">
                {{ choice.tag }}
                <label for="{{ choice.id_for_label }}" class="text-sm font-medium cursor-pointer">
                  {{ choice.choice_label }}
                </label>
              </div>
            {% empty %}
              <div class="col-span-full text-center text-gray-500 py-4">
                No groups available for the selected log year. Please select a log year first.
              </div>
            {% endfor %}
          </div>
          <p class="mt-2 text-sm text-gray-500">Select student groups that will be mapped to this training site</p>
        </div>

        <!-- Form Actions -->
        <div class="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200 dark:border-gray-700">
          <a href="{% url 'admin_section:mapped_attendance_list' %}" 
             class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-600 transition-colors duration-200">
            Cancel
          </a>
          <button type="submit" 
                  class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
            <i class="fas fa-save mr-2"></i>
            {{ submit_text }}
          </button>
        </div>
      </form>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const logYearSelect = document.getElementById('{{ form.log_year.id_for_label }}');
    const trainingSelect = document.getElementById('{{ form.training_site.id_for_label }}');
    const groupsContainer = document.querySelector('.checkbox-grid');
    const departmentFilter = document.getElementById('departmentFilter');
    const doctorsContainer = document.getElementById('doctorsContainer');

    // Store original doctors for filtering
    let allDoctors = [];
    let selectedDoctorIds = [];

    // Initialize doctors data
    function initializeDoctors() {
        const checkboxes = doctorsContainer.querySelectorAll('input[type="checkbox"]');
        allDoctors = Array.from(checkboxes).map(checkbox => {
            const label = checkbox.nextElementSibling;
            return {
                id: checkbox.value,
                name: label.textContent.trim(),
                checked: checkbox.checked,
                element: checkbox.closest('.checkbox-item')
            };
        });

        // Store initially selected doctors
        selectedDoctorIds = allDoctors.filter(doctor => doctor.checked).map(doctor => doctor.id);
    }

    // Update doctors display based on department filter
    function updateDoctorsDisplay(doctors) {
        doctorsContainer.innerHTML = '';

        if (doctors.length === 0) {
            doctorsContainer.innerHTML = '<div class="col-span-full text-center text-gray-500 py-4">No doctors available for the selected department.</div>';
            return;
        }

        doctors.forEach(doctor => {
            const checkboxItem = document.createElement('div');
            checkboxItem.className = 'checkbox-item';

            const isChecked = selectedDoctorIds.includes(doctor.id.toString());

            checkboxItem.innerHTML = `
                <input type="checkbox"
                       name="doctors"
                       value="${doctor.id}"
                       id="id_doctors_${doctor.id}"
                       ${isChecked ? 'checked' : ''}
                       class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                <label for="id_doctors_${doctor.id}" class="text-sm font-medium cursor-pointer">
                    ${doctor.name}
                </label>
            `;

            doctorsContainer.appendChild(checkboxItem);

            // Add event listener to track selections
            const checkbox = checkboxItem.querySelector('input[type="checkbox"]');
            checkbox.addEventListener('change', function() {
                if (this.checked) {
                    if (!selectedDoctorIds.includes(this.value)) {
                        selectedDoctorIds.push(this.value);
                    }
                } else {
                    selectedDoctorIds = selectedDoctorIds.filter(id => id !== this.value);
                }
            });
        });
    }

    // Department filter functionality
    if (departmentFilter) {
        // Initialize doctors on page load
        initializeDoctors();

        departmentFilter.addEventListener('change', function() {
            const departmentId = this.value;

            if (departmentId) {
                // Fetch doctors for selected department
                fetch(`{% url 'admin_section:get_doctors_by_department' %}?department_id=${departmentId}`)
                    .then(response => response.json())
                    .then(data => {
                        updateDoctorsDisplay(data.doctors);
                    })
                    .catch(error => {
                        console.error('Error fetching doctors:', error);
                        doctorsContainer.innerHTML = '<div class="col-span-full text-center text-red-500 py-4">Error loading doctors. Please try again.</div>';
                    });
            } else {
                // Show all doctors
                fetch(`{% url 'admin_section:get_doctors_by_department' %}`)
                    .then(response => response.json())
                    .then(data => {
                        updateDoctorsDisplay(data.doctors);
                    })
                    .catch(error => {
                        console.error('Error fetching doctors:', error);
                        doctorsContainer.innerHTML = '<div class="col-span-full text-center text-red-500 py-4">Error loading doctors. Please try again.</div>';
                    });
            }
        });
    }

    // Log Year functionality (existing)
    if (logYearSelect) {
        logYearSelect.addEventListener('change', function() {
            const logYearId = this.value;

            // Update training sites
            if (logYearId) {
                fetch(`{% url 'admin_section:get_training_sites_by_year' %}?log_year_id=${logYearId}`)
                    .then(response => response.json())
                    .then(data => {
                        trainingSelect.innerHTML = '<option value="">Select Training Site</option>';
                        data.training_sites.forEach(site => {
                            const option = document.createElement('option');
                            option.value = site.id;
                            option.textContent = site.name;
                            trainingSelect.appendChild(option);
                        });
                    })
                    .catch(error => console.error('Error fetching training sites:', error));

                // Update groups
                fetch(`{% url 'admin_section:get_groups_by_year' %}?log_year_id=${logYearId}`)
                    .then(response => response.json())
                    .then(data => {
                        // This would need to be implemented to update the groups checkboxes
                        // For now, we'll show a message to refresh the page
                        if (data.groups.length === 0) {
                            groupsContainer.innerHTML = '<div class="col-span-full text-center text-gray-500 py-4">No groups available for the selected log year.</div>';
                        }
                    })
                    .catch(error => console.error('Error fetching groups:', error));
            } else {
                trainingSelect.innerHTML = '<option value="">Select Training Site</option>';
                groupsContainer.innerHTML = '<div class="col-span-full text-center text-gray-500 py-4">Please select a log year first.</div>';
            }
        });
    }
});
</script>
{% endblock %}
