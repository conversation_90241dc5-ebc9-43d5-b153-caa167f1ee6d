# Generated by Django 5.1.4 on 2025-06-02 09:50

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('publicpage', '0002_remove_group_log_year_remove_group_log_year_section_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='PageVisit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('page_name', models.CharField(help_text='Name of the page visited', max_length=100)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user_agent', models.TextField(blank=True, help_text='Browser user agent')),
                ('visited_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('is_unique', models.<PERSON><PERSON>an<PERSON>ield(default=True, help_text='Whether this is a unique visit from this IP')),
            ],
            options={
                'verbose_name': 'Page Visit',
                'verbose_name_plural': 'Page Visits',
                'ordering': ['-visited_at'],
            },
        ),
    ]
