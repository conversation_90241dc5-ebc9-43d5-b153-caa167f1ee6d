# Generated by Django 5.1.4 on 2025-05-12 06:55

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('admin_section', '0006_merge_0002_blog_0005_blog'),
    ]

    operations = [
        migrations.AddField(
            model_name='daterestrictionsettings',
            name='doctor_notification_days',
            field=models.PositiveIntegerField(default=3, help_text='Number of days before deadline to send notification to doctors'),
        ),
        migrations.AddField(
            model_name='daterestrictionsettings',
            name='doctor_review_enabled',
            field=models.BooleanField(default=True, help_text='Whether to enforce the review period deadline'),
        ),
        migrations.AddField(
            model_name='daterestrictionsettings',
            name='doctor_review_period',
            field=models.PositiveIntegerField(default=30, help_text='Number of days doctors have to review student logs'),
        ),
    ]
