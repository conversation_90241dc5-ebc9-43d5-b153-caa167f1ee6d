{% extends 'base.html' %}
{% block title %}
  Admin Support Page
{% endblock %}

{% block navbar %}
{% include 'components/admin_auth_navbar.html' %}
{% endblock %}


<!-- Main Content -->

{% block content %}
  <div class="container mx-auto px-4 py-8">
    <div class="text-center mb-8">
      <h2 class="text-3xl font-bold text-gray-800 dark:text-white">Support Tickets</h2>
      <p class="text-gray-600 dark:text-gray-300 mt-2">Manage and respond to support requests</p>
    </div>

    <!-- Messages section -->
    {% if messages %}
      <div class="mb-6">
        {% for message in messages %}
          <div class="p-4 mb-4 rounded-lg {% if message.tags == 'success' %}bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100{% elif message.tags == 'error' %}bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100{% else %}bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100{% endif %}">
            {{ message }}
          </div>
        {% endfor %}
      </div>
    {% endif %}

    <!-- Ticket Type Controls -->
    <div class="mb-4 flex flex-wrap gap-4">
      <a href="{% url 'admin_section:admin_support' %}?type=student{% if status_filter %}&status={{ status_filter }}{% endif %}" class="px-4 py-2 rounded-lg {% if ticket_type == 'student' %}bg-blue-600 text-white{% else %}bg-gray-200 text-gray-800 dark:bg-gray-700 dark:text-white{% endif %} hover:bg-blue-700 hover:text-white transition-colors duration-200">
        Student Tickets
      </a>
      <a href="{% url 'admin_section:admin_support' %}?type=doctor{% if status_filter %}&status={{ status_filter }}{% endif %}" class="px-4 py-2 rounded-lg {% if ticket_type == 'doctor' %}bg-purple-600 text-white{% else %}bg-gray-200 text-gray-800 dark:bg-gray-700 dark:text-white{% endif %} hover:bg-purple-700 hover:text-white transition-colors duration-200">
        Doctor Tickets
      </a>
    </div>

    <!-- Status Filter Controls -->
    <div class="mb-6 flex flex-wrap gap-4">
      <a href="{% url 'admin_section:admin_support' %}?type={{ ticket_type }}" class="px-4 py-2 rounded-lg {% if not status_filter %}bg-blue-600 text-white{% else %}bg-gray-200 text-gray-800 dark:bg-gray-700 dark:text-white{% endif %} hover:bg-blue-700 hover:text-white transition-colors duration-200">
        All Status
      </a>
      <a href="{% url 'admin_section:admin_support' %}?type={{ ticket_type }}&status=pending" class="px-4 py-2 rounded-lg {% if status_filter == 'pending' %}bg-yellow-500 text-white{% else %}bg-gray-200 text-gray-800 dark:bg-gray-700 dark:text-white{% endif %} hover:bg-yellow-600 hover:text-white transition-colors duration-200">
        Pending
      </a>
      <a href="{% url 'admin_section:admin_support' %}?type={{ ticket_type }}&status=solved" class="px-4 py-2 rounded-lg {% if status_filter == 'solved' %}bg-green-600 text-white{% else %}bg-gray-200 text-gray-800 dark:bg-gray-700 dark:text-white{% endif %} hover:bg-green-700 hover:text-white transition-colors duration-200">
        Solved
      </a>
    </div>

    <!-- Support Tickets Table -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
      {% if tickets %}
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead class="bg-gray-50 dark:bg-gray-700">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">User</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Subject</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Date</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            {% for ticket in tickets %}
              <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                <td class="px-6 py-4 whitespace-nowrap">
                  {% if ticket_type == 'student' %}
                    <div class="text-sm font-medium text-gray-900 dark:text-white">{{ ticket.student.user.get_full_name }}</div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">{{ ticket.student.student_id }}</div>
                  {% else %}
                    <div class="text-sm font-medium text-gray-900 dark:text-white">Dr. {{ ticket.doctor.user.get_full_name }}</div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">Doctor</div>
                  {% endif %}
                </td>
                <td class="px-6 py-4">
                  <div class="text-sm text-gray-900 dark:text-white">{{ ticket.subject }}</div>
                  <div class="text-sm text-gray-500 dark:text-gray-400 truncate max-w-xs">{{ ticket.description|truncatechars:50 }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">{{ ticket.date_created|date:"M d, Y" }}</td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {% if ticket.status == 'solved' %}bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100{% else %}bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100{% endif %}">
                    {{ ticket.status|title }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <a href="{% url 'admin_section:resolve_ticket' ticket.id %}?type={{ ticket_type }}" class="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300">
                    {% if ticket.status == 'pending' %}Resolve{% else %}View/Edit{% endif %}
                  </a>
                </td>
              </tr>
            {% endfor %}
          </tbody>
        </table>
      {% else %}
        <div class="text-center py-8">
          <p class="text-gray-500 dark:text-gray-400">No support tickets found.</p>
          {% if status_filter %}
            <p class="text-gray-500 dark:text-gray-400 mt-2">Try removing filters to see all tickets.</p>
          {% endif %}
        </div>
      {% endif %}
    </div>
  </div>
{% endblock %}
