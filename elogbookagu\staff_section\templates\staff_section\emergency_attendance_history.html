{% extends 'base.html' %}
{% load static %}

{% block title %}Emergency Attendance History{% endblock %}

{% block extra_head %}
<style>
  .emergency-card {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  }
  
  .student-avatar {
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .student-avatar:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }
  
  .avatar-initials {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  }
  
  .status-badge {
    transition: all 0.3s ease;
  }
  
  .status-present {
    background-color: #dcfce7;
    color: #166534;
    border: 1px solid #bbf7d0;
  }
  
  .status-absent {
    background-color: #fef2f2;
    color: #991b1b;
    border: 1px solid #fecaca;
  }
  
  .emergency-warning {
    animation: pulse 2s infinite;
  }
</style>
{% endblock %}

{% block navbar %}
  {% include 'components/staff_auth_navbar.html' %}
{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    
    <!-- Header Section -->
    <div class="emergency-card rounded-lg p-6 mb-8 text-white">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold flex items-center">
            <i class="fas fa-history emergency-warning mr-3"></i>
            Emergency Attendance History
          </h1>
          <p class="mt-2 text-red-100">
            View and manage your emergency attendance records
          </p>
        </div>
        <div class="flex space-x-3">
          <a href="{% url 'staff_section:emergency_attendance' %}" 
             class="inline-flex items-center px-4 py-2 bg-white text-red-600 rounded-lg hover:bg-gray-100 transition-colors duration-200 font-medium">
            <i class="fas fa-plus mr-2"></i>
            Take Attendance
          </a>
          <a href="{% url 'staff_section:staff_dash' %}" 
             class="inline-flex items-center px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors duration-200 font-medium">
            <i class="fas fa-arrow-left mr-2"></i>
            Back to Dashboard
          </a>
        </div>
      </div>
    </div>

    <!-- Filters Section -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm mb-8">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
          <i class="fas fa-filter mr-2 text-red-600"></i>
          Filter Records
        </h3>
      </div>
      <div class="p-6">
        <form method="get" class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <!-- Date Filter -->
          <div>
            <label for="date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Filter by Date
            </label>
            <input type="date" name="date" id="date" value="{{ selected_date }}"
                   class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-red-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
          </div>

          <!-- Department Filter -->
          <div>
            <label for="department" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Filter by Department
            </label>
            <select name="department" id="department"
                    class="w-full px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-red-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
              <option value="">All Departments</option>
              {% for dept in departments %}
                <option value="{{ dept.id }}" {% if dept.id|stringformat:"s" == selected_department %}selected{% endif %}>
                  {{ dept.department_name }}
                </option>
              {% endfor %}
            </select>
          </div>

          <!-- Filter Button -->
          <div class="flex items-end">
            <button type="submit" 
                    class="w-full px-4 py-2 bg-red-600 hover:bg-red-700 text-white font-medium rounded-lg shadow-sm transition-colors duration-200">
              <i class="fas fa-search mr-2"></i>
              Apply Filters
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- Statistics Cards -->
    {% if attendances %}
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-blue-100 dark:bg-blue-900">
              <i class="fas fa-clipboard-list text-blue-600 dark:text-blue-400 text-xl"></i>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Records</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ total_records }}</p>
            </div>
          </div>
        </div>
        
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-green-100 dark:bg-green-900">
              <i class="fas fa-check-circle text-green-600 dark:text-green-400 text-xl"></i>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Present</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ present_count }}</p>
              {% if total_records > 0 %}
                <p class="text-sm text-green-600 dark:text-green-400">
                  {% widthratio present_count total_records 100 %}% of total
                </p>
              {% endif %}
            </div>
          </div>
        </div>
        
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-red-100 dark:bg-red-900">
              <i class="fas fa-times-circle text-red-600 dark:text-red-400 text-xl"></i>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Absent</p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ absent_count }}</p>
              {% if total_records > 0 %}
                <p class="text-sm text-red-600 dark:text-red-400">
                  {% widthratio absent_count total_records 100 %}% of total
                </p>
              {% endif %}
            </div>
          </div>
        </div>
      </div>
    {% endif %}

    <!-- Attendance Records Table -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
          <i class="fas fa-table mr-2 text-red-600"></i>
          Emergency Attendance Records
          {% if attendances %}
            <span class="ml-2 text-sm font-normal text-gray-500">({{ attendances|length }} records)</span>
          {% endif %}
        </h3>
      </div>

      {% if attendances %}
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead class="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Student
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Department
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Training Site
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Date
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Marked At
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Notes
                </th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {% for attendance in attendances %}
                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <div class="student-avatar flex-shrink-0 h-10 w-10 rounded-full overflow-hidden mr-3 border-2 border-gray-200 dark:border-gray-600">
                        {% if attendance.student.user.profile_photo %}
                          <img src="{{ attendance.student.user.profile_photo.url }}" 
                               alt="{{ attendance.student.user.get_full_name|default:attendance.student.user.username }}"
                               class="w-full h-full object-cover">
                        {% else %}
                          <div class="avatar-initials w-full h-full flex items-center justify-center">
                            <span class="text-white font-semibold text-xs">
                              {{ attendance.student.user.get_full_name|default:attendance.student.user.username|first|upper }}
                            </span>
                          </div>
                        {% endif %}
                      </div>
                      <div>
                        <div class="text-sm font-medium text-gray-900 dark:text-white">
                          {{ attendance.student.user.get_full_name|default:attendance.student.user.username }}
                        </div>
                        <div class="text-sm text-gray-500 dark:text-gray-400">
                          ID: {{ attendance.student.student_id }}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900 dark:text-white">
                      {{ attendance.department.department_name }}
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900 dark:text-white">
                      {% if attendance.training_site %}
                        {{ attendance.training_site.name }}
                      {% else %}
                        <span class="text-gray-400">Not specified</span>
                      {% endif %}
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900 dark:text-white">
                      {{ attendance.date|date:"M d, Y" }}
                    </div>
                    <div class="text-xs text-gray-500 dark:text-gray-400">
                      {{ attendance.date|date:"l" }}
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <span class="status-badge px-2 py-1 text-xs font-semibold rounded-full {% if attendance.status == 'present' %}status-present{% else %}status-absent{% endif %}">
                      {% if attendance.status == 'present' %}
                        <i class="fas fa-check mr-1"></i>Present
                      {% else %}
                        <i class="fas fa-times mr-1"></i>Absent
                      {% endif %}
                    </span>
                    {% if attendance.is_emergency %}
                      <div class="mt-1">
                        <span class="inline-flex items-center px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full">
                          <i class="fas fa-exclamation-triangle mr-1"></i>Emergency
                        </span>
                      </div>
                    {% endif %}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="text-sm text-gray-900 dark:text-white">
                      {{ attendance.marked_at|date:"M d, Y" }}
                    </div>
                    <div class="text-xs text-gray-500 dark:text-gray-400">
                      {{ attendance.marked_at|time:"g:i A" }}
                    </div>
                  </td>
                  <td class="px-6 py-4">
                    {% if attendance.notes %}
                      <div class="text-sm text-gray-900 dark:text-white max-w-xs truncate" title="{{ attendance.notes }}">
                        {{ attendance.notes }}
                      </div>
                    {% else %}
                      <span class="text-gray-400 text-sm">No notes</span>
                    {% endif %}
                  </td>
                </tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      {% else %}
        <div class="text-center py-12">
          <i class="fas fa-clipboard-list text-gray-400 text-6xl mb-4"></i>
          <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Emergency Attendance Records</h3>
          <p class="text-gray-500 dark:text-gray-400 mb-6">
            {% if selected_date or selected_department %}
              No records found for the selected filters. Try adjusting your search criteria.
            {% else %}
              You haven't taken any emergency attendance yet.
            {% endif %}
          </p>
          <a href="{% url 'staff_section:emergency_attendance' %}" 
             class="inline-flex items-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white font-medium rounded-lg shadow-sm transition-colors duration-200">
            <i class="fas fa-plus mr-2"></i>
            Take Emergency Attendance
          </a>
        </div>
      {% endif %}
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-submit form when filters change
    const dateInput = document.getElementById('date');
    const departmentSelect = document.getElementById('department');
    
    if (dateInput) {
        dateInput.addEventListener('change', function() {
            this.form.submit();
        });
    }
    
    if (departmentSelect) {
        departmentSelect.addEventListener('change', function() {
            this.form.submit();
        });
    }
});
</script>
{% endblock %}
