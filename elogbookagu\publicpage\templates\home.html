{% extends 'base.html' %}

{% block title %}
  Home Page
{% endblock %}

{% block navbar %}
  {% include './components/public_navbar.html' %}
{% endblock %}

{% block extra_head %}
  <!-- Optimized CSS: Moved styles to head for better performance -->
  <style>
    /* Animation keyframes */
    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translateY(20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    @keyframes fadeInLeft {
      from {
        opacity: 0;
        transform: translateX(-20px);
      }
      to {
        opacity: 1;
        transform: translateX(0);
      }
    }

    @keyframes fadeInRight {
      from {
        opacity: 0;
        transform: translateX(20px);
      }
      to {
        opacity: 1;
        transform: translateX(0);
      }
    }

    @keyframes fadeInUp {
      from {
        opacity: 0;
        transform: translateY(20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    @keyframes float {
      0% {
        transform: translateY(0px);
      }
      50% {
        transform: translateY(-10px);
      }
      100% {
        transform: translateY(0px);
      }
    }

    @keyframes pulse {
      0% {
        transform: scale(1);
      }
      50% {
        transform: scale(1.05);
      }
      100% {
        transform: scale(1);
      }
    }

    /* Animation classes */
    .animate-fade-in {
      animation: fadeIn 0.8s ease-out forwards;
    }
    .animate-fade-in-left {
      animation: fadeInLeft 0.8s ease-out forwards;
    }
    .animate-fade-in-right {
      animation: fadeInRight 0.8s ease-out forwards;
    }
    .animate-fade-in-up {
      animation: fadeInUp 0.8s ease-out forwards;
    }
    .animate-float {
      animation: float 6s ease-in-out infinite;
    }
    .animate-pulse {
      animation: pulse 3s ease-in-out infinite;
    }

    /* Delay classes */
    .delay-100 {
      animation-delay: 0.1s;
    }
    .delay-200 {
      animation-delay: 0.2s;
    }
    .delay-300 {
      animation-delay: 0.3s;
    }
    .delay-400 {
      animation-delay: 0.4s;
    }
    .delay-500 {
      animation-delay: 0.5s;
    }

    /* Reduce motion for accessibility */
    @media (prefers-reduced-motion: reduce) {
      .animate-fade-in,
      .animate-fade-in-left,
      .animate-fade-in-right,
      .animate-fade-in-up,
      .animate-float,
      .animate-pulse {
        animation: none !important;
        opacity: 1 !important;
        transform: none !important;
      }
    }

    /* Optimize image loading */
    .lazy-image {
      transition: opacity 0.3s ease;
      opacity: 0;
    }

    .lazy-image.loaded {
      opacity: 1;
    }

    /* Custom styles */
#features, #gallery, #testimonials {
  display: none !important;
}
    .hero-shape {
      position: absolute;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.1);
      z-index: 0;
    }

    .service-card:hover .service-icon {
      transform: translateY(-5px);
    }

    .service-icon {
      transition: transform 0.3s ease;
    }

    .stat-card {
      overflow: hidden;
      position: relative;
    }

    .stat-card::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0) 70%);
      opacity: 0;
      transition: opacity 0.5s ease;
    }

    .stat-card:hover::before {
      opacity: 1;
    }
  </style>

  <!-- Preload hero image -->
  <link rel="preload" as="image" href="https://images.unsplash.com/photo-1576091160550-2173dba999ef?q=80&w=2070" fetchpriority="high" />
{% endblock %}

{% block content_container %}
  <!-- Enhanced Hero Section with Visual Elements -->
  <section class="relative bg-gradient-to-r from-blue-600 to-purple-700 dark:from-gray-800 dark:to-gray-900 py-24 md:py-32 w-full overflow-hidden">
    <!-- Decorative shapes -->
    <div class="hero-shape w-64 h-64 top-0 left-0 opacity-20"></div>
    <div class="hero-shape w-96 h-96 bottom-0 right-0 opacity-10"></div>
    <div class="hero-shape w-48 h-48 top-1/4 right-1/4 opacity-15"></div>

    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-10">
      <svg class="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
        <defs>
          <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
            <path d="M 10 0 L 0 0 0 10" fill="none" stroke="white" stroke-width="0.5" />
          </pattern>
        </defs>
        <rect width="100" height="100" fill="url(#grid)" />
      </svg>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
        <div class="text-center md:text-left">
          <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold text-white animate-fade-in leading-tight">Welcome to <span class="text-yellow-300">AguELogBook</span></h1>
          <p class="mt-6 text-xl text-white opacity-90 animate-fade-in delay-100 leading-relaxed">Revolutionizing Medical Education at Medical University through innovative technology and collaborative learning.</p>
          <div class="mt-8 flex flex-wrap gap-4 justify-center md:justify-start animate-fade-in delay-200">
            <a href="{% url 'login' %}" class="px-8 py-4 bg-white text-blue-600 dark:bg-gray-900 dark:text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition duration-300 transform hover:scale-105"><i class="fas fa-sign-in-alt mr-2"></i> Get Started</a>
            <a href="#features" class="px-8 py-4 bg-transparent text-white border-2 border-white font-medium rounded-lg hover:bg-opacity-10 hover:shadow-xl transition duration-300 transform hover:scale-105"><i class="fas fa-info-circle mr-2"></i> Learn More</a>
          </div>

          <!-- Trust indicators -->
          <div class="mt-12 flex flex-wrap items-center justify-center md:justify-start gap-6 animate-fade-in delay-300">
            <div class="flex items-center">
              <div class="bg-white bg-opacity-20 p-2 rounded-full">
                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                </svg>
              </div>
              <span class="ml-2 text-white text-sm">Secure Platform</span>
            </div>
            <!-- <div class="flex items-center">
              <div class="bg-white bg-opacity-20 p-2 rounded-full">
                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z"></path>
                </svg>
              </div>
              <span class="ml-2 text-white text-sm">5,000+ Users</span>
            </div>  -->
            <div class="flex items-center">
              <div class="bg-white bg-opacity-20 p-2 rounded-full">
                <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z" clip-rule="evenodd"></path>
                </svg>
              </div>
              <span class="ml-2 text-white text-sm">Continuous Growth</span>
            </div>
          </div>
        </div>

        <!-- Hero Image -->
        <div class="hidden md:block animate-float">
          <div class="relative">
            <img src="https://images.unsplash.com/photo-1576091160550-2173dba999ef?q=80&w=2070" alt="Medical Education" class="rounded-lg shadow-2xl w-full object-cover h-auto" loading="eager" />
            <div class="absolute -bottom-4 -right-4 bg-white dark:bg-gray-800 p-4 rounded-lg shadow-lg animate-pulse">
              <div class="flex items-center">
                <div class="bg-green-500 p-2 rounded-full">
                  <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                  </svg>
                </div>
                <div class="ml-3">
                  <p class="text-sm font-medium text-gray-900 dark:text-white">Trusted by Medical Professionals</p>
                  <p class="text-xs text-gray-500 dark:text-gray-400">Across the Middle East</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Statistics Section -->
  <section id="stats" class="py-16 bg-white dark:bg-gray-900 w-full">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-2 md:grid-cols-4 gap-6 md:gap-8">
        <!-- Stat 1 -->
        <div class="stat-card bg-blue-50 dark:bg-blue-900/20 p-6 rounded-xl border border-blue-100 dark:border-blue-800 text-center transform transition-all duration-300 hover:-translate-y-1 hover:shadow-lg">
          <div class="inline-flex items-center justify-center w-12 h-12 bg-blue-100 dark:bg-blue-800 rounded-full mb-4 mx-auto">
            <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="currentColor" viewBox="0 0 20 20">
              <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z"></path>
            </svg>
          </div>
          <div class="text-3xl md:text-4xl font-bold text-blue-600 dark:text-blue-400">{{ active_users }}</div>
          <div class="text-sm text-gray-600 dark:text-gray-300 mt-1">Active Users</div>
          <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
            <span title="Doctors">👨‍⚕️ {{ doctor_count }}</span> •
            <span title="Staff">👩‍💼 {{ staff_count }}</span> •
            <span title="Students">👨‍🎓 {{ student_count }}</span> •
            <span title="Admins">👨‍💻 {{ admin_count }}</span>
          </div>
        </div>

        <!-- Stat 2 -->
        <div class="stat-card bg-purple-50 dark:bg-purple-900/20 p-6 rounded-xl border border-purple-100 dark:border-purple-800 text-center transform transition-all duration-300 hover:-translate-y-1 hover:shadow-lg">
          <div class="inline-flex items-center justify-center w-12 h-12 bg-purple-100 dark:bg-purple-800 rounded-full mb-4 mx-auto">
            <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a1 1 0 110 2h-3a1 1 0 01-1-1v-2a1 1 0 00-1-1H9a1 1 0 00-1 1v2a1 1 0 01-1 1H4a1 1 0 110-2V4zm3 1h2v2H7V5zm2 4H7v2h2V9zm2-4h2v2h-2V5zm2 4h-2v2h2V9z" clip-rule="evenodd"></path>
            </svg>
          </div>
          <div class="text-3xl md:text-4xl font-bold text-purple-600 dark:text-purple-400">{{ institutions }}</div>
          <div class="text-sm text-gray-600 dark:text-gray-300 mt-1">Training sites</div>
          <div class="text-xs text-gray-500 dark:text-gray-400 mt-1"></div>
        </div>

        <!-- Stat 3 -->
        <div class="stat-card bg-green-50 dark:bg-green-900/20 p-6 rounded-xl border border-green-100 dark:border-green-800 text-center transform transition-all duration-300 hover:-translate-y-1 hover:shadow-lg">
          <div class="inline-flex items-center justify-center w-12 h-12 bg-green-100 dark:bg-green-800 rounded-full mb-4 mx-auto">
            <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9 4.804A7.968 7.968 0 005.5 4c-1.255 0-2.443.29-3.5.804v10A7.969 7.969 0 015.5 14c1.669 0 3.218.51 4.5 1.385A7.962 7.962 0 0114.5 14c1.255 0 2.443.29 3.5.804v-10A7.968 7.968 0 0014.5 4c-1.255 0-2.443.29-3.5.804V12a1 1 0 11-2 0V4.804z"></path>
            </svg>
          </div>
          <div class="text-3xl md:text-4xl font-bold text-green-600 dark:text-green-400">{{ total_logs }}</div>
          <div class="text-sm text-gray-600 dark:text-gray-300 mt-1">Total Logs</div>
          <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">Student log entries submitted</div>
        </div>

        <!-- Stat 4 -->
        <div class="stat-card bg-red-50 dark:bg-red-900/20 p-6 rounded-xl border border-red-100 dark:border-red-800 text-center transform transition-all duration-300 hover:-translate-y-1 hover:shadow-lg">
          <div class="inline-flex items-center justify-center w-12 h-12 bg-red-100 dark:bg-red-800 rounded-full mb-4 mx-auto">
            <svg class="w-6 h-6 text-red-600 dark:text-red-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z" clip-rule="evenodd"></path>
            </svg>
          </div>
          <div class="text-3xl md:text-4xl font-bold text-red-600 dark:text-red-400">{{ support_available }}</div>
          <div class="text-sm text-gray-600 dark:text-gray-300 mt-1">Support Available</div>
        </div>
      </div>
    </div>
  </section>

  {% block content %}
    <!-- Enhanced Services Section with Feature Cards -->
    <section id="features" class="py-20 w-full bg-gray-50 dark:bg-gray-800">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white relative inline-block animate-fade-in">
            Our Services
            <span class="absolute -bottom-2 left-0 w-full h-1 bg-blue-500 rounded-full"></span>
          </h2>
          <p class="mt-6 text-gray-600 dark:text-gray-300 max-w-3xl mx-auto animate-fade-in delay-100">Discover how MedLogBook can transform your medical education experience with our comprehensive suite of tools and services.</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 md:gap-10">
          <!-- Service Card 1 -->
          <div class="service-card bg-white dark:bg-gray-900 rounded-xl shadow-lg overflow-hidden transition-all duration-300 transform hover:-translate-y-2 hover:shadow-xl animate-fade-in-up delay-100 group">
            <div class="relative">
              <img src="https://images.unsplash.com/photo-1631217868264-e5b90bb7e133?q=80&w=2091" alt="Medical Records" class="w-full h-56 object-cover transition-transform duration-500 group-hover:scale-105" loading="lazy" />
              <div class="absolute top-4 left-4 bg-blue-600 text-white text-xs font-bold px-3 py-1 rounded-full">SECURE</div>
            </div>
            <div class="p-8">
              <div class="service-icon inline-flex items-center justify-center w-14 h-14 bg-blue-100 dark:bg-blue-900/30 rounded-full mb-5 text-blue-600 dark:text-blue-400 transition-all duration-300">
                <svg class="w-7 h-7" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path>
                </svg>
              </div>
              <h3 class="text-2xl font-bold mb-3 text-gray-900 dark:text-white">Medical Records</h3>
              <p class="text-gray-600 dark:text-gray-300 mb-6">Efficiently manage and access patient records with our secure platform. Keep all your clinical data organized and accessible.</p>
              <div class="flex flex-col space-y-3">
                <div class="flex items-center">
                  <svg class="w-5 h-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                  </svg>
                  <span class="text-sm text-gray-600 dark:text-gray-300">Secure data storage</span>
                </div>
                <div class="flex items-center">
                  <svg class="w-5 h-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                  </svg>
                  <span class="text-sm text-gray-600 dark:text-gray-300">Easy search functionality</span>
                </div>
                <div class="flex items-center">
                  <svg class="w-5 h-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                  </svg>
                  <span class="text-sm text-gray-600 dark:text-gray-300">Comprehensive history</span>
                </div>
              </div>
              <a href="#" class="mt-8 inline-flex items-center text-blue-600 dark:text-blue-400 font-medium hover:text-blue-800 dark:hover:text-blue-300 transition-colors">
                Learn More<svg class="w-5 h-5 ml-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
              </a>
            </div>
          </div>

          <!-- Service Card 2 -->
          <div class="service-card bg-white dark:bg-gray-900 rounded-xl shadow-lg overflow-hidden transition-all duration-300 transform hover:-translate-y-2 hover:shadow-xl animate-fade-in-up delay-200 group">
            <div class="relative">
              <img src="https://images.unsplash.com/photo-1581594693702-fbdc51b2763b?q=80&w=2070" alt="Research Tools" class="w-full h-56 object-cover transition-transform duration-500 group-hover:scale-105" loading="lazy" />
              <div class="absolute top-4 left-4 bg-purple-600 text-white text-xs font-bold px-3 py-1 rounded-full">COLLABORATIVE</div>
            </div>
            <div class="p-8">
              <div class="service-icon inline-flex items-center justify-center w-14 h-14 bg-purple-100 dark:bg-purple-900/30 rounded-full mb-5 text-purple-600 dark:text-purple-400 transition-all duration-300">
                <svg class="w-7 h-7" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"></path>
                </svg>
              </div>
              <h3 class="text-2xl font-bold mb-3 text-gray-900 dark:text-white">Research Tools</h3>
              <p class="text-gray-600 dark:text-gray-300 mb-6">Access cutting-edge tools for medical research and collaboration. Connect with peers and share insights seamlessly.</p>
              <div class="flex flex-col space-y-3">
                <div class="flex items-center">
                  <svg class="w-5 h-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                  </svg>
                  <span class="text-sm text-gray-600 dark:text-gray-300">Advanced analytics</span>
                </div>
                <div class="flex items-center">
                  <svg class="w-5 h-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                  </svg>
                  <span class="text-sm text-gray-600 dark:text-gray-300">Collaboration features</span>
                </div>
                <div class="flex items-center">
                  <svg class="w-5 h-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                  </svg>
                  <span class="text-sm text-gray-600 dark:text-gray-300">Data visualization</span>
                </div>
              </div>
              <a href="#" class="mt-8 inline-flex items-center text-purple-600 dark:text-purple-400 font-medium hover:text-purple-800 dark:hover:text-purple-300 transition-colors">
                Learn More<svg class="w-5 h-5 ml-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
              </a>
            </div>
          </div>

          <!-- Service Card 3 -->
          <div class="service-card bg-white dark:bg-gray-900 rounded-xl shadow-lg overflow-hidden transition-all duration-300 transform hover:-translate-y-2 hover:shadow-xl animate-fade-in-up delay-300 group">
            <div class="relative">
              <img src="https://images.unsplash.com/photo-1576091160399-112ba8d25d1d?q=80&w=2070" alt="Learning Resources" class="w-full h-56 object-cover transition-transform duration-500 group-hover:scale-105" loading="lazy" />
              <div class="absolute top-4 left-4 bg-green-600 text-white text-xs font-bold px-3 py-1 rounded-full">COMPREHENSIVE</div>
            </div>
            <div class="p-8">
              <div class="service-icon inline-flex items-center justify-center w-14 h-14 bg-green-100 dark:bg-green-900/30 rounded-full mb-5 text-green-600 dark:text-green-400 transition-all duration-300">
                <svg class="w-7 h-7" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 4.804A7.968 7.968 0 005.5 4c-1.255 0-2.443.29-3.5.804v10A7.969 7.969 0 015.5 14c1.669 0 3.218.51 4.5 1.385A7.962 7.962 0 0114.5 14c1.255 0 2.443.29 3.5.804v-10A7.968 7.968 0 0014.5 4c-1.255 0-2.443.29-3.5.804V12a1 1 0 11-2 0V4.804z"></path>
                </svg>
              </div>
              <h3 class="text-2xl font-bold mb-3 text-gray-900 dark:text-white">Learning Resources</h3>
              <p class="text-gray-600 dark:text-gray-300 mb-6">Explore a vast library of medical textbooks, journals, and case studies. Stay updated with the latest medical knowledge.</p>
              <div class="flex flex-col space-y-3">
                <div class="flex items-center">
                  <svg class="w-5 h-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                  </svg>
                  <span class="text-sm text-gray-600 dark:text-gray-300">Extensive library</span>
                </div>
                <div class="flex items-center">
                  <svg class="w-5 h-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                  </svg>
                  <span class="text-sm text-gray-600 dark:text-gray-300">Regular updates</span>
                </div>
                <div class="flex items-center">
                  <svg class="w-5 h-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                  </svg>
                  <span class="text-sm text-gray-600 dark:text-gray-300">Downloadable content</span>
                </div>
              </div>
              <a href="#" class="mt-8 inline-flex items-center text-green-600 dark:text-green-400 font-medium hover:text-green-800 dark:hover:text-green-300 transition-colors">
                Learn More<svg class="w-5 h-5 ml-1" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>

   

    <!-- Enhanced CTA Section with Visual Elements -->
    <section class="py-20 w-full relative overflow-hidden">
      <div class="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-700 dark:from-blue-900 dark:to-purple-900"></div>

      <!-- Decorative Elements -->
     <!--  <div class="absolute top-0 left-0 w-full h-full overflow-hidden">
        <svg class="absolute top-0 left-0 transform translate-y-[-30%] opacity-10" width="800" height="800" viewBox="0 0 800 800" fill="none" xmlns="http://www.w3.org/2000/svg">
          <circle cx="400" cy="400" r="400" fill="white" />
        </svg>
        <svg class="absolute bottom-0 right-0 transform translate-x-[30%] translate-y-[30%] opacity-10" width="800" height="800" viewBox="0 0 800 800" fill="none" xmlns="http://www.w3.org/2000/svg">
          <circle cx="400" cy="400" r="400" fill="white" />
        </svg>
      </div>

     <!--  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 relative z-10">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
          <div class="text-center md:text-left">
            <h2 class="text-3xl md:text-4xl font-bold text-white mb-6 animate-fade-in">Ready to Transform Your Medical Education?</h2>
            <p class="text-white text-opacity-90 mb-8 text-lg max-w-xl animate-fade-in delay-100">Join thousands of medical professionals using MedLogBook to streamline their education and practice. Get started today and experience the difference.</p>
            <div class="flex flex-col sm:flex-row md:justify-start justify-center gap-4 animate-fade-in delay-200">
              <a href="{% url 'login' %}" class="px-8 py-4 bg-white text-blue-600 font-medium rounded-lg shadow-lg hover:shadow-xl transition duration-300 transform hover:scale-105"><i class="fas fa-sign-in-alt mr-2"></i> Sign In</a>
              <a href="#features" class="px-8 py-4 bg-transparent text-white border-2 border-white font-medium rounded-lg hover:bg-opacity-10 hover:shadow-xl transition duration-300 transform hover:scale-105"><i class="fas fa-info-circle mr-2"></i> Learn More</a>
            </div>

            <!-- Trust Indicators -->
            <!--  <div class="mt-10 flex flex-wrap items-center justify-center md:justify-start gap-6 animate-fade-in delay-300">
              <div class="flex items-center">
                <div class="bg-white bg-opacity-20 p-2 rounded-full">
                  <i class="fas fa-shield-alt text-white"></i>
                </div>
                <span class="ml-2 text-white text-sm">Secure & Private</span>
              </div>
              <div class="flex items-center">
                <div class="bg-white bg-opacity-20 p-2 rounded-full">
                  <i class="fas fa-sync text-white"></i>
                </div>
                <span class="ml-2 text-white text-sm">Regular Updates</span>
              </div>
              <div class="flex items-center">
                <div class="bg-white bg-opacity-20 p-2 rounded-full">
                  <i class="fas fa-headset text-white"></i>
                </div>
                <span class="ml-2 text-white text-sm">24/7 Support</span>
              </div>
            </div>
          </div>

          CTA Image -->
        <!--  <div class="hidden md:block animate-float">
            <div class="relative">
              <img src="https://images.unsplash.com/photo-1576091160550-2173dba999ef?q=80&w=2070" alt="Medical Education" class="rounded-lg shadow-2xl w-full object-cover h-auto transform rotate-2" loading="lazy" />
              <div class="absolute -bottom-4 -left-4 bg-white dark:bg-gray-800 p-4 rounded-lg shadow-lg">
                <div class="flex items-center">
                  <div class="bg-green-500 p-2 rounded-full">
                    <i class="fas fa-graduation-cap text-white"></i>
                  </div>
                  <div class="ml-3">
                    <p class="text-sm font-medium text-gray-900 dark:text-white">Start Learning Today</p>
                    <p class="text-xs text-gray-500 dark:text-gray-400">Join 5,000+ Medical Students</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section> -->
  {% endblock %}
{% endblock %}

{% block extra_scripts %}
  <!-- Optimized JavaScript for image loading -->
  <script>
    document.addEventListener('DOMContentLoaded', function () {
      // Intersection Observer for lazy loading images
      if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver(
          (entries, observer) => {
            entries.forEach((entry) => {
              if (entry.isIntersecting) {
                const img = entry.target
                img.classList.add('loaded')
                observer.unobserve(img)
              }
            })
          },
          { rootMargin: '50px 0px', threshold: 0.1 }
        )

        // Observe all images with lazy-image class
        document.querySelectorAll('.lazy-image').forEach((img) => {
          imageObserver.observe(img)
        })
      } else {
        // Fallback for browsers that don't support Intersection Observer
        document.querySelectorAll('.lazy-image').forEach((img) => {
          img.classList.add('loaded')
        })
      }
    })
  </script>
{% endblock %}