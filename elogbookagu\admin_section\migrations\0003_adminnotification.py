# Generated by Django 5.1.4 on 2025-04-14 10:47

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('admin_section', '0002_department_logyear_alter_activitytype_department_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AdminNotification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=100)),
                ('message', models.TextField()),
                ('support_ticket_type', models.CharField(choices=[('student', 'Student Support'), ('doctor', 'Doctor Support'), ('staff', 'Staff Support')], max_length=20)),
                ('ticket_id', models.IntegerField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('is_read', models.<PERSON><PERSON>an<PERSON>ield(default=False)),
                ('recipient', models.ForeignKey(limit_choices_to={'role': 'admin'}, on_delete=django.db.models.deletion.CASCADE, related_name='admin_notifications', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Admin Notification',
                'verbose_name_plural': 'Admin Notifications',
                'ordering': ['-created_at'],
            },
        ),
    ]
