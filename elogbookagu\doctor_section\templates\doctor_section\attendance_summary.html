{% extends 'base.html' %}
{% load static %}

{% block title %}Attendance Summary{% endblock %}

{% block extra_head %}
<style>
  .summary-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }
  
  .stat-card {
    transition: all 0.3s ease;
  }
  
  .stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
  
  .progress-bar {
    height: 8px;
    border-radius: 4px;
    overflow: hidden;
  }
  
  .progress-fill {
    height: 100%;
    transition: width 0.3s ease;
  }
</style>
{% endblock %}

{% block navbar %}
  {% include 'components/doc_auth_navbar.html' %}
{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    
    <!-- Header Section -->
    <div class="summary-card rounded-lg p-6 mb-8 text-white">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold">Attendance Summary</h1>
          <p class="mt-2 text-blue-100">
            Overview of attendance statistics and performance metrics
          </p>
        </div>
        <div class="flex space-x-3">
          <a href="{% url 'doctor_section:take_attendance' %}" 
             class="inline-flex items-center px-4 py-2 bg-white text-blue-600 rounded-lg hover:bg-gray-100 transition-colors duration-200 font-medium">
            <i class="fas fa-clipboard-check mr-2"></i>
            Take Attendance
          </a>
          <a href="{% url 'doctor_section:attendance_history' %}" 
             class="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200 font-medium">
            <i class="fas fa-history mr-2"></i>
            View History
          </a>
        </div>
      </div>
    </div>

    <!-- Overall Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
      <div class="stat-card bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-blue-100 dark:bg-blue-900">
            <i class="fas fa-clipboard-list text-blue-600 dark:text-blue-400 text-2xl"></i>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Attendance Records</p>
            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ total_attendances }}</p>
          </div>
        </div>
      </div>
      
      <div class="stat-card bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-green-100 dark:bg-green-900">
            <i class="fas fa-check-circle text-green-600 dark:text-green-400 text-2xl"></i>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Students Present</p>
            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ present_count }}</p>
            {% if total_attendances > 0 %}
              <p class="text-sm text-green-600 dark:text-green-400">
                {{ present_count|floatformat:0 }}/{{ total_attendances }} 
                ({% widthratio present_count total_attendances 100 %}%)
              </p>
            {% endif %}
          </div>
        </div>
      </div>
      
      <div class="stat-card bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
        <div class="flex items-center">
          <div class="p-3 rounded-full bg-red-100 dark:bg-red-900">
            <i class="fas fa-times-circle text-red-600 dark:text-red-400 text-2xl"></i>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Students Absent</p>
            <p class="text-3xl font-bold text-gray-900 dark:text-white">{{ absent_count }}</p>
            {% if total_attendances > 0 %}
              <p class="text-sm text-red-600 dark:text-red-400">
                {{ absent_count|floatformat:0 }}/{{ total_attendances }} 
                ({% widthratio absent_count total_attendances 100 %}%)
              </p>
            {% endif %}
          </div>
        </div>
      </div>
    </div>

    <!-- Attendance by Training Site -->
    {% if training_sites_stats %}
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm mb-8">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
            <i class="fas fa-building mr-2 text-blue-600"></i>
            Attendance by Training Site
          </h3>
        </div>
        <div class="p-6">
          <div class="space-y-6">
            {% for site_stat in training_sites_stats %}
              <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                <div class="flex items-center justify-between mb-3">
                  <h4 class="text-lg font-medium text-gray-900 dark:text-white">
                    {{ site_stat.training_site.name }}
                  </h4>
                  <span class="text-sm text-gray-500 dark:text-gray-400">
                    Total: {{ site_stat.total }} records
                  </span>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                  <div class="text-center">
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ site_stat.total }}</p>
                    <p class="text-sm text-gray-500 dark:text-gray-400">Total Records</p>
                  </div>
                  <div class="text-center">
                    <p class="text-2xl font-bold text-green-600 dark:text-green-400">{{ site_stat.present }}</p>
                    <p class="text-sm text-gray-500 dark:text-gray-400">Present</p>
                  </div>
                  <div class="text-center">
                    <p class="text-2xl font-bold text-red-600 dark:text-red-400">{{ site_stat.absent }}</p>
                    <p class="text-sm text-gray-500 dark:text-gray-400">Absent</p>
                  </div>
                </div>

                <!-- Progress Bar -->
                {% if site_stat.total > 0 %}
                  <div class="progress-bar bg-gray-200 dark:bg-gray-700">
                    <div class="progress-fill bg-green-500" 
                         style="width: {% widthratio site_stat.present site_stat.total 100 %}%"></div>
                  </div>
                  <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
                    <span>{% widthratio site_stat.present site_stat.total 100 %}% Present</span>
                    <span>{% widthratio site_stat.absent site_stat.total 100 %}% Absent</span>
                  </div>
                {% endif %}
              </div>
            {% endfor %}
          </div>
        </div>
      </div>
    {% endif %}

    <!-- Quick Actions -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
          <i class="fas fa-bolt mr-2 text-blue-600"></i>
          Quick Actions
        </h3>
      </div>
      <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <a href="{% url 'doctor_section:take_attendance' %}" 
             class="flex items-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
            <div class="p-2 rounded-full bg-blue-100 dark:bg-blue-900 mr-3">
              <i class="fas fa-clipboard-check text-blue-600 dark:text-blue-400"></i>
            </div>
            <div>
              <p class="font-medium text-gray-900 dark:text-white">Take Attendance</p>
              <p class="text-sm text-gray-500 dark:text-gray-400">Mark today's attendance</p>
            </div>
          </a>

          <a href="{% url 'doctor_section:attendance_history' %}" 
             class="flex items-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
            <div class="p-2 rounded-full bg-green-100 dark:bg-green-900 mr-3">
              <i class="fas fa-history text-green-600 dark:text-green-400"></i>
            </div>
            <div>
              <p class="font-medium text-gray-900 dark:text-white">View History</p>
              <p class="text-sm text-gray-500 dark:text-gray-400">Browse past records</p>
            </div>
          </a>

          <a href="{% url 'doctor_section:doctor_reviews' %}" 
             class="flex items-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
            <div class="p-2 rounded-full bg-purple-100 dark:bg-purple-900 mr-3">
              <i class="fas fa-book-reader text-purple-600 dark:text-purple-400"></i>
            </div>
            <div>
              <p class="font-medium text-gray-900 dark:text-white">Review Logs</p>
              <p class="text-sm text-gray-500 dark:text-gray-400">Review student logs</p>
            </div>
          </a>

          <a href="{% url 'doctor_section:doctor_dash' %}" 
             class="flex items-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-200">
            <div class="p-2 rounded-full bg-orange-100 dark:bg-orange-900 mr-3">
              <i class="fas fa-chart-line text-orange-600 dark:text-orange-400"></i>
            </div>
            <div>
              <p class="font-medium text-gray-900 dark:text-white">Dashboard</p>
              <p class="text-sm text-gray-500 dark:text-gray-400">Go to main dashboard</p>
            </div>
          </a>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    {% if total_attendances == 0 %}
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm mt-8">
        <div class="px-6 py-12 text-center">
          <div class="text-gray-500 dark:text-gray-400">
            <i class="fas fa-clipboard-list text-6xl mb-4"></i>
            <h3 class="text-xl font-medium mb-2">No Attendance Records Yet</h3>
            <p class="text-sm mb-6">Start taking attendance to see statistics and summaries here.</p>
            <a href="{% url 'doctor_section:take_attendance' %}" 
               class="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors duration-200">
              <i class="fas fa-clipboard-check mr-2"></i>
              Take Your First Attendance
            </a>
          </div>
        </div>
      </div>
    {% endif %}
  </div>
</div>
{% endblock %}
