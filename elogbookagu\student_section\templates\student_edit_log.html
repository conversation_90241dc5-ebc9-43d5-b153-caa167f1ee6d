{% extends 'base.html' %}
{% load static %}

{% block title %}
  Edit Log Entry
{% endblock %}

{% block navbar %}
  {% include './components/student_auth_navbar.html' %}
{% endblock %}

{% block content %}
  <div class="text-center p-10 mt-10">
    <div class="max-w-3xl mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
      <h2 class="text-2xl font-bold mb-6 text-gray-900 dark:text-white">Edit Log Entry</h2>

      {% if messages %}
        <div class="max-w-3xl mx-auto mb-6">
          {% for message in messages %}
            <div class="p-4 rounded-lg {% if message.tags == 'success' %}
                bg-green-100 text-green-700
              {% else %}
                bg-red-100 text-red-700
              {% endif %}">{{ message }}</div>
          {% endfor %}
        </div>
      {% endif %}

      <!-- Form Errors -->
      {% if form.errors %}
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6" role="alert">
          <strong class="font-bold">Please correct the following errors:</strong>
          <ul class="list-disc list-inside">
            {% for field in form %}
              {% for error in field.errors %}
                <li>{{ field.label }}: {{ error }}</li>
              {% endfor %}
            {% endfor %}
          </ul>
        </div>
      {% endif %}

      <!-- Student Info -->
      <div class="mb-6 bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <strong>Student:</strong> {{ student_name }}
          </div>
          <div>
            <strong>ID:</strong> {{ student_id }}
          </div>
          <div>
            <strong>Year:</strong> {{ year_name }}
          </div>
          <div>
            <strong>Section:</strong> {{ section_name }}
          </div>
          <div>
            <strong>Group:</strong> {{ group_name }}
          </div>
        </div>
      </div>

      <!-- Main Form -->
      <form method="post" class="space-y-6" id="elog-form">
        {% csrf_token %}

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label for="{{ form.department.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Department</label>
            {{ form.department }}
          </div>

          <div>
            <label for="{{ form.activity_type.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Activity Type</label>
            {{ form.activity_type }}
          </div>

          <div>
            <label for="{{ form.core_diagnosis.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Core Diagnosis</label>
            {{ form.core_diagnosis }}
          </div>

          <div>
            <label for="{{ form.tutor.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Tutor</label>
            {{ form.tutor }}
          </div>

          <div>
            <label for="{{ form.date.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Date</label>
            {{ form.date }}
          </div>

          <div>
            <label for="{{ form.patient_id.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Patient ID</label>
            {{ form.patient_id }}
          </div>

          <div>
            <label for="{{ form.training_site.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Training Site</label>
            {{ form.training_site }}
          </div>

          <div>
            <label for="{{ form.participation_type.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Participation Type</label>
            {{ form.participation_type }}
          </div>

          <div class="md:col-span-2">
            <label for="{{ form.description.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Description</label>
            {{ form.description }}
          </div>
        </div>

        <div class="flex justify-between mt-6">
          <a href="{% url 'student_section:student_final_records' %}" class="px-6 py-2 bg-gray-300 text-gray-800 rounded-lg hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 dark:bg-gray-700 dark:text-white dark:hover:bg-gray-600">
            Cancel
          </a>
          <button type="submit" class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:bg-blue-500 dark:hover:bg-blue-600">
            Update Log
          </button>
        </div>
      </form>
    </div>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function () {
      const form = document.getElementById('elog-form')
      const departmentSelect = document.getElementById('id_department')
      const activityTypeSelect = document.getElementById('id_activity_type')
      const coreDiagnosisSelect = document.getElementById('id_core_diagnosis')
      const tutorSelect = document.getElementById('id_tutor')
    
      // Reset dependent fields with a placeholder message
      const resetDependentFields = (exceptField = null) => {
        const fields = [
          { select: activityTypeSelect, text: 'Choose Department ' },
          { select: coreDiagnosisSelect, text: 'Choose Activity Type ' },
          { select: tutorSelect, text: 'Choose Department' }
        ]
    
        fields.forEach(({ select, text }) => {
          if (select !== exceptField) {
            select.innerHTML = `<option value="">${text}</option>`
            select.disabled = true
          }
        })
      }
    
      // Show loading state
      const showLoading = (selectElement, message = 'Loading...') => {
        selectElement.innerHTML = `<option value="">${message}</option>`
        selectElement.disabled = true
      }
    
      // Department change handler
      departmentSelect.addEventListener('change', async function () {
        const departmentId = this.value
        resetDependentFields()
    
        if (!departmentId) return
    
        try {
          // Show loading state
          showLoading(activityTypeSelect)
          showLoading(tutorSelect)
    
          // Fetch activity types and tutors in parallel
          const [activityTypesResponse, tutorsResponse] = await Promise.all([
            fetch(`/student_section/get-activity-types/?department=${departmentId}`, {
              headers: { 'X-Requested-With': 'XMLHttpRequest' }
            }),
            fetch(`/student_section/get-tutors/?department=${departmentId}`, {
              headers: { 'X-Requested-With': 'XMLHttpRequest' }
            })
          ])
    
          const activityTypes = await activityTypesResponse.json()
          const tutors = await tutorsResponse.json()
    
          // Update activity types
          activityTypeSelect.innerHTML = '<option value="">Choose Activity Type</option>'
          activityTypes.forEach((type) => {
            activityTypeSelect.add(new Option(type.name, type.id))
          })
          activityTypeSelect.disabled = false
    
          // Update tutors
          tutorSelect.innerHTML = '<option value="">Choose Tutor</option>'
          tutors.forEach((tutor) => {
            tutorSelect.add(new Option(tutor.name, tutor.id))
          })
          tutorSelect.disabled = false
        } catch (error) {
          console.error('Error fetching dependent data:', error)
          activityTypeSelect.innerHTML = '<option value="">error: data is not loaded</option>'
          tutorSelect.innerHTML = '<option value="">error: data is not loaded</option>'
        }
      })
    
      // Activity type change handler
      activityTypeSelect.addEventListener('change', async function () {
        const activityTypeId = this.value
        coreDiagnosisSelect.innerHTML = '<option value="">Choose Core Diagnosis</option>'
        coreDiagnosisSelect.disabled = true
    
        if (!activityTypeId) return
    
        try {
          // Show loading state
          showLoading(coreDiagnosisSelect)
    
          const response = await fetch(`/student_section/get-core-diagnosis/?activity_type=${activityTypeId}`, {
            headers: { 'X-Requested-With': 'XMLHttpRequest' }
          })
          const diagnoses = await response.json()
    
          coreDiagnosisSelect.innerHTML = '<option value="">Choose Core Diagnosis </option>'
          diagnoses.forEach((diagnosis) => {
            coreDiagnosisSelect.add(new Option(diagnosis.name, diagnosis.id))
          })
          coreDiagnosisSelect.disabled = false
        } catch (error) {
          console.error('Error fetching core diagnoses:', error)
          coreDiagnosisSelect.innerHTML = '<option value="">error: data is not loaded</option>'
        }
      })
    
      // Form submission handler
      form.addEventListener('submit', function (e) {
        console.log('Form submission attempted')
    
        const requiredFields = [
          { field: departmentSelect, name: 'Department' },
          { field: activityTypeSelect, name: 'Activity Type' },
          { field: coreDiagnosisSelect, name: 'Core Diagnosis' },
          { field: tutorSelect, name: 'Tutor' }
        ]
    
        // Debug: Log the values of all required fields
        requiredFields.forEach(({ field, name }) => {
          console.log(`${name} value:`, field.value)
        })
    
        const emptyFields = requiredFields.filter(({ field }) => !field.value).map(({ name }) => name)
    
        if (emptyFields.length > 0) {
          e.preventDefault()
          e.stopPropagation()
          alert(`Please fill in the following fields: ${emptyFields.join(', ')}`)
          console.log('Form submission prevented due to empty fields:', emptyFields)
          return false
        }
    
        console.log('Form submission allowed - all fields filled')
      })
    })
  </script>
{% endblock %}
