{% extends 'base.html' %}
{% load static %}

{% block title %}Take Attendance{% endblock %}

{% block extra_head %}
<style>
  .attendance-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }
  
  .student-card {
    transition: all 0.3s ease;
    border: 2px solid transparent;
  }
  
  .student-card:hover {
    border-color: #3b82f6;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }
  
  .attendance-radio {
    transform: scale(1.2);
    margin: 0 8px;
  }
  
  .present-selected {
    background-color: #dcfce7;
    border-color: #16a34a;
  }
  
  .absent-selected {
    background-color: #fef2f2;
    border-color: #dc2626;
  }

  .date-restriction {
    pointer-events: none;
    opacity: 0.5;
  }

  .student-avatar {
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .student-avatar:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }

  .student-card:hover .student-avatar {
    border-color: #3b82f6;
  }

  .avatar-initials {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }
</style>
{% endblock %}

{% block navbar %}
  {% include 'components/doc_auth_navbar.html' %}
{% endblock %}

{% block content %}
<div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    
    <!-- Header Section -->
    <div class="attendance-card rounded-lg p-6 mb-8 text-white">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold">Take Attendance</h1>
          <p class="mt-2 text-blue-100">
            Mark student attendance for any date within the last 30 days (can be updated multiple times)
          </p>
        </div>
        <div class="flex space-x-3">
          <a href="{% url 'doctor_section:attendance_history' %}" 
             class="inline-flex items-center px-4 py-2 bg-white text-blue-600 rounded-lg hover:bg-gray-100 transition-colors duration-200 font-medium">
            <i class="fas fa-history mr-2"></i>
            View History
          </a>
          <a href="{% url 'doctor_section:doctor_dash' %}" 
             class="inline-flex items-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200 font-medium">
            <i class="fas fa-arrow-left mr-2"></i>
            Back to Dashboard
          </a>
        </div>
      </div>
    </div>

    <!-- Selection Form -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm mb-8">
      <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
          <i class="fas fa-cog mr-2 text-blue-600"></i>
          Attendance Settings
        </h3>
      </div>
      <div class="p-6">
        <form method="post" id="attendance-form">
          {% csrf_token %}
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <!-- Training Site Selection -->
            <div>
              <label for="{{ form.training_site.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Training Site <span class="text-red-500">*</span>
              </label>
              {{ form.training_site }}
              {% if form.training_site.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.training_site.errors.0 }}</p>
              {% endif %}
            </div>

            <!-- Date Selection (Today Only) -->
            <div>
              <label for="{{ form.attendance_date.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Attendance Date <span class="text-red-500">*</span>
              </label>
              {{ form.attendance_date }}
              {% if form.attendance_date.errors %}
                <p class="mt-1 text-sm text-red-600">{{ form.attendance_date.errors.0 }}</p>
              {% endif %}
              <p class="mt-1 text-sm text-gray-500">Attendance can be taken multiple times for today's date</p>
            </div>
          </div>

          <!-- General Notes -->
          <div class="mb-6">
            <label for="{{ form.notes.id_for_label }}" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              General Notes (Optional)
            </label>
            {{ form.notes }}
            {% if form.notes.errors %}
              <p class="mt-1 text-sm text-red-600">{{ form.notes.errors.0 }}</p>
            {% endif %}
          </div>

          <!-- Load Students Button -->
          {% if not students_data %}
            <div class="text-center">
              <button type="submit" name="load_students" 
                      class="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg shadow-sm transition-colors duration-200">
                <i class="fas fa-users mr-2"></i>
                Load Students
              </button>
            </div>
          {% endif %}

          <!-- Students Attendance Section -->
          {% if students_data %}
            <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
              <div class="flex items-center justify-between mb-6">
                <h4 class="text-lg font-semibold text-gray-900 dark:text-white">
                  Student Attendance - {{ selected_training_site.name }}
                </h4>
                <div class="flex space-x-2">
                  <button type="button" id="mark-all-present" 
                          class="px-3 py-2 bg-green-100 text-green-800 rounded-md hover:bg-green-200 transition-colors duration-200 text-sm font-medium">
                    <i class="fas fa-check mr-1"></i>
                    Mark All Present
                  </button>
                  <button type="button" id="mark-all-absent" 
                          class="px-3 py-2 bg-red-100 text-red-800 rounded-md hover:bg-red-200 transition-colors duration-200 text-sm font-medium">
                    <i class="fas fa-times mr-1"></i>
                    Mark All Absent
                  </button>
                </div>
              </div>

              {% if students_data %}
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                  {% for student_data in students_data %}
                    <div class="student-card bg-white dark:bg-gray-700 rounded-lg p-4 border border-gray-200 dark:border-gray-600" 
                         data-student-id="{{ student_data.student.id }}">
                      
                      <!-- Student Info -->
                      <div class="flex items-center mb-3">
                        <div class="student-avatar w-12 h-12 rounded-full overflow-hidden mr-3 flex-shrink-0 border-2 border-gray-200 dark:border-gray-600">
                          {% if student_data.student.user.profile_photo %}
                            <img src="{{ student_data.student.user.profile_photo.url }}"
                                 alt="{{ student_data.student.user.get_full_name|default:student_data.student.user.username }}"
                                 class="w-full h-full object-cover">
                          {% else %}
                            <div class="avatar-initials w-full h-full flex items-center justify-center">
                              <span class="text-white font-semibold text-sm">
                                {{ student_data.student.user.get_full_name|default:student_data.student.user.username|first|upper }}
                              </span>
                            </div>
                          {% endif %}
                        </div>
                        <div class="flex-1">
                          <h5 class="font-medium text-gray-900 dark:text-white">
                            {{ student_data.student.user.get_full_name|default:student_data.student.user.username }}
                          </h5>
                          <p class="text-sm text-gray-500 dark:text-gray-400">
                            ID: {{ student_data.student.student_id }} | {{ student_data.group.group_name }}
                          </p>
                        </div>
                      </div>

                      <!-- Attendance Status -->
                      <div class="mb-3">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          Attendance Status
                        </label>
                        <div class="flex items-center space-x-4">
                          <label class="flex items-center cursor-pointer">
                            <input type="radio" name="student_{{ student_data.student.id }}_status" value="present" 
                                   class="attendance-radio text-green-600 focus:ring-green-500"
                                   {% if student_data.existing_attendance.status == 'present' %}checked{% endif %}>
                            <span class="ml-2 text-sm font-medium text-green-700 dark:text-green-400">Present</span>
                          </label>
                          <label class="flex items-center cursor-pointer">
                            <input type="radio" name="student_{{ student_data.student.id }}_status" value="absent" 
                                   class="attendance-radio text-red-600 focus:ring-red-500"
                                   {% if student_data.existing_attendance.status == 'absent' %}checked{% endif %}>
                            <span class="ml-2 text-sm font-medium text-red-700 dark:text-red-400">Absent</span>
                          </label>
                        </div>
                      </div>

                      <!-- Individual Notes -->
                      <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                          Notes (Optional)
                        </label>
                        <textarea name="student_{{ student_data.student.id }}_notes" 
                                  rows="2" 
                                  class="w-full px-3 py-2 text-sm rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:text-white"
                                  placeholder="Individual notes...">{% if student_data.existing_attendance %}{{ student_data.existing_attendance.notes }}{% endif %}</textarea>
                      </div>

                      <!-- Existing Attendance Indicator -->
                      {% if student_data.existing_attendance %}
                        <div class="mt-2 text-xs text-blue-600 dark:text-blue-400">
                          <i class="fas fa-info-circle mr-1"></i>
                          Previously marked as {{ student_data.existing_attendance.status }} (can be updated)
                        </div>
                      {% endif %}
                    </div>
                  {% endfor %}
                </div>

                <!-- Submit Button -->
                <div class="text-center">
                  <button type="submit" name="submit_attendance" 
                          class="inline-flex items-center px-8 py-3 bg-green-600 hover:bg-green-700 text-white font-medium rounded-lg shadow-sm transition-colors duration-200">
                    <i class="fas fa-save mr-2"></i>
                    Save Attendance
                  </button>
                </div>
              {% else %}
                <div class="text-center py-8">
                  <i class="fas fa-users text-gray-400 text-4xl mb-3"></i>
                  <p class="text-gray-500 dark:text-gray-400">No students found for the selected training site.</p>
                  <p class="text-sm text-gray-400 dark:text-gray-500 mt-1">Please check if you are mapped to this training site.</p>
                </div>
              {% endif %}
            </div>
          {% endif %}
        </form>
      </div>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Mark all present functionality
    document.getElementById('mark-all-present')?.addEventListener('click', function() {
        const presentRadios = document.querySelectorAll('input[type="radio"][value="present"]');
        presentRadios.forEach(radio => {
            radio.checked = true;
            updateCardStyle(radio);
        });
    });

    // Mark all absent functionality
    document.getElementById('mark-all-absent')?.addEventListener('click', function() {
        const absentRadios = document.querySelectorAll('input[type="radio"][value="absent"]');
        absentRadios.forEach(radio => {
            radio.checked = true;
            updateCardStyle(radio);
        });
    });

    // Update card styling based on selection
    function updateCardStyle(radio) {
        const studentCard = radio.closest('.student-card');
        const studentId = studentCard.dataset.studentId;
        const allRadios = document.querySelectorAll(`input[name="student_${studentId}_status"]`);
        
        // Remove all status classes
        studentCard.classList.remove('present-selected', 'absent-selected');
        
        // Add appropriate class based on selection
        if (radio.value === 'present' && radio.checked) {
            studentCard.classList.add('present-selected');
        } else if (radio.value === 'absent' && radio.checked) {
            studentCard.classList.add('absent-selected');
        }
    }

    // Add event listeners to all radio buttons
    document.querySelectorAll('input[type="radio"][name*="_status"]').forEach(radio => {
        radio.addEventListener('change', function() {
            updateCardStyle(this);
        });
        
        // Initialize card styling
        if (radio.checked) {
            updateCardStyle(radio);
        }
    });

    // Training site change handler
    const trainingSiteSelect = document.getElementById('{{ form.training_site.id_for_label }}');
    if (trainingSiteSelect) {
        trainingSiteSelect.addEventListener('change', function() {
            // Auto-submit form when training site changes
            if (this.value) {
                document.getElementById('attendance-form').submit();
            }
        });
    }
});
</script>
{% endblock %}
